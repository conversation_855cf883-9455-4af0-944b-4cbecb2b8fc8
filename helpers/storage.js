import lscache from 'lscache'

export function setUserBehavior (option, value, productName) {
  if ((option === 'size' || option === 'color') && productName) {
    const key = `last-${option}-by-product-name`
    let last = {}
    try {
      const cachedValue = lscache.get(key)
      if (cachedValue) {
        last = JSON.parse(cachedValue)
      }
    } catch (error) {
    }
    if (productName && value) {
      lscache.set(key, JSON.stringify({
        ...last,
        [productName]: value,
      }))
    }
  }
  lscache.set(`last-${option}`, value)
}

export function getLastUserBehavior (option, productName) {
  let lastOption = null
  if ((option === 'size' || option === 'color') && productName) {
    const key = `last-${option}-by-product-name`
    let last = {}
    try {
      const cachedValue = lscache.get(key)
      if (cachedValue) {
        last = JSON.parse(cachedValue)
      }
    } catch (error) {
    }
    lastOption = last[productName]
  }
  return lastOption || lscache.get(`last-${option}`)
}
