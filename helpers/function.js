
export {
  hex,
  sha256,
  parseCfInfo,
  getQueriesString,
  getLocation,
  isBot,
  getCurrencySymbol,
  getCurrentTimeInUTC
}

/**
 * Get current time in UTC
 * @param {number} minusSeconds - minus seconds from current time
 * @returns {string} example: 2022-11-08T00:00:00+00:00
 */
function getCurrentTimeInUTC (minusSeconds = 0) {
  const localDate = new Date()
  const utcDate = new Date(Date.UTC(
    localDate.getUTCFullYear(),
    localDate.getUTCMonth(),
    localDate.getUTCDate(),
    localDate.getUTCHours(),
    localDate.getUTCMinutes(),
    localDate.getUTCSeconds() - minusSeconds
  ))
  return utcDate.toISOString()
}

function hex (buffer) {
  let digest = ''
  const view = new DataView(buffer)
  for (let i = 0; i < view.byteLength; i += 4) {
    // We use getUint32 to reduce the number of iterations (notice the `i += 4`)
    const value = view.getUint32(i)
    // toString(16) will transform the integer into the corresponding hex string
    // but will remove any initial "0"
    const stringValue = value.toString(16)
    // One Uint32 element is 4 bytes or 8 hex chars (it would also work with 4
    // chars for Uint16 and 2 chars for Uint8)
    const padding = '00000000'
    const paddedValue = (padding + stringValue).slice(-padding.length)
    digest += paddedValue
  }

  return digest
}

function sha256 (str) {
  str = str.toLowerCase()
  // Get the string as arraybuffer.
  const buffer = new TextEncoder('utf-8').encode(str)
  return crypto.subtle.digest('SHA-256', buffer).then(function (hash) {
    return hex(hash)
  })
}

/**
     * Parse CloudFlare Data
     *
     * @param obj
     * @returns {*}
     */
function parseCfInfo (obj) {
  // remove these keys from object
  ['colo', 'fl', 'h', 'uag', 'sni', 'visit_scheme', 'hostname', 'asOrganization', 'httpProtocol', 'asn'].forEach((el) => {
    if (obj[el] !== undefined) {
      delete obj[el]
    }
  })

  if (undefined !== obj.loc) {
    obj.country = obj.loc.toUpperCase()
    delete obj.loc
  }

  return obj
}

function getQueriesString () {
  if (!window) {
    return {}
  }

  const qs = new URLSearchParams(window.top.location.search)

  if (qs.toString() === '') {
    return {}
  }

  try {
    // convert to object
    return Object.fromEntries(qs)
  } catch (e) {
    return {}
  }
}

async function getLocation () {
  const res = await fetch('https://api.geoapify.com/v1/ipinfo?&apiKey=********************************', {
    method: 'GET', // or 'PUT'
    headers: {
      'Content-Type': 'application/json'
    }
  })

  const resJson = await res.json()
  const location = resJson.location
  const result = {
    lat: location.latitude,
    lon: location.longitude
  }
  return result
}

function isBot () {
  if (process.browser) {
    // eslint-disable-next-line no-useless-escape
    return navigator?.userAgent.match(/abacho|accona|AddThis|AdsBot|ahoy|AhrefsBot|AISearchBot|alexa|altavista|anthill|appie|applebot|arale|araneo|AraybOt|ariadne|arks|aspseek|ATN_Worldwide|Atomz|baiduspider|baidu|bbot|bingbot|bing|Bjaaland|BlackWidow|BotLink|bot|boxseabot|bspider|calif|CCBot|ChinaClaw|christcrawler|CMC\/0\.01|combine|confuzzledbot|contaxe|CoolBot|cosmos|crawler|crawlpaper|crawl|curl|cusco|cyberspyder|cydralspider|dataprovider|digger|DIIbot|DotBot|downloadexpress|DragonBot|DuckDuckBot|dwcp|EasouSpider|ebiness|ecollector|elfinbot|esculapio|ESI|esther|eStyle|Ezooms|facebookexternalhit|facebook|facebot|fastcrawler|FatBot|FDSE|FELIX IDE|fetch|fido|find|Firefly|fouineur|Freecrawl|froogle|gammaSpider|gazz|gcreep|geona|Getterrobo-Plus|get|girafabot|golem|googlebot|\-google|grabber|GrabNet|griffon|Gromit|gulliver|gulper|hambot|havIndex|hotwired|htdig|HTTrack|ia_archiver|iajabot|IDBot|Informant|InfoSeek|InfoSpiders|INGRID\/0\.1|inktomi|inspectorwww|Internet Cruiser Robot|irobot|Iron33|JBot|jcrawler|Jeeves|jobo|KDD\-Explorer|KIT\-Fireball|ko_yappo_robot|label\-grabber|larbin|legs|libwww-perl|linkedin|Linkidator|linkwalker|Lockon|logo_gif_crawler|Lycos|m2e|majesticsEO|marvin|mattie|mediafox|mediapartners|MerzScope|MindCrawler|MJ12bot|mod_pagespeed|moget|Motor|msnbot|muncher|muninn|MuscatFerret|MwdSearch|NationalDirectory|naverbot|NEC\-MeshExplorer|NetcraftSurveyAgent|NetScoop|NetSeer|newscan\-online|nil|none|Nutch|ObjectsSearch|Occam|openstat.ru\/Bot|packrat|pageboy|ParaSite|patric|pegasus|perlcrawler|phpdig|piltdownman|Pimptrain|pingdom|pinterest|pjspider|PlumtreeWebAccessor|PortalBSpider|psbot|rambler|Raven|RHCS|RixBot|roadrunner|Robbie|robi|RoboCrawl|robofox|Scooter|Scrubby|Search\-AU|searchprocess|search|SemrushBot|Senrigan|seznambot|Shagseeker|sharp\-info\-agent|sift|SimBot|Site Valet|SiteSucker|skymob|SLCrawler\/2\.0|slurp|snooper|solbot|speedy|spider_monkey|SpiderBot\/1\.0|spiderline|spider|suke|tach_bw|TechBOT|TechnoratiSnoop|templeton|teoma|titin|topiclink|twitterbot|twitter|UdmSearch|Ukonline|UnwindFetchor|URL_Spider_SQL|urlck|urlresolver|Valkyrie libwww\-perl|verticrawl|Victoria|void\-bot|Voyager|VWbot_K|wapspider|WebBandit\/1\.0|webcatcher|WebCopier|WebFindBot|WebLeacher|WebMechanic|WebMoose|webquest|webreaper|webspider|webs|WebWalker|WebZip|wget|whowhere|winona|wlm|WOLP|woriobot|WWWC|XGET|xing|yahoo|YandexBot|YandexMobileBot|yandex|yeti|Zeus/i)
  }
  return false
}

function getCurrencySymbol (locale = 'en-US', currency = 'USD') {
  if (typeof locale === 'undefined' || locale === null || locale === '') {
    locale = 'en-US'
  }
  if (!currency) {
    currency = 'USD'
  }
  return (0).toLocaleString(locale, { style: 'currency', currency, minimumFractionDigits: 0, maximumFractionDigits: 0 }).replace(/\d/g, '').trim()
}
