export const getDisabledKeys = (optionList) => {
  if (!optionList || typeof optionList !== 'object') {
    return []
  }

  return Object.keys(optionList).filter((key) => {
    const value = optionList[key]
    return Array.isArray(value) && value.length === 1
  })
}

export const getVariant = (rawSelectedOptions, rawOptionList = null) => {
  if (!rawSelectedOptions) {
    return ''
  }

  const optionList = rawOptionList ? (typeof rawOptionList === 'string' ? JSON.parse(rawOptionList) : rawOptionList) : {}
  const selectedOptions = typeof rawSelectedOptions === 'string' ? JSON.parse(rawSelectedOptions) : rawSelectedOptions

  const disabledKeys = rawOptionList ? getDisabledKeys(optionList) : []
  const acceptKeys = Object.keys(rawOptionList ? optionList : selectedOptions).filter(key => !disabledKeys.includes(key))

  let variant = ''
  for (let i = 0; i < acceptKeys.length; i++) {
    const key = acceptKeys[i]
    variant += `${selectedOptions[key]}`
    if (i < acceptKeys.length - 1) {
      variant += ' / '
    }
  }
  return variant.toUpperCase()
}
