
const API_LIST = {
  // pages
  API_GET_PAGE: '/public/storefront/pages',
  // products list
  API_GET_PRODUCT: '/public/products',
  // product single
  API_GET_SINGLE_PRODUCT: '/public/campaigns',
  // checkout
  API_STRIPE_GET_INTENT_ORDER: '/public/order/stripe/get-intent-order',
  API_STRIPE_GET_INTENT_ORDER_ADDITION: '/public/order/stripe/get-intent-order-addition',
  API_CHECKOUT_LOG_EXCEPTION: '/public/order/log-checkout-exception',
  // storefront public api
  API_GET_STOREFRONT_INFO: '/public/storefront',
  // storefront public api
  API_GET_STORE_INFO: '/public/storeinfo',
  // general info
  API_GET_GENERAL_INFO: '/public/general',
  // error single images
  API_GET_ERROR_IMAGES: '/canvas/render3d',
  // filter
  API_GET_FILTERS: '/public/products/get-filter',
  // upsell
  API_GET_UPSELL: '/public/upsell',
  // subscriber
  API_GET_SUBSCRIBE: '/public/subscribe',
  // create order
  API_CREATE_ORDER: '/public/order/create',
  // get order
  API_GET_ORDER: '/public/order',
  // get order
  API_UPDATE_ORDER: '/public/order/update',
  // send contact
  API_CONTACT_FORM: '/public/storefront/contact',
  // send report
  API_REPORT_FORM: '/public/report',
  // home
  API_HOME: '/public/home',
  // promotion
  API_CUSTOMER_INFO: '/public/customers/update',
  // promotion
  API_PROMOTION: '/public/promotions',
  // bundle discount
  API_BUNDLE_DISCOUNT: '/public/bundle-discount',
  // product_stats
  API_PRODUCTS_STATS: '/public/products/stats',
  // confirm address
  API_CONFIRM_ADDRESS: '/public/customers/confirm-address',
  // faq
  API_FAQ: '/public/faq',
  // csrf
  API_CSRF: '/public/csrf-token',
  // update cart key
  API_CART_KEY: '/public/order/callback/abandoned',
  // generate SEO content
  API_GENERATE_SEO: '/public/page/custom',
  // create product review
  API_CREATE_PRODUCT_REVIEW: '/public/product-reviews/create',
  // product review
  API_PRODUCT_REVIEW: '/public/product-reviews',
  // email validation
  API_EMAIL_VALIDATION: '/public/email-validation',
  // product similar
  API_PRODUCTS_SIMILAR: '/public/products/similar',
  // email validation
  API_GET_EMAIL_BY_TOKEN: '/public/order/unsubscribe',
  // email validation
  API_UNSUBSCRIBE_EMAIL: '/public/order/unsubscribe',
  // confirm cancel order
  API_CONFIRM_CANCEL_ORDER: '/public/order/cancel',
  // get product variants
  API_GET_PRODUCT_VARIANTS: '/public/product/variants',
  API_GET_CURRENT_VARIANT: '/public/variant',

  // Distributed checkout
  API_CREATE_DISTRIBUTED_CHECKOUT: '/v2/public/order/create',
  API_UPDATE_DISTRIBUTED_CHECKOUT: '/v2/public/order/update',
  API_GET_DISTRIBUTED_CHECKOUT: '/v2/public/order',
  API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION: '/v2/public/order/log-checkout-exception',

  API_BLOG: '/public/blogs'
}

const METHOD = {
  get: 'GET',
  post: 'POST',
  put: 'PUT',
  del: 'DELETE'
}

const CURRENCY = {
  locale: 'en',
  code: 'USD',
  rate: 1
}

const USD_CODE = 'USD'
const EUR_CODE = 'EUR'
const VND_CODE = 'VND'

const COUNTRY_STATE_LIST = { US: [{ text: 'Alabama', value: 'AL' }, { text: 'Alaska', value: 'AK' }, { text: 'American Samoa', value: 'AS' }, { text: 'Arizona', value: 'AZ' }, { text: 'Arkansas', value: 'AR' }, { text: 'Baker Island', value: 'UM-81' }, { text: 'California', value: 'CA' }, { text: 'Colorado', value: 'CO' }, { text: 'Connecticut', value: 'CT' }, { text: 'Delaware', value: 'DE' }, { text: 'District of Columbia', value: 'DC' }, { text: 'Florida', value: 'FL' }, { text: 'Georgia', value: 'GA' }, { text: 'Guam', value: 'GU' }, { text: 'Hawaii', value: 'HI' }, { text: 'Howland Island', value: 'UM-84' }, { text: 'Idaho', value: 'ID' }, { text: 'Illinois', value: 'IL' }, { text: 'Indiana', value: 'IN' }, { text: 'Iowa', value: 'IA' }, { text: 'Jarvis Island', value: 'UM-86' }, { text: 'Johnston Atoll', value: 'UM-67' }, { text: 'Kansas', value: 'KS' }, { text: 'Kentucky', value: 'KY' }, { text: 'Kingman Reef', value: 'UM-89' }, { text: 'Louisiana', value: 'LA' }, { text: 'Maine', value: 'ME' }, { text: 'Maryland', value: 'MD' }, { text: 'Massachusetts', value: 'MA' }, { text: 'Michigan', value: 'MI' }, { text: 'Midway Atoll', value: 'UM-71' }, { text: 'Minnesota', value: 'MN' }, { text: 'Mississippi', value: 'MS' }, { text: 'Missouri', value: 'MO' }, { text: 'Montana', value: 'MT' }, { text: 'Navassa Island', value: 'UM-76' }, { text: 'Nebraska', value: 'NE' }, { text: 'Nevada', value: 'NV' }, { text: 'New Hampshire', value: 'NH' }, { text: 'New Jersey', value: 'NJ' }, { text: 'New Mexico', value: 'NM' }, { text: 'New York', value: 'NY' }, { text: 'North Carolina', value: 'NC' }, { text: 'North Dakota', value: 'ND' }, { text: 'Northern Mariana Islands', value: 'MP' }, { text: 'Ohio', value: 'OH' }, { text: 'Oklahoma', value: 'OK' }, { text: 'Oregon', value: 'OR' }, { text: 'Palmyra Atoll', value: 'UM-95' }, { text: 'Pennsylvania', value: 'PA' }, { text: 'Puerto Rico', value: 'PR' }, { text: 'Rhode Island', value: 'RI' }, { text: 'South Carolina', value: 'SC' }, { text: 'South Dakota', value: 'SD' }, { text: 'Tennessee', value: 'TN' }, { text: 'Texas', value: 'TX' }, { text: 'United States Minor Outlying Islands', value: 'UM' }, { text: 'United States Virgin Islands', value: 'VI' }, { text: 'Utah', value: 'UT' }, { text: 'Vermont', value: 'VT' }, { text: 'Virginia', value: 'VA' }, { text: 'Wake Island', value: 'UM-79' }, { text: 'Washington', value: 'WA' }, { text: 'West Virginia', value: 'WV' }, { text: 'Wisconsin', value: 'WI' }, { text: 'Wyoming', value: 'WY' }], AU: [{ text: 'Australian Capital Territory', value: 'ACT' }, { text: 'New South Wales', value: 'NSW' }, { text: 'Northern Territory', value: 'NT' }, { text: 'Queensland', value: 'QLD' }, { text: 'South Australia', value: 'SA' }, { text: 'Tasmania', value: 'TAS' }, { text: 'Victoria', value: 'VIC' }, { text: 'Western Australia', value: 'WA' }], NZ: [{ text: 'Auckland Region', value: 'AUK' }, { text: 'Bay of Plenty Region', value: 'BOP' }, { text: 'Canterbury Region', value: 'CAN' }, { text: 'Chatham Islands', value: 'CIT' }, { text: 'Gisborne District', value: 'GIS' }, { text: "Hawke's Bay Region", value: 'HKB' }, { text: 'Manawatu-Wanganui Region', value: 'MWT' }, { text: 'Marlborough Region', value: 'MBH' }, { text: 'Nelson Region', value: 'NSN' }, { text: 'Northland Region', value: 'NTL' }, { text: 'Otago Region', value: 'OTA' }, { text: 'Southland Region', value: 'STL' }, { text: 'Taranaki Region', value: 'TKI' }, { text: 'Tasman District', value: 'TAS' }, { text: 'Waikato Region', value: 'WKO' }, { text: 'Wellington Region', value: 'WGN' }, { text: 'West Coast Region', value: 'WTC' }], IT: [{ text: 'Abruzzo', value: '65' }, { text: 'Aosta Valley', value: '23' }, { text: 'Apulia', value: '75' }, { text: 'Basilicata', value: '77' }, { text: 'Benevento Province', value: 'BN' }, { text: 'Calabria', value: '78' }, { text: 'Campania', value: '72' }, { text: 'Emilia-Romagna', value: '45' }, { text: 'Friuli\u2013Venezia Giulia', value: '36' }, { text: 'Lazio', value: '62' }, { text: 'Agrigento', value: 'AG' }, { text: 'Caltanissetta', value: 'CL' }, { text: 'Enna', value: 'EN' }, { text: 'Ragusa', value: 'RG' }, { text: 'Siracusa', value: 'SR' }, { text: 'Trapani', value: 'TP' }, { text: 'Liguria', value: '42' }, { text: 'Lombardy', value: '25' }, { text: 'Marche', value: '57' }, { text: 'Bari', value: 'BA' }, { text: 'Bologna', value: 'BO' }, { text: 'Cagliari', value: 'CA' }, { text: 'Catania', value: 'CT' }, { text: 'Florence', value: 'FI' }, { text: 'Genoa', value: 'GE' }, { text: 'Messina', value: 'ME' }, { text: 'Milan', value: 'MI' }, { text: 'Naples', value: 'NA' }, { text: 'Palermo', value: 'PA' }, { text: 'Reggio Calabria', value: 'RC' }, { text: 'Rome', value: 'RM' }, { text: 'Turin', value: 'TO' }, { text: 'Venice', value: 'VE' }, { text: 'Molise', value: '67' }, { text: 'Pesaro and Urbino Province', value: 'PU' }, { text: 'Piedmont', value: '21' }, { text: 'Alessandria', value: 'AL' }, { text: 'Ancona', value: 'AN' }, { text: 'Ascoli Piceno', value: 'AP' }, { text: 'Asti', value: 'AT' }, { text: 'Avellino', value: 'AV' }, { text: 'Barletta-Andria-Trani', value: 'BT' }, { text: 'Belluno', value: 'BL' }, { text: 'Bergamo', value: 'BG' }, { text: 'Biella', value: 'BI' }, { text: 'Brescia', value: 'BS' }, { text: 'Brindisi', value: 'BR' }, { text: 'Campobasso', value: 'CB' }, { text: 'Carbonia-Iglesias', value: 'CI' }, { text: 'Caserta', value: 'CE' }, { text: 'Catanzaro', value: 'CZ' }, { text: 'Chieti', value: 'CH' }, { text: 'Como', value: 'CO' }, { text: 'Cosenza', value: 'CS' }, { text: 'Cremona', value: 'CR' }, { text: 'Crotone', value: 'KR' }, { text: 'Cuneo', value: 'CN' }, { text: 'Fermo', value: 'FM' }, { text: 'Ferrara', value: 'FE' }, { text: 'Foggia', value: 'FG' }, { text: 'Forl\u00EC-Cesena', value: 'FC' }, { text: 'Frosinone', value: 'FR' }, { text: 'Gorizia', value: 'GO' }, { text: 'Grosseto', value: 'GR' }, { text: 'Imperia', value: 'IM' }, { text: 'Isernia', value: 'IS' }, { text: "L'Aquila", value: 'AQ' }, { text: 'La Spezia', value: 'SP' }, { text: 'Latina', value: 'LT' }, { text: 'Lecce', value: 'LE' }, { text: 'Lecco', value: 'LC' }, { text: 'Livorno', value: 'LI' }, { text: 'Lodi', value: 'LO' }, { text: 'Lucca', value: 'LU' }, { text: 'Macerata', value: 'MC' }, { text: 'Mantua', value: 'MN' }, { text: 'Massa and Carrara', value: 'MS' }, { text: 'Matera', value: 'MT' }, { text: 'Medio Campidano', value: 'VS' }, { text: 'Modena', value: 'MO' }, { text: 'Monza and Brianza', value: 'MB' }, { text: 'Novara', value: 'NO' }, { text: 'Nuoro', value: 'NU' }, { text: 'Ogliastra', value: 'OG' }, { text: 'Olbia-Tempio', value: 'OT' }, { text: 'Oristano', value: 'OR' }, { text: 'Padua', value: 'PD' }, { text: 'Parma', value: 'PR' }, { text: 'Pavia', value: 'PV' }, { text: 'Perugia', value: 'PG' }, { text: 'Pescara', value: 'PE' }, { text: 'Piacenza', value: 'PC' }, { text: 'Pisa', value: 'PI' }, { text: 'Pistoia', value: 'PT' }, { text: 'Pordenone', value: 'PN' }, { text: 'Potenza', value: 'PZ' }, { text: 'Prato', value: 'PO' }, { text: 'Ravenna', value: 'RA' }, { text: 'Reggio Emilia', value: 'RE' }, { text: 'Rieti', value: 'RI' }, { text: 'Rimini', value: 'RN' }, { text: 'Rovigo', value: 'RO' }, { text: 'Salerno', value: 'SA' }, { text: 'Sassari', value: 'SS' }, { text: 'Savona', value: 'SV' }, { text: 'Siena', value: 'SI' }, { text: 'Sondrio', value: 'SO' }, { text: 'Taranto', value: 'TA' }, { text: 'Teramo', value: 'TE' }, { text: 'Terni', value: 'TR' }, { text: 'Treviso', value: 'TV' }, { text: 'Trieste', value: 'TS' }, { text: 'Udine', value: 'UD' }, { text: 'Varese', value: 'VA' }, { text: 'Verbano-Cusio-Ossola', value: 'VB' }, { text: 'Vercelli', value: 'VC' }, { text: 'Verona', value: 'VR' }, { text: 'Vibo Valentia', value: 'VV' }, { text: 'Vicenza', value: 'VI' }, { text: 'Viterbo', value: 'VT' }, { text: 'Sardinia', value: '88' }, { text: 'Sicily', value: '82' }, { text: 'South Tyrol', value: 'BZ' }, { text: 'Trentino', value: 'TN' }, { text: 'Trentino-South Tyrol', value: '32' }, { text: 'Tuscany', value: '52' }, { text: 'Umbria', value: '55' }, { text: 'Veneto', value: '34' }], CA: [{ text: 'Alberta', value: 'AB' }, { text: 'British Columbia', value: 'BC' }, { text: 'Manitoba', value: 'MB' }, { text: 'New Brunswick', value: 'NB' }, { text: 'Newfoundland and Labrador', value: 'NL' }, { text: 'Northwest Territories', value: 'NT' }, { text: 'Nova Scotia', value: 'NS' }, { text: 'Nunavut', value: 'NU' }, { text: 'Ontario', value: 'ON' }, { text: 'Prince Edward Island', value: 'PE' }, { text: 'Quebec', value: 'QC' }, { text: 'Saskatchewan', value: 'SK' }, { text: 'Yukon', value: 'YT' }], BR: [{ text: 'Acre', value: 'AC' }, { text: 'Alagoas', value: 'AL' }, { text: 'Amap\u00E1', value: 'AP' }, { text: 'Amazonas', value: 'AM' }, { text: 'Bahia', value: 'BA' }, { text: 'Cear\u00E1', value: 'CE' }, { text: 'Distrito Federal', value: 'DF' }, { text: 'Esp\u00EDrito Santo', value: 'ES' }, { text: 'Goi\u00E1s', value: 'GO' }, { text: 'Maranh\u00E3o', value: 'MA' }, { text: 'Mato Grosso', value: 'MT' }, { text: 'Mato Grosso do Sul', value: 'MS' }, { text: 'Minas Gerais', value: 'MG' }, { text: 'Par\u00E1', value: 'PA' }, { text: 'Para\u00EDba', value: 'PB' }, { text: 'Paran\u00E1', value: 'PR' }, { text: 'Pernambuco', value: 'PE' }, { text: 'Piau\u00ED', value: 'PI' }, { text: 'Rio de Janeiro', value: 'RJ' }, { text: 'Rio Grande do Norte', value: 'RN' }, { text: 'Rio Grande do Sul', value: 'RS' }, { text: 'Rond\u00F4nia', value: 'RO' }, { text: 'Roraima', value: 'RR' }, { text: 'Santa Catarina', value: 'SC' }, { text: 'S\u00E3o Paulo', value: 'SP' }, { text: 'Sergipe', value: 'SE' }, { text: 'Tocantins', value: 'TO' }] }

const TRACKING_PLATFORMS = {
  ALL: 'all',
  CUSTOM: 'custom',
  NEW_CUSTOM: 'new_custom',
  KLAVIYO: 'klaviyo',
  FACEBOOK: 'facebook',
  PINTEREST: 'pinterest',
  TIKTOK: 'tiktok',
  GOOGLE: 'google',
  BING: 'bing',
  REDDIT: 'reddit',
  QUORA: 'quora',
  SNAP: 'snap'
}

const TRACKING_EVENT_LIST = {
  page_view: {
    custom: 'pageview',
    facebook: 'PageView',
    pintrk: 'PageVisit',
    ttq: 'page_view',
    gtag: 'page_view',
    snap: 'PAGE_VIEW',
    quora: 'ViewContent',
    bing: 'page_view',
    klaviyo: 'Active on Site',
    reddit: 'PageVisit',
  },
  view_content: {
    custom: 'viewcontent',
    facebook: 'ViewContent',
    pintrk: 'PageVisit',
    ttq: 'ViewContent',
    gtag: 'view_item',
    snap: 'VIEW_CONTENT',
    quora: 'ViewContent',
    bing: 'view_item',
    klaviyo: 'Viewed Product',
    reddit: 'ViewContent',
  },
  contact: {
    custom: 'contact',
    facebook: 'Contact',
    pintrk: 'Contact',
    ttq: 'Contact',
    gtag: false,
    snap: 'CONTACT',
    quora: 'Contact',
    bing: 'contact',
    reddit: 'Contact',
  },
  add_to_cart: {
    custom: 'addtocart',
    facebook: 'AddToCart',
    pintrk: 'AddToCart',
    ttq: 'AddToCart',
    gtag: 'add_to_cart',
    snap: 'ADD_CART',
    quora: 'AddToCart',
    bing: 'add_to_cart',
    klaviyo: 'Added to Cart',
    reddit: 'AddToCart',
  },
  initiate_checkout: {
    custom: 'initiatecheckout',
    facebook: 'InitiateCheckout',
    pintrk: 'Checkout',
    ttq: 'InitiateCheckout',
    gtag: 'begin_checkout',
    snap: 'START_CHECKOUT',
    quora: 'InitiateCheckout',
    bing: 'begin_checkout',
    klaviyo: 'Started Checkout',
    reddit: 'InitiateCheckout',
  },
  purchase: {
    custom: 'purchase',
    facebook: 'Purchase',
    pintrk: 'Purchase',
    ttq: 'CompletePayment',
    gtag: 'purchase',
    snap: 'PURCHASE',
    quora: 'Purchase',
    bing: 'purchase',
    reddit: 'Purchase',
  },
  view_item_list: {
    gtag: 'view_item_list',
    klaviyo: 'Viewed Collection',
  },
  select_item: {
    gtag: 'select_item'
  },
  search: {
    klaviyo: 'Submitted Search'
  }
}

const CUSTOM_OPTION_TYPE = {
  TEXT: 'text',
  DROPDOWN: 'dropdown',
  IMAGE: 'image'
}

const PAYMENT_GATEWAY_TYPE = {
  BOTH: 'both',
  PAYPAL: 'paypal',
  STRIPE: 'stripe'
}
const PRICING_MODE = {
  ADJUST_PRICE: 'adjust_price',
  FIXED_PRICE: 'fixed_price',
  CUSTOM_PRICE: 'custom_price'
}

const ORDER_SUMMARY_POSITION = {
  TOP: 'top',
  BOTTOM: 'bottom'
}

const BUNDLE_DISCOUNT_VIEW_PLACE = {
  CAMPAIGN_DETAIL_AND_CART: 0,
  CAMPAIGN_DETAIL: 1,
  CART: 2
}

const DOMAIN_MARKET_PLACE = ['senprints.com', 'senstores.com']

const DISCOUNT_ERROR_TYPE = {
  NONE: 0,
  USING_OTHER_DISCOUNT: 1
}

export {
  API_LIST,
  METHOD,
  CURRENCY,
  USD_CODE,
  EUR_CODE,
  VND_CODE,
  COUNTRY_STATE_LIST,
  TRACKING_EVENT_LIST,
  CUSTOM_OPTION_TYPE,
  PAYMENT_GATEWAY_TYPE,
  PRICING_MODE,
  ORDER_SUMMARY_POSITION,
  BUNDLE_DISCOUNT_VIEW_PLACE,
  DOMAIN_MARKET_PLACE,
  DISCOUNT_ERROR_TYPE,
  TRACKING_PLATFORMS
}
