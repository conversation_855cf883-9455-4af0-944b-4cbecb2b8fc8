export function isMissingRequiredOption (customOptions, commonOptions, customerCustomOptions) {
  const hasCustomOptions = customOptions && customOptions.length > 0
  const hasCommonOptions = commonOptions && commonOptions.length > 0
  if (!hasCustomOptions && !hasCommonOptions) {
    return false
  }
  const allCustomOptionsOptional = hasCustomOptions ? customOptions.every(option => option.unrequired) : true
  const allCommonOptionsOptional = hasCommonOptions ? commonOptions.every(option => option.unrequired) : true
  const hasValue = customerCustomOptions.length ? customerCustomOptions.every((groupOption) => {
    return groupOption.every(customOption => customOption.value || customOption.unrequired)
  }) : false
  const allOptional = allCustomOptionsOptional && allCommonOptionsOptional
  return !allOptional && !hasValue
}

export function isShowCampaignThumbnailInsteadOfProduct (campaign) {
  if (!campaign) {
    return false
  }

  return campaign.system_type === 'custom' &&
    campaign.products.length === 1
}

export function getFilterImages (campaign, product, color) {
  let filterImages = []
  if (Array.isArray(campaign?.images)) {
    filterImages = [...campaign.images]
  }

  if (product && product.id) {
    filterImages = filterImages.filter(image => image.product_id === product.id || image.product_id === product.template_id || image.product_id === null)
  }

  if (color) {
    const imageByColor = filterImages.filter(image => (!image.option || image.option.replace(/ /g, '-') === color))
    if (imageByColor && imageByColor.length) {
      filterImages = imageByColor
    }
  }
  return filterImages
}
