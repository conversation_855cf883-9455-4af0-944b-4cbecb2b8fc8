@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?mih0tv');
  src:  url('fonts/icomoon.eot?mih0tv#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?mih0tv') format('truetype'),
    url('fonts/icomoon.woff?mih0tv') format('woff'),
    url('fonts/icomoon.svg?mih0tv#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-sen-image-edit:before {
  content: "\e939";
}
.icon-sen-skip-previous:before {
  content: "\e937";
}
.icon-sen-skip-next:before {
  content: "\e938";
}
.icon-sen-arrow-down-thin:before {
  content: "\e930";
}
.icon-sen-arrow-right-thin:before {
  content: "\e931";
}
.icon-sen-arrow-up-thin:before {
  content: "\e932";
}
.icon-sen-arrow-left-thin:before {
  content: "\e933";
}
.icon-sen-magnify-minus-outline:before {
  content: "\e934";
}
.icon-sen-magnify-plus-outline:before {
  content: "\e935";
}
.icon-sen-refresh:before {
  content: "\e936";
}
.icon-sen-plus-circle:before {
  content: "\e92e";
}
.icon-sen-checkbox-marked:before {
  content: "\e92f";
}
.icon-sen-check:before {
  content: "\e92d";
}
.icon-sen-bike-fast:before {
  content: "\e92b";
}
.icon-sen-tag:before {
  content: "\e92c";
}
.icon-sen-checkbox-blank-outline:before {
  content: "\e92a";
}
.icon-sen-menu-down:before {
  content: "\e928";
}
.icon-sen-menu-swap:before {
  content: "\e929";
}
.icon-sen-checkbox-marked-outline:before {
  content: "\e927";
}
.icon-sen-chevron-double-left:before {
  content: "\e925";
}
.icon-sen-chevron-double-right:before {
  content: "\e926";
}
.icon-sen-truck-delivery-outline:before {
  content: "\e906";
}
.icon-sen-cart-remove:before {
  content: "\e907";
}
.icon-sen-package-variant:before {
  content: "\e909";
}
.icon-sen-printer-check:before {
  content: "\e90a";
}
.icon-sen-account-check:before {
  content: "\e90b";
}
.icon-sen-alert-outline:before {
  content: "\e90e";
}
.icon-sen-star:before {
  content: "\e90f";
}
.icon-sen-play-circle-outline:before {
  content: "\e910";
}
.icon-sen-alert:before {
  content: "\e911";
}
.icon-sen-arrow-left:before {
  content: "\e912";
}
.icon-sen-checkbox-blank:before {
  content: "\e913";
}
.icon-sen-text-box-multiple:before {
  content: "\e914";
}
.icon-sen-help-circle-outline:before {
  content: "\e915";
}
.icon-sen-twitter:before {
  content: "\e916";
}
.icon-sen-pinterest:before {
  content: "\e917";
}
.icon-sen-skype-business:before {
  content: "\e918";
}
.icon-sen-instagram:before {
  content: "\e919";
}
.icon-sen-facebook:before {
  content: "\e91a";
}
.icon-sen-chevron-right:before {
  content: "\e91b";
}
.icon-sen-chevron-left:before {
  content: "\e91c";
}
.icon-sen-minus:before {
  content: "\e91d";
}
.icon-sen-delete:before {
  content: "\e91e";
}
.icon-sen-square-edit-outline:before {
  content: "\e91f";
}
.icon-sen-camera:before {
  content: "\e920";
}
.icon-sen-chevron-down:before {
  content: "\e922";
}
.icon-sen-chevron-up:before {
  content: "\e923";
}
.icon-sen-plus:before {
  content: "\e924";
}
.icon-sen-cart:before {
  content: "\e986";
}
.icon-sen-search:before {
  content: "\e9b4";
}
.icon-sen-menu:before {
  content: "\ea39";
}
.icon-sen-cart-arrow-down:before {
  content: "\e900";
}
.icon-sen-image-multiple-outline:before {
  content: "\e901";
}
.icon-sen-text-box-multiple-outline:before {
  content: "\e902";
}
.icon-sen-ruler:before {
  content: "\e903";
}
.icon-sen-lead-pencil:before {
  content: "\e904";
}
.icon-sen-close-circle:before {
  content: "\e905";
}
.icon-sen-lock:before {
  content: "\e93b";
}
.icon-sen-cart-plus:before {
  content: "\e908";
}
.icon-sen-cash-refund:before {
  content: "\e921";
}
.icon-sen-hand-coin:before {
  content: "\e93a";
}
.icon-sen-loading:before {
  content: "\e97b";
}
.icon-sen-google:before {
  content: "\ea8a";
}
.icon-sen-youtube:before {
  content: "\ea9d";
}
