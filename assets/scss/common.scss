.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.small-badge {
  height: 1rem;
  width: 1rem;
  position: absolute;
  right: -8px;
  top: 5px;
  border-radius: 0.5rem;
  text-align: center;
  line-height: 1rem;
  color: white;
  font-size: 0.75rem;
  font-weight: normal;
}

.w-md-auto {
  @media (min-width: 768px) {
    width: auto !important;
  }
}

.w-md-50 {
  @media (min-width: 768px) {
    width: 50% !important;
  }
}

.w-lg-50 {
  @media (min-width: 992px) {
    width: 50% !important;
  }
}

.mw-md-maxcontent {
  @media (min-width: 768px) {
    max-width: max-content !important;
  }
}

.container-xl {
  max-width: 100%;

  @media (min-width: 1300px) {
    max-width: 1300px;
  }
}

.border-none {
  border: none !important;
}

body:not(.classic) .border-radius-none {
  border-radius: unset !important;
}

.transition-fade-1 {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.text-overflow-hidden {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.font-weight-200 {
  font-weight: 200;
}

.font-weight-300 {
  font-weight: 300;
}

.font-weight-400 {
  font-weight: 400;
}

.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

.font-small {
  font-size: small !important;
}

.font-large {
  font-size: large !important;
}

.font-3xl {
  font-size: 1.75rem;
}

.font-4xl {
  font-size: 2rem;
}

.cursor-pointer {
  cursor: pointer;
}

.mh-70vh {
  max-height: 70vh;
}

.bottom-0 {
  bottom: 0;
}

.top-0 {
  top: 0;
}

.left-0 {
  left: 0;
}

.z-index-1 {
  z-index: 1;
}

.z-index-100 {
  z-index: 100;
}

.d-grid {
  display: grid;
}

.py-2-5 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.text-gray {
  color: rgb(173, 173, 173);
}

.min-w-max-content {
  min-width: max-content;
}

.index-0 {
  z-index: 0;
}

.opacity-9 {
  opacity: 0.9;
}

.text-underline {
  text-decoration: underline;
}

.h-fit-content {
  height: fit-content;
}

.visibility-hidden {
  visibility: hidden;
}

.right-0 {
  right: 0;
}

.pe-none {
  pointer-events: none;
}

.bg-warm {
  background-color: rgb(245, 245, 245);
}

.h-50px {
  height: 50px;
}

.gap-2 {
  row-gap: 0.125rem;
  column-gap: 0.5rem;
}
