FROM node:erbium-alpine as builer
WORKDIR /home/<USER>/app

RUN apk add --update --no-cache pkgconfig libaio libnsl libtool autoconf automake nasm build-base curl zlib-dev libc6-compat gcompat ffmpeg opus pixman cairo pango giflib ca-certificates \
  git \
  openssh \
  python \
  python-dev \
  py-pip \
  curl \
  && apk add --no-cache --virtual .build-deps git pkgconfig libaio libtool autoconf automake nasm build-base curl zlib-dev libc6-compat gcompat jpeg-dev pixman-dev \
  cairo-dev pango-dev pangomm-dev libjpeg-turbo-dev giflib-dev freetype-dev python g++ make \
  && cd /tmp && \
  curl -o instantclient-basiclite.zip https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip -SL && \
  unzip instantclient-basiclite.zip && \
  mv instantclient*/ /usr/lib/instantclient && \
  rm instantclient-basiclite.zip && \
  ln -sf /usr/lib/instantclient/libclntsh.so.19.1 /usr/lib/libclntsh.so && \
  ln -s /usr/lib/instantclient/libocci.so.19.1 /usr/lib/libocci.so && \
  ln -s /usr/lib/instantclient/libociicus.so /usr/lib/libociicus.so && \
  ln -s /usr/lib/instantclient/libnnz19.so /usr/lib/libnnz19.so && \
  ln -s /usr/lib/libnsl.so.2 /usr/lib/libnsl.so.1 && \
  ln -s /lib/libc.so.6 /usr/lib/libresolv.so.2 && \
  ln -s /lib64/ld-linux-x86-64.so.2 /usr/lib/ld-linux-x86-64.so.2 \
  && yarn global add node-gyp \
  && yarn global add node-sass \
  && rm -rf /var/cache/apk/*

ENV ORACLE_BASE /usr/lib/instantclient
ENV LD_LIBRARY_PATH /usr/lib/instantclient
ENV TNS_ADMIN /usr/lib/instantclient
ENV ORACLE_HOME /usr/lib/instantclient

ADD ./package.json ./package.json
ADD ./start.sh /usr/local/bin/start-nuxt.sh
RUN yarn add global canvas && yarn install --build-from-source --unsafe-perm --allow-root \
    && apk del .build-deps \
    && chmod 777 /usr/local/bin/start-nuxt.sh \
    && ln -s /usr/local/bin/start-nuxt.sh

COPY . .

FROM builer as base

ARG APP_ENV="production"

WORKDIR /home/<USER>/app
COPY --from=builer /home/<USER>/app .
COPY --from=builer /usr/local/bin/start-nuxt.sh ./start-nuxt.sh

ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=3000
EXPOSE 3000
RUN ./start-nuxt.sh ${APP_ENV}

#### test deploy 4