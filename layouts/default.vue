<template>
  <div v-if="$store.state.storeInfo.status!=='blocked'" class="wrapper">
    <sitewide-banner v-if="$store.state.storeInfo.sitewide_banner_enable && $store.state.storeInfo.sitewide_banner" />
    <header-wraper />
    <Nuxt />
    <html-head-tag :for-position="'body'" />
    <modal-view-image />
    <modal-size-guide />
    <modal-campaign-wraper />

    <!-- Confirm Recaptcha -->
    <b-modal
      id="confirm-recaptcha"
      title="Confirm"
      hide-footer
      @shown="$tracking.newCustomTracking('modal-shown', null, 'confirm-recaptcha', null)"
      @hidden="$tracking.newCustomTracking('modal-hiden', null, 'confirm-recaptcha', null)"
    >
      <b-alert variant="warning" class="text-center" show>
        We noticed something unusual from your network. Please confirm to continue!
      </b-alert>

      <recaptcha />

      <b-alert :show="errorCaptcha" variant="danger" class="text-center mt-3">
        <strong>Error:</strong> Unconfimred.
      </b-alert>

      <b-button
        type="button"
        variant="custom"
        class="mt-3"
        size="lg"
        block
        :disabled="clicked"
        @click="onConfirmRecaptcha"
      >
        {{ $t('Proceed to checkout') }}
      </b-button>
    </b-modal>
    <cookie-consent v-if="isShowCookieConsent" @confirm="confirmCookieConsent" />
    <footer-wraper />
  </div>
  <div v-else class="vh-100 d-flex flex-column">
    <div class="error-page d-flex align-items-center justify-content-center" style="flex: 1;">
      <Nuxt v-if="isAcceptAblePath" />
      <h2 v-else>
        {{ $t('This store is suspended due to policy violation.') }}
      </h2>
    </div>
    <footer-wraper :is-blocked-store="true" />
  </div>
</template>

<script>
import { addListener, launch } from 'devtools-detector'
import lscache from 'lscache'
import tracking from '~/mixins/tracking'
import rtl from '~/mixins/rtl'

const ACCEPTABLE_PATH = ['/order/track', '/page/contact-us', '/order/status']
export default {
  name: 'DefaultLayout',
  mixins: [tracking, rtl],
  data () {
    return {
      reCaptchaEvent: '',
      clicked: false,
      errorCaptcha: false,
      isShowCookieConsent: false
    }
  },
  computed: {
    isAcceptAblePath () {
      for (let i = 0; i < ACCEPTABLE_PATH.length; i++) {
        if (this.$route.path.includes(ACCEPTABLE_PATH[i])) {
          return true
        }
      }
      return false
    }
  },
  mounted () {
    if (process.browser) {
      setTimeout(searchHotkeyInit, 500)
    }

    this.$bus.$on('confirm-recaptcha', () => {
      this.$bvModal.show('confirm-recaptcha')
    })

    this.$bus.$on('recaptcha-reset-button-state', () => {
      this.clicked = false
    })

    if (this.$store.state.storeInfo.status === 'blocked') {
      document.querySelector(':root').style.setProperty('--primary-color', '#ef4444')
    }

    const isTurnOffCookieConsent = lscache.get('is-turn-off-cookie-consent')
    if (!isTurnOffCookieConsent) {
      this.doShowCookieConsentForEu()
    }

    if (!this.$route.query.ddd && this.$config.appEnv === 'production') {
      addListener((isOpen) => {
        const visitInfo = lscache.get('visitInfo')
        const device = isOpen ? 'desktop' : visitInfo.device
        lscache.set('visitInfo', { ...visitInfo, dd: isOpen, device })
      })

      launch()
    }
  },
  beforeDestroy () {
    this.$log('beforeDestroy DefaultLayout')
    this.$bus.$off('confirm-recaptcha')
    this.$bus.$off('recaptcha-reset-button-state')
  },
  methods: {
    doShowCookieConsentForEu (retry = 0) {
      const code = this.$store.state.userCountry
      if (code) {
        const EU_COUNTRIES = [
          'AT', // Austria
          'BE', // Belgium
          'BG', // Bulgaria
          'HR', // Croatia
          'CY', // Republic of Cyprus
          'CZ', // Czech Republic
          'DK', // Denmark
          'EE', // Estonia
          'FI', // Finland
          'FR', // France
          'DE', // Germany
          'GR', // Greece
          'HU', // Hungary
          'IE', // Ireland
          'IT', // Italy
          'LV', // Latvia
          'LT', // Lithuania
          'LU', // Luxembourg
          'MT', // Malta
          'NL', // Netherlands
          'PL', // Poland
          'PT', // Portugal
          'RO', // Romania
          'SK', // Slovakia
          'SI', // Slovenia
          'ES', // Spain
          'SE', // Sweden
          'UK', // United Kingdom
          'GB' // United Kingdom
        ]
        const isEu = EU_COUNTRIES.includes(code)
        this.isShowCookieConsent = isEu
        lscache.set('is-turn-off-cookie-consent', !isEu)
      } else {
        setTimeout(() => {
          if (retry < 11) {
            this.doShowCookieConsentForEu(retry + 1)
          }
        }, 1000)
      }
    },
    confirmCookieConsent () {
      this.isShowCookieConsent = false
      lscache.set('is-turn-off-cookie-consent', true)
    },
    async onConfirmRecaptcha () {
      this.errorCaptcha = false

      // check if we have a valid token
      try {
        await this.$recaptcha.getResponse()
      } catch (e) {
        this.errorCaptcha = true
        return
      }

      this.clicked = true
      await this.$store.dispatch('order/createOrderWithRecaptcha')
    }
  }
}

function searchHotkeyInit () {
  document.body.addEventListener('keydown', (e) => {
    if (!window.searchInput) {
      window.searchInput = document.querySelector('#search-keyword')
    }

    if (e.ctrlKey && e.key === '/') {
      window.searchInput.focus()
    }
  })
}
</script>
