<template>
  <div v-if="$store.state.storeInfo.status!=='blocked'" class="wrapper">
    <sitewide-banner v-if="$store.state.storeInfo.sitewide_banner_enable && $store.state.storeInfo.sitewide_banner" />
    <header-wraper-order />
    <Nuxt />
    <modal-campaign-wraper />
    <modal-view-image />
    <footer-wraper-order />
  </div>
  <div v-else class="py-5">
    <div class="error-page mt-5 pt-5 text-center">
      <h2>  {{ $t('This store is suspended due to policy violation.') }} </h2>
    </div>
  </div>
</template>

<script>
import tracking from '~/mixins/tracking'
import rtl from '~/mixins/rtl'
export default {
  name: 'CheckoutLayout',
  mixins: [tracking, rtl]
}
</script>
