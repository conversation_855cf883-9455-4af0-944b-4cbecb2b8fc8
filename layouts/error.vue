<template>
  <Component
    :is="asyncComponent"
    :status-code="error&&error.statusCode"
  />
</template>
<script>
import rtl from '~/mixins/rtl'
export default {
  mixins: [rtl],
  props: {
    error: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      title: this.$store.state.storeInfo.name || ''
    }
  },
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  return {
    title: this.title,
    meta: this.$createSEOMeta({ title: this.title, description: this.title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/error`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/error'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/error'
      )
  }
}
</script>
