export default {
  // Global page headers (https://go.nuxtjs.dev/config-head)
  head: {
    title: '',

    meta: [{
      charset: 'utf-8'
    }, {
      hid: 'description',
      name: 'description',
      content: '' // todo: update description
    }, {
      name: 'HandheldFriendly',
      content: 'True'
    }, {
      name: 'MobileOptimized',
      content: '320'
    }, {
      name: 'viewport',
      content: 'initial-scale=1.0,user-scalable=no,maximum-scale=1,width=device-width'
    }, {
      name: 'version',
      content: '1.0.2'
    }],

    link: [{
      rel: 'preconnect',
      href: process.env.TRACKING_ENDPOINT_URL ? process.env.TRACKING_ENDPOINT_URL.match(/https:\/\/[^/]+/)[0] : 'https://logs.cloudimgs.net'
    }, {
      rel: 'dns-prefetch',
      href: process.env.TRACKING_ENDPOINT_URL ? process.env.TRACKING_ENDPOINT_URL.match(/https:\/\/[^/]+/)[0] : 'https://logs.cloudimgs.net'
    }, {
      rel: 'preconnect',
      href: 'https://cdn.jsdelivr.net'
    }, {
      rel: 'dns-prefetch',
      href: 'https://cdn.jsdelivr.net'
    }, {
      rel: 'preconnect',
      href: process.env.BASE_IMAGE_URL || 'https://img.cloudimgs.net'
    }, {
      rel: 'dns-prefetch',
      href: process.env.BASE_IMAGE_URL || 'https://img.cloudimgs.net'
    }, {
      hid: 'icon',
      rel: 'icon',
      type: 'image/x-icon',
      href: '/favicon.ico'
    }]
  },

  serverMiddleware: [
    // '~/server-middleware/logger'\
    '~/server-middleware/applePay'
  ],

  // Global CSS (https://go.nuxtjs.dev/config-css)
  css: [
    '~/assets/scss'
  ],

  // Plugins to run before rendering page (https://go.nuxtjs.dev/config-plugins)
  plugins: [
    '~/plugins/tracking',
    '~/plugins/http',
    '~/plugins/log',
    '~/plugins/commonFunction',
    '~/plugins/globalVuePlugins',
    '~/plugins/awsTempUpload',
    { src: '~/plugins/createMutationsSharer', ssr: false },
    // { src: '~/plugins/persistedstate', ssr: false },
    { src: '~/plugins/base-mockup', ssr: false },
    { src: '~/plugins/t-mockup', ssr: false },
    { src: '~/plugins/design-canvas', ssr: false },
    { src: '~plugins/globalVuePluginsNoSsr', ssr: false },
    { src: '~plugins/testPrice', ssr: true },
    { src: '~plugins/lscache', ssr: false },
    { src: '~/plugins/vue-video-player.js', ssr: false },
  ],

  // https://nuxtjs.org/api/configuration-extend-plugins/
  extendPlugins (plugins) {
    // always load Event Bus first
    plugins.unshift('~/plugins/bus.client.js')
    return plugins
  },

  // Auto import components (https://go.nuxtjs.dev/config-components)
  components: true,

  // Modules for dev and build (recommended) (https://go.nuxtjs.dev/config-modules)
  buildModules: [
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',
    // https://go.nuxtjs.dev/stylelint
    '@nuxtjs/stylelint-module'
  ],

  // Modules (https://go.nuxtjs.dev/config-modules)
  modules: [
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',
    '@nuxtjs/proxy',
    // https://go.nuxtjs.dev/pwa
    // '@nuxtjs/pwa',
    '@nuxtjs/toast',
    '@nuxtjs/recaptcha',

    'bootstrap-vue/nuxt',
    '~/modules/ping.js',
    '~/modules/healthcheck.js',
    'nuxt-i18n',
    '@nuxtjs/gtm',
    // With options
    ['cookie-universal-nuxt', { alias: 'cookies' }],
    '~modules/dynamic-robots.js',
    '~modules/sitemap.js',
    '@nuxtjs/sentry'
  ],

  i18n: {
    vueI18n: {
      fallbackLocale: 'en',
      silentTranslationWarn: process.env.I18N_TRANSLATION_WARN || false
    },
    lazy: true,
    seo: false,
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: true
    },
    locales: [
      {
        name: 'English (US)',
        countryCode: ['US', 'USA'],
        code: 'en',
        date: 'en-US',
        iso: 'en',
        file: 'en.js'
      }, {
        name: 'English (UK)',
        countryCode: ['GB', 'GBR'],
        code: 'en-gb',
        date: 'en-GB',
        iso: 'en-GB',
        file: 'en-GB.js'
      },
      {
        name: 'اللغة العربية',
        countryCode: ['PS', 'PSE', 'DZ', 'DZA', 'BH', 'BHR', 'TD', 'TCD', 'KM', 'COM', 'DJ', 'DJI', 'EG', 'EGY', 'IQ', 'IRQ', 'JO', 'JOR', 'KW', 'KWT', 'LB', 'LBN', 'LY', 'LBY', 'MR', 'MRT', 'MA', 'MAR', 'OM', 'OMN', 'QA', 'QAT', 'SA', 'SAU', 'SO', 'SOM', 'SD', 'SDN', 'SY', 'SYR', 'TN', 'TUN', 'AE', 'ARE', 'YE', 'YEM'],
        code: 'ar',
        iso: 'ar',
        date: 'ar-EG',
        file: 'ar.js'
      },
      {
        name: 'বাংলা ভাষা',
        countryCode: ['BD', 'BGD'],
        code: 'bn',
        iso: 'bn',
        file: 'bn.js'
      },
      {
        name: 'Česky',
        countryCode: ['CZ', 'CZE'],
        code: 'cs',
        iso: 'cs',
        date: 'cs',
        file: 'cs.js'
      },
      {
        name: 'Dansk',
        countryCode: ['DK', 'DNK'],
        code: 'da',
        iso: 'da',
        date: 'da-DK',
        file: 'da.js'
      },
      {
        name: 'Deutsch',
        countryCode: ['DE', 'DEU', 'AT', 'AUT', 'BE', 'BEL', 'LU', 'LUX', 'CH', 'CHE', 'LI', 'LIE'],
        code: 'de',
        iso: 'de',
        date: 'de-DE',
        file: 'de.js'
      },
      {
        name: 'Ελληνική Γλώσσα',
        countryCode: ['GR', 'GRC'],
        code: 'el',
        iso: 'el',
        date: 'el',
        file: 'el.js'
      },
      {
        name: 'Español',
        countryCode: ['ES', 'ESP', 'AR', 'ARG', 'BO', 'BOL', 'CL', 'CHL', 'CO', 'COL', 'CR', 'CRI', 'CU', 'CUB', 'DO', 'DOM', 'EC', 'ECU', 'SV', 'SLV', 'GT', 'GTM', 'HN', 'HND', 'MX', 'MEX', 'NI', 'NIC', 'PA', 'PAN', 'PY', 'PRY', 'PE', 'PER', 'UY', 'URY', 'VE', 'VEN'],
        code: 'es',
        iso: 'es',
        date: 'es',
        file: 'es.js'
      },
      {
        name: 'Eesti Keel',
        countryCode: ['EE', 'EST'],
        code: 'et',
        iso: 'et',
        date: 'et-EE',
        file: 'et.js'
      },
      {
        name: 'Suomen Kieli',
        countryCode: ['SO', 'SOM'],
        code: 'fi',
        iso: 'fi',
        date: 'fi-FI',
        file: 'fi.js'
      },
      {
        name: 'Français',
        countryCode: ['FR', 'FRA', 'GA', 'GAB', 'GN', 'GIN', 'ML', 'MLI', 'MC', 'MCO', 'NE', 'NER', 'SN', 'SEN', 'TG', 'TGO', 'CA', 'CAN'],
        code: 'fr',
        iso: 'fr',
        date: 'fr-FR',
        file: 'fr.js'
      },
      {
        name: 'Hrvatski',
        countryCode: ['HR', 'HRV'],
        code: 'hr',
        iso: 'hr',
        date: 'hr',
        file: 'hr.js'
      },
      {
        name: 'Magyar Nyelv',
        countryCode: ['HU', 'HUN'],
        code: 'hu',
        iso: 'hu',
        date: 'hu-HU',
        file: 'hu.js'
      },
      {
        name: 'Bahasa Indonesia',
        countryCode: ['ID', 'IDN'],
        code: 'id',
        iso: 'id',
        file: 'id.js'
      },
      {
        name: 'Italiano',
        countryCode: ['IT', 'ITA'],
        code: 'it',
        iso: 'it',
        date: 'it-IT',
        file: 'it.js'
      },
      {
        name: '日本語',
        countryCode: ['JP', 'JPN'],
        code: 'ja',
        iso: 'ja',
        date: 'ja-JP',
        file: 'ja.js'
      },
      {
        name: '한국어',
        countryCode: ['KR', 'KOR'],
        code: 'ko',
        iso: 'ko',
        date: 'ko-KR',
        file: 'ko.js'
      },
      {
        name: 'Lietuvių kalba',
        countryCode: ['LT', 'LTU'],
        code: 'lt',
        iso: 'lt',
        file: 'lt.js'
      },
      {
        name: 'Bahasa Melayu',
        countryCode: ['MY', 'MYS'],
        code: 'ms',
        iso: 'ms',
        file: 'ms.js'
      },
      {
        name: 'Nederlands',
        countryCode: ['NL', 'NLD'],
        code: 'nl',
        iso: 'nl',
        date: 'nl-NL',
        file: 'nl.js'
      },
      {
        name: 'Norsk',
        countryCode: ['NO', 'NOR'],
        code: 'no',
        iso: 'no',
        date: 'no-NO',
        file: 'no.js'
      },
      {
        name: 'Polski',
        countryCode: ['PL', 'POL'],
        code: 'pl',
        iso: 'pl',
        date: 'pl-PL',
        file: 'pl.js'
      },
      {
        name: 'Português',
        countryCode: ['PT', 'PRT', 'BR', 'BRA', 'CV', 'CPV', 'AO', 'AGO', 'MZ', 'MOZ', 'GW', 'GNB', 'ST', 'STP'],
        code: 'pt',
        iso: 'pt',
        date: 'pt-PT',
        file: 'pt.js'
      },
      {
        name: 'Limba Română',
        countryCode: ['RO', 'ROU'],
        code: 'ro',
        iso: 'ro',
        date: 'ro-RO',
        file: 'ro.js'
      },
      {
        name: 'Slovenský jazyk',
        countryCode: ['SK', 'SVK'],
        code: 'sk',
        iso: 'sk',
        file: 'sk.js'
      },
      {
        name: 'Slovenski Jezik',
        countryCode: ['SI', 'SVN'],
        code: 'sl',
        iso: 'sl',
        date: 'sl-SI',
        file: 'sl.js'
      },
      {
        name: 'Gjuha shqipe',
        countryCode: ['AL', 'ALB'],
        code: 'sq',
        iso: 'sq',
        file: 'sq.js'
      },
      {
        name: 'Svenska',
        countryCode: ['SE', 'SWE'],
        code: 'sv',
        iso: 'sv',
        date: 'sv-SE',
        file: 'sv.js'
      },
      {
        name: 'Türkçe',
        countryCode: ['TR', 'TUR', 'CY', 'CYP'],
        code: 'tr',
        iso: 'tr',
        date: 'tr-TR',
        file: 'tr.js'
      },
      {
        name: 'Tiếng Việt',
        countryCode: ['VN', 'VNM'],
        code: 'vi',
        iso: 'vi',
        date: 'vi',
        file: 'vi.js'
      },
      {
        name: '中文',
        countryCode: ['CN', 'CHN', 'TW', 'TWN', 'HK', 'HKG'],
        code: 'zh',
        iso: 'zh',
        date: 'zh-CN',
        file: 'zh.js'
      }
    ],
    loadLanguagesAsync: true,
    langDir: 'lang/',
    defaultLocale: 'en'
  },

  bootstrapVue: {
    bootstrapCSS: true, // Or `css: false`
    bootstrapVueCSS: true, // Or `bvCSS: false`,
    icons: false,
    components: ['BNavbar', 'BNavbarToggle', 'BNavbarNav', 'BNavItem', 'BNavText', 'BNavbarBrand', 'BCollapse',
      'BButton', 'BLink', 'BModal', 'BOverlay', 'BBreadcrumb', 'BAlert', 'BSpinner', 'BImg', 'BProgress', 'BPagination',
      'BForm', 'BFormSelect', 'BFormGroup', 'BFormSelectOption',
      'BFormInput', 'BFormCheckbox', 'BFormRadio', 'BFormRating', 'BFormTextarea', 'BFormFile',
      'BInputGroup', 'BInputGroupAppend', 'BFormInvalidFeedback', 'BFormValidFeedback',
      'BDropdown', 'BDropdownItem',
      'BTabs', 'BTab', 'BBadge',
      'BMedia', 'BMediaAside', 'BMediaBody'],
    componentPlugins: ['ModalPlugin'], // https://bootstrap-vue.org/docs#tree-shaking-with-nuxtjs
    directives: ['VBToggle', 'VBModal']
  },
  // Axios module configuration (https://go.nuxtjs.dev/config-axios)
  axios: {
    baseURL: process.env.AXIOS_BASE_URL || 'http://localhost:3000/api',
    progress: true,
    timeout: 5000
  },

  toast: {
    position: 'bottom-right',
    duration: 3000
  },

  recaptcha: {
    version: 2
  },

  sentry: {
    dsn: process.env.SENTRY_DSN,
    tracing: {
      tracesSampleRate: parseFloat(process.env.SENTRY_SAMPLE_RATE) || 1,
      vueOptions: {
        tracing: true
      }
    },
    config: {
      environment: process.env.SENTRY_ENVIRONMENT || 'develop'
    }
  },

  gtm: {
    debug: process.env.APP_ENV === 'dev',
    enabled: true,
    pageTracking: true
  },
  // robots: {
  //   /* module options */
  //   UserAgent: 'FacebookBot',
  //   CrawlDelay: 5,
  //   Sitemap: req => `https://${req.headers.host}/api/public/sitemap.xml`
  // },

  /*
   ** Nuxt runtime config
   ** See https://nuxtjs.org/blog/moving-from-nuxtjs-dotenv-to-runtime-config/
   */
  publicRuntimeConfig: {
    appEnv: process.env.APP_ENV || 'production',
    runtype: process.env.RUN_TYPE || 'build',
    baseUrl: process.env.BASE_URL || 'http://localhost:3000',
    axios: {
      // Use BROWSER_BASE_URL to support relative path in proxy
      // https://axios.nuxtjs.org/options#browserbaseurl
      browserBaseURL: process.env.BROWSER_BASE_URL || 'http://localhost:3000/api'
    },
    baseImageUrl: process.env.BASE_IMAGE_URL || '/assets/images',
    baseImageUrlVN: process.env.BASE_IMAGE_URL_VN || process.env.BASE_IMAGE_URL || '/assets/images',
    trackingEndpointUrl: process.env.TRACKING_ENDPOINT_URL || 'https://logs.dev.senprints.net/bz',
    newTrackingEndpointUrl: process.env.NEW_TRACKING_ENDPOINT_URL || 'https://newlogs.dev.senprints.net/bz',
    googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || '',
    googleAnalytics: process.env.GOOGLE_ANALYTICS || '',
    hasWriteApi: true, // todo: remove later
    xDomain: process.env.X_DOMAIN || null,
    sentryDNS: process.env.SENTRY_DSN, // todo: remove later
    recaptcha: {
      siteKey: '6LdO3NwqAAAAAGHNjYSPjC_IoMfbE7nYez89mzg9'
    },
    gtm: {
      id: process.env.GOOGLE_TAG_MANAGER_ID || ''
    },
    personalBridgeUrl: process.env.PERSONAL_BRIDGE_URL || 'https://apis.personalbridge.com/senprints.js',
    publicPath: process.env.PUBLIC_PATH || '',
    sentry: {
      config: {
        environment: process.env.SENTRY_ENVIRONMENT || 'develop',
        dsn: process.env.SENTRY_DSN,
        sampleRate: parseFloat(process.env.SENTRY_SAMPLE_RATE) || 1
      },
      serverConfig: {
        // Any server-specific config
      },
      clientConfig: {
        // Any client-specific config
      }
    },
    appRegion: process.env.APP_REGION || 'us',
    cdnUrl: process.env.CDN_URL || 'https://cdn.cloudimgs.net'
  },

  loading: {
    color: '#29d',
    height: '2px'
  },

  proxy: {
    '/api2/': {
      target: process.env.PROXY_API_URL2 || process.env.PROXY_API_URL || 'https://apis.dev.senprints.net',
      pathRewrite: {
        '^/api2/': '/'
      },
      onProxyReq (proxyReq, req, res, options) {
        const checkIp = proxyReq.getHeader('cf-connecting-ip')
        if (checkIp) {
          proxyReq.setHeader('sp-connecting-ip', checkIp)
        }
      }
    },
    '/api/': {
      target: process.env.PROXY_API_URL || 'https://apis.dev.senprints.net',
      pathRewrite: {
        '^/api/': '/'
      },
      onProxyReq (proxyReq, req, res, options) {
        const checkIp = proxyReq.getHeader('cf-connecting-ip')
        if (checkIp) {
          proxyReq.setHeader('sp-connecting-ip', checkIp)
        }
      }
    },
    '/apistats/': {
      target: process.env.STATS_API_URL || process.env.PROXY_API_URL || 'https://apis.dev.senprints.net',
      pathRewrite: {
        '^/apistats/': '/'
      }
    }
  },

  // Build Configuration (https://go.nuxtjs.dev/config-build)
  build: {
    // custom webpack config
    // https://nuxtjs.org/docs/2.x/configuration-glossary/configuration-build#extend
    extend (config) {
      // https://github.com/peaksandpies/universal-analytics/issues/58#issuecomment-279485637
      config.node = {
        console: true,
        fs: 'empty',
        net: 'empty',
        tls: 'empty'
      }
    },
    loaders: {
      vue: {
        compilerOptions: {
          whitespace: 'condense'
        }
      }
    },
    extractCSS: process.env.RUN_TYPE === 'build',
    publicPath: process.env.PUBLIC_PATH ? `${process.env.PUBLIC_PATH}/_nuxt` : false
  },

  router: {
    base: '/',
    extendRoutes (routes, resolve) {
      routes.push({
        name: 'custom',
        path: '/custom/:type/*',
        component: resolve(__dirname, 'pages/custom/_type/_slug.vue')
      })
      routes.push({
        name: 'custom-2',
        path: '/custom-:type/*',
        component: resolve(__dirname, 'pages/custom/_type/index.vue')
      })
    }
  },

  pingz: {
    path: '/ping',
    contentType: 'application/json',
    healthy: () => {
      return JSON.stringify({ result: 'pong' })
    }
  },
  healthcheck: {
    path: '/healthcheck',
    contentType: 'application/json',
    healthy: () => {
      return JSON.stringify({ status: true })
    }
  }
}
