<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  name: 'HeaderWraper',
  computed: {
    asyncComponent
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/components/order/PageHeaderOrder`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/components/order/PageHeaderOrder'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/components/order/PageHeaderOrder'
      )
  }
}
</script>
