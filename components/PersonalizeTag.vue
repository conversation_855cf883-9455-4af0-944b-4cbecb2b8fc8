<template>
  <div
    class="personalize-tag border-radius-none"
    :role="(clickFocusPesronalizeInput) ? 'button' : ''"
    @click="onclickFocusPersonalizeInput"
  >
    {{ $t('Personalize it!') }}
    <i class="icon-sen-lead-pencil" />
  </div>
</template>

<script>
export default {
  name: 'PersonalizeTag',
  props: {
    clickFocusPesronalizeInput: {
      default: false,
      type: Boolean,
    },
  },
  methods: {
    onclickFocusPersonalizeInput () {
      if (!this.clickFocusPesronalizeInput) { return }

      const bottomInput = document.querySelector('.personalize input#bottomCustomTextInput')
      if (bottomInput && bottomInput?.offsetParent) {
        bottomInput.focus()
        return
      }

      document.querySelector('.personalize input.form-control')?.focus()
    }
  }
}

</script>
<style lang="scss" scoped>
.personalize-tag {
  font-size: 12px;
  padding: 4px 10px;
  color: white;
  background-color: var(--primary-color);
  opacity: 0.8;
  position: absolute;
  top: 0;
  left: 0;

  i {
    padding-left: 3px;
  }

  &.size-sm {
    font-size: 10px;
    padding: 3px 5px;
  }

  @media (min-width: 768px) {
    padding: 8px 20px;
    font-size: 16px;
  }
}

.modal .personalize-tag {
  font-size: 12px;
  padding: 3px 7px;
}
</style>
