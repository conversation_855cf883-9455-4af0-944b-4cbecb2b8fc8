<template>
  <div
    class="zoom-on-hover"
    :class="{zoomed}"
    @touchstart="touchzoom"
    @mousemove="move"
    @mouseenter="zoom"
    @mouseleave="unzoom"
  >
    <product-img
      ref="normal"
      class="normal"
      :path="imgNormal"
      :type="type"
      :alt="altText"
      :color="color"
    />
    <product-img
      ref="zoom"
      class="zoom"
      :path="imgZoom || imgNormal"
      :type="type"
      :alt="altText"
      :color="color"
    />
  </div>
</template>

<script>
export default {
  name: 'ZoomOnHover',
  // eslint-disable-next-line vue/require-prop-types
  props: ['imgNormal', 'imgZoom', 'scale', 'disabled', 'type', 'altText', 'color'],
  data () {
    return {
      scaleFactor: 1,
      resizeCheckInterval: null,
      zoomed: false
    }
  },
  mounted () {
    if (this.scale) {
      this.scaleFactor = parseInt(this.scale)
      this.$refs.zoom.$refs.image.style.transform = 'scale(' + this.scaleFactor + ')'
    }
    this.initEventLoaded()
    this.initEventResized()
  },
  updated () {
    this.initEventLoaded()
  },
  beforeDestroy () {
    this.resizeCheckInterval && clearInterval(this.resizeCheckInterval)
  },
  methods: {
    touchzoom (event) {
      if (this.disabled) { return }
      this.move(event)
      this.zoomed = !this.zoomed
    },
    zoom () {
      if (!this.disabled && window.innerWidth > 767) { this.zoomed = true }
    },
    unzoom () {
      if (!this.disabled && window.innerWidth > 767) { this.zoomed = false }
    },
    move (event) {
      try {
        if (this.disabled || !this.zoomed) { return }
        const offset = pageOffset(this.$el)
        const zoom = this.$refs.zoom.$refs.image
        const normal = this.$refs.normal.$refs.image
        const relativeX = event.clientX - offset.x + window.pageXOffset
        const relativeY = event.clientY - offset.y + window.pageYOffset
        const normalFactorX = relativeX / normal.offsetWidth
        const normalFactorY = relativeY / normal.offsetHeight
        const x = normalFactorX * (zoom.offsetWidth * this.scaleFactor - normal.offsetWidth)
        const y = normalFactorY * (zoom.offsetHeight * this.scaleFactor - normal.offsetHeight)
        zoom.style.left = -x + 'px'
        zoom.style.top = -y + 'px'
      } catch (error) {
        console.log(error)
      }
    },
    initEventLoaded () {
      // emit the "loaded" event if all images have been loaded
      const promises = [this.$refs.zoom.$refs.image, this.$refs.normal.$refs.image].map(function (image) {
        return new Promise(function (resolve, reject) {
          image.addEventListener('load', resolve)
          image.addEventListener('error', reject)
        })
      })
      const component = this
      Promise.all(promises).then(function () {
        component.$emit('loaded')
      })
    },
    initEventResized () {
      const normal = this.$refs.normal.$refs.image
      let previousWidth = normal.offsetWidth
      let previousHeight = normal.offsetHeight
      const component = this
      this.resizeCheckInterval = setInterval(function () {
        if ((previousWidth !== normal.offsetWidth) || (previousHeight !== normal.offsetHeight)) {
          previousWidth = normal.offsetWidth
          previousHeight = normal.offsetHeight
          component.$emit('resized', {
            width: normal.width,
            height: normal.height,
            fullWidth: normal.naturalWidth,
            fullHeight: normal.naturalHeight
          })
        }
      }, 1000)
    }
  }

}

function pageOffset (el) {
  // -> {x: number, y: number}
  // get the left and top offset of a dom block element
  const rect = el.getBoundingClientRect()
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  return {
    y: rect.top + scrollTop,
    x: rect.left + scrollLeft
  }
}
</script>

<style>
.zoom-on-hover {
  position: relative;
  overflow: hidden;
  height: 100%;
  cursor: zoom-in;
}

.zoom-on-hover .normal img {
  width: 100%;
}

.zoom-on-hover .zoom {
  position: absolute;
  top: 0;
  left: 0;
}

.zoom-on-hover .zoom img {
  position: absolute;
  opacity: 0;
  transform-origin: top left;
}

@media (min-width: 768px) {
  .zoom-on-hover.zoomed .zoom img {
    opacity: 1;
  }

  .zoom-on-hover.zoomed .normal img {
    opacity: 0;
  }
}

</style>
