<template>
  <span class="img-thumb">
    <span v-if="alternativeLabel">View image</span>
    <img
      v-else
      :src="$imgUrl(url) || 'images/no-image.png'"
      :width="width"
      :height="height"
      style="background-color: #a9a9a9;"
      class="img-thumbnail img-fluid thumbnail"
      loading="lazy"
      alt="SenPrints2"
      onerror="this.onerror=null;this.src='images/no-image.png';"
    >
    <div class="zoom-thumb">
      <img
        :src="$imgUrl(url, 'product-review-image') || 'images/no-image.png'"
        style="background-color: #a9a9a9;"
        class="img-thumbnail img-fluid thumbnail"
        loading="lazy"
        alt="SenPrints"
        onerror="this.onerror=null;this.src='images/no-image.png';"
      >
    </div>
  </span>
</template>

<script>
export default {
  name: 'HoverImg',
  props: {
    url: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 40
    },
    height: {
      type: Number,
      default: 40
    },
    color: {
      type: String,
      default: null
    },
    alternativeLabel: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style scoped>

.img-thumb {
  position: relative;
}

.img-thumb img {
  background: darkgrey;
  padding: 0;
}

.zoom-thumb {
  display: none;
  position: absolute;
  z-index: 100;
  top: 50%;
  transform: translateY(-50%);
}

.zoom-thumb > img {
  min-width: 400px;
}

.img-thumb:hover .zoom-thumb {
  display: inline-block;
  z-index: 101;
}

.img-thumbnail {
  background-color: lightgrey;
}

</style>
