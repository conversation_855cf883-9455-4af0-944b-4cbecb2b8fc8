<template>
  <div class="expandImage-container d-flex justify-content-center" style="cursor: pointer;">
    <icon-expand width="20" height="20" class="expand-icon" />
  </div>
</template>
<script>
export default {
  name: 'ExpandImage',
}
</script>
<style scoped>
.expandImage-container {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 7px;
  border-radius: 9999px;
  z-index: 1;
  background-color: rgb(209, 213, 219);
}

.expandImage-container:hover {
  background-color: rgb(156, 163, 175);
}
</style>
