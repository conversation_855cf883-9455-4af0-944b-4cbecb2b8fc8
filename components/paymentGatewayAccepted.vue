<template>
  <div>
    <p class="font-small font-weight-bold mt-3 mb-1 text-uppercase">
      {{ $t('We accept') }}:
    </p>
    <div class="d-flex justify-content-center justify-content-lg-start">
      <div v-for="name in paymentsGatewayTypeAccept" :key="name" class="icon-card pr-2">
        <i :class="`payments-sprite payments-sprite-${name}`" />
      </div>
    </div>
  </div>
</template>

<script>
import { PAYMENT_GATEWAY_TYPE } from '~/helpers/variableConst'

export default {
  name: 'PaymentGatewayAccepted',
  computed: {
    spriteUrl () {
      return `--sprite-url: url("${this.$config.publicPath}/images/logo_cart_sprite.webp")`
    },
    paymentsGatewayTypeAccept () {
      const paymentGatewayType = this.$store.state.storeInfo?.payment_gateway_type || ''
      if (paymentGatewayType === PAYMENT_GATEWAY_TYPE.BOTH) {
        return ['paypal', 'visa', 'master', 'amex', 'sofort', 'giropay']
      } else if (paymentGatewayType === PAYMENT_GATEWAY_TYPE.STRIPE) {
        return ['visa', 'master', 'amex', 'sofort', 'giropay']
      } else if (paymentGatewayType === PAYMENT_GATEWAY_TYPE.PAYPAL) {
        return this.$store.state.storeInfo?.paypal_enable_card ? ['paypal', 'visa', 'master', 'amex', 'sofort', 'giropay'] : ['paypal']
      } else {
        return ['paypal', 'visa', 'master', 'amex', 'sofort', 'giropay']
      }
    }
  }
}
</script>

<style scoped>
.icon-card {
  height: 25px;
}

.payments-sprite {
  --scale-fac: calc(85.5 / 25); /* scaling factor for height */

  display: block;
  border: 1px solid #d3d3d3;
  height: inherit;
  width: calc(150px / var(--scale-fac) + 8px);
  background: var(--sprite-url) no-repeat;
  background-size: calc(150px / var(--scale-fac));
  background-position: center calc((var(--icon-position) * 25px + 0.5px) * -1);
}

.payments-sprite-amex { --icon-position: 0; }
.payments-sprite-paypal { --icon-position: 1; }
.payments-sprite-visa { --icon-position: 2; }
.payments-sprite-master { --icon-position: 3; }
.payments-sprite-sofort { --icon-position: 4; }
.payments-sprite-giropay { --icon-position: 5; }
</style>
