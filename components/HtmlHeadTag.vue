<script>
export default {
  name: 'HeadTag',
  props: {
    forPosition: {
      type: String,
      default: ''
    }
  },
  computed: {
    headTags () {
      if (!this.forPosition) {
        return []
      }

      const rawTags = this.$store.getters['storeInfo/getStoreHeadTags'][this.forPosition]
      if (!rawTags) {
        return []
      }
      const tags = this.$headTagFilter(rawTags)

      return [...tags].sort((a, b) => b.priority - a.priority)
    }
  },
}
</script>
<template>
  <div v-if="headTags">
    <div v-for="tag in headTags" :key="tag.name" v-html="tag.code" />
  </div>
</template>
