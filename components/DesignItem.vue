<template>
  <div class="canvas-item position-relative">
    <div ref="editDesignBox" class="edit-design-box" />
    <div v-if="customImage" class="control-box">
      <div class=" d-flex justify-content-center">
        <button
          v-for="control in imageControlList"
          :key="control.key"
          type="button"
          class="btn border control-custom-image-btn m-1 btn-outline-custom"
          @click="controlCustomImage(control.key)"
          @mousedown="startIntevalControl(control.key)"
          @mouseleave="removeIntervalControl"
          @mouseup="removeIntervalControl"
        >
          <i :class="control.icon" />
        </button>
      </div>
      <p v-if="isImageLowQuanlity" class="text-danger text-center my-3">
        <span>
          {{ $t('Image resolution is low. Please upload higher resolution image, minimum size is heightxwidth px', {height: Math.round(customImage && customImage.height * customImage.scaleY / 2) , width: Math.round(customImage && customImage.width * customImage.scaleX / 2)}) }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import controlCustomImage from '~/mixins/controlCustomImage'

export default {
  name: 'DesignItem',
  mixins: [controlCustomImage],
  // eslint-disable-next-line vue/require-prop-types
  props: ['canvas'],
  data () {
    return {
      isLoading: false,

      imageControlList: [{
        key: 'zoomOut',
        icon: 'icon-sen-magnify-minus-outline'
      }, {
        key: 'zoomIn',
        icon: 'icon-sen-magnify-plus-outline'
      }, {
        key: 'rollLeft',
        icon: 'icon-sen-refresh'
      }, {
        key: 'moveRight',
        icon: 'icon-sen-arrow-right-thin'
      }, {
        key: 'moveLeft',
        icon: 'icon-sen-arrow-left-thin'
      }, {
        key: 'moveDown',
        icon: 'icon-sen-arrow-down-thin'
      }, {
        key: 'moveUp',
        icon: 'icon-sen-arrow-up-thin'
      }],
      timeoutControl: null,
      intervalControll: null
    }
  },
  watch: {
    canvas: loadCanvas
  },
  mounted: loadCanvas,
  methods: {
    reRender
  }
}

function loadCanvas () {
  if (this.canvas) {
    this.$refs.editDesignBox.innerHTML = ''
    this.$refs.editDesignBox.appendChild(this.canvas.wrapperEl)
    this.canvas.setDisplaySize(this.$refs.editDesignBox.clientWidth)
    if (!this.canvas.isLockEdit) {
      this.canvas.lockEdit()
    }
    this.checkScale()
  }
}

function reRender () {
  this.canvas.renderAll()
}
</script>

<style scoped>
.canvas-item {
  width: 100%;
}
</style>
