<template>
  <div id="sitewide" ref="sitewide" v-html="$store.state.storeInfo.sitewide_banner" />
</template>

<script>
export default {
  name: 'Sitewide',
  mounted () {
    this.autoTextColor()
  },
  methods: {
    autoTextColor () {
      // auto set the text color based on the background color
      // to make sure the text is always visible
      const sitewideEl = this.$refs.sitewide

      if (!sitewideEl) {
        return
      }

      try {
        sitewideEl.style.color = this.getContrastYIQ(this.$store.state.storeInfo.default_color)
      } catch (e) {
        // ignore
      }
    },
    getContrastYIQ (hexcolor) {
      // https://stackoverflow.com/a/11868398
      const r = parseInt(hexcolor.substring(1, 3), 16)
      const g = parseInt(hexcolor.substring(3, 5), 16)
      const b = parseInt(hexcolor.substring(5, 7), 16)
      const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000
      return (yiq >= 128) ? 'black' : 'white'
    }
  }
}
</script>

<style lang="scss">
#sitewide {
  background-color: var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  text-align: center;
  font-size: 14px;
  position: relative;
  z-index: 1030;

  @media (min-width: 768px) {
    z-index: 99999;
  }

  * {
    padding: 0;
    margin: 0;
  }

  strong {
    font-weight: 600;
  }
}
</style>
