<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  name: 'HeaderWraper',
  computed: {
    asyncComponent
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/components/common/PageHeader`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/components/common/PageHeader'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/components/common/PageHeader'
      )
  }
}
</script>
