<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
}
</script>

<template>
  <div class="cc-wrapper">
    <div class="cc-left">
      <div>
        <svg
          focusable="false"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 48 48"
          class="cc-icon text-color-primary"
        ><path d="M24 2.01c12.072 0 22 9.81 22 22s-9.81 22-22 22-22-9.81-22-22 9.81-22 22-22ZM28 20H18v4h4v10h-4v4h14v-4h-4V20Zm0-10h-6v6h6v-6Z" class="wt-noconflict" />
        </svg>
      </div>
      <div>
        {{ $t('Your privacy and experience are our top priorities! We use cookies on our site to ensure you get the most efficient and personalized shopping experience. Cookies help us understand user behavior, improve our website, and offer content tailored to your interests.') }}
      </div>
    </div>
    <div class="cc-right">
      <button class="btn btn-custom" style="white-space: nowrap;" @click="$emit('confirm')">
        {{ $t('Accept all cookies') }}
      </button>
      <button data-test-id="cs_essential" class="btn btn-custom" style="white-space: nowrap;" @click="$emit('confirm')">
        {{ $t('Accept only essential cookies') }}
      </button>
    </div>
  </div>
</template>
<style scoped>
.cc-icon {
  width: 40px;
  height: 40px;
  margin-right: 16px;
  fill: currentColor;
}

.cc-left {
  display: flex;
  align-items: center;
}

.cc-right {
  display: flex;
  align-items: center;
  gap: 16px;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
  }
}

.cc-wrapper {
  display: flex;
  gap: 16px;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px;
  z-index: 9999;
  background-color: #e5e7eb;

  @media (max-width: 768px) {
    flex-wrap: wrap;
  }
}
</style>
