<template>
  <div class="cs-policy-wrapper">
    <a
      v-for="policy in policies"
      :key="policy.slug"
      :href="localePath(`/page/${policy.slug}`)"
      class="cs-policy-text text-info"
      @click.prevent="$emit('show', policy.slug)"
      v-text="$t(policy.title)"
    />
  </div>
</template>

<script>
export default {
  name: 'PolicyGroup',
  data () {
    return {
      policies: [
        {
          title: 'Return policy',
          slug: 'return-policy'
        },
        {
          title: 'Shipping policy',
          slug: 'shipping-policy'
        },
        {
          title: 'Privacy policy',
          slug: 'privacy'
        }
      ]
    }
  }
}
</script>

<style scoped>
.cs-policy-wrapper {
  display: flex;
}

.cs-policy-text {
  font-size: 0.75rem;
  line-height: 1rem;
  margin-right: 1rem;
}
</style>
