<template>
  <div v-if="isMobile !== undefined">
    <div style="height: 100%; width: 100%; position: absolute; top: 0; left: 0; display: flex; align-items: center;">
      <video-player
        class="vjs-custom-skin d-flex justify-content-center align-items-center w-100 h-100"
        :options="playerOptions"
        @play="onPlayerPlay"
        @pause="onPlayerPause"
        @ended="onPlayerEnded"
        @ready="onPlayerReady"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    videoUrl: {
      type: String,
      required: true,
    },
    thumbnailUrl: {
      type: String,
      default: null,
    },
    autoplay: {
      type: Boolean,
      default: true,
    },
  },
  data () {
    return {
      isMobile: undefined,
    }
  },
  computed: {
    playerOptions () {
      return {
        muted: true,
        loop: true,
        autoplay: this.autoplay && !this.isMobile,
        fluid: false,
        controls: true,
        controlBar: {
          children: [
            'playToggle',
            'progressControl',
            'fullscreenToggle',
          ],
        },
        sources: [
          {
            type: 'video/mp4',
            src: this.videoUrl,
          },
        ],
        poster: this.thumbnailUrl ? this.$imgUrl(this.thumbnailUrl, 'full') : null,
      }
    },
  },
  mounted () {
    const match = window.matchMedia('(pointer:coarse)')
    this.isMobile = match && match.matches
  },
  methods: {
    onPlayerPlay () {
      console.log('Video is playing')
    },
    onPlayerPause () {
      console.log('Video is paused')
    },
    onPlayerEnded () {
      console.log('Video has ended')
    },
    onPlayerReady () {
      console.log('Player is ready')
    }
  },
}
</script>

<style>
.video-js {
  width: 100% !important;
  height: 100% !important;
  background: transparent !important;
}

.vjs-poster {
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}
</style>
