<template>
  <div :key="forceRender" class="mockup-canvas-item position-relative">
    <div id="preview_mockup" ref="preview_mockup" class="three-d-canvas-container" />

    <!-- Navigation Arrows -->
    <div v-if="showNavigation" class="mockup-navigation-arrows">
      <div
        class="border-radius-none mockup-arrow prev-arrow transition-fade-1"
        @click="goToPrev"
      >
        <i class="icon-sen-chevron-left absolute-center" />
      </div>
      <div
        class="border-radius-none mockup-arrow next-arrow transition-fade-1"
        @click="goToNext"
      >
        <i class="icon-sen-chevron-right absolute-center" />
      </div>
    </div>

    <div v-if="isLoading" class="absolute-center">
      <b-spinner variant="gray" />
    </div>
  </div>
</template>

<script>
import controlCustomImage from '~/mixins/controlCustomImage'
export default {
  name: 'MockupCan<PERSON>',
  mixins: [controlCustomImage],
  // eslint-disable-next-line vue/require-prop-types
  props: ['isModal', 'mockupData', 'design', 'color', 'designData', 'mockupWidth', 'type', 'showNavigation'],
  data () {
    return {
      forceRender: 1,
      threeDMockupCanvas: false,
      isLoading: true,
      timeoutControl: null,
      intervalControll: null
    }
  },
  watch: {
    designData: loadMockup,
    color: changeColor
  },
  mounted: loadMockup,
  beforeDestroy,
  methods: {
    loadMockup,
    loadDesign,
    changeColor,
    reRender,
    goToNext,
    goToPrev
  }
}

function beforeDestroy () {
  window.removeEventListener('resize', this.resetMockupSize)
}

function loadDesign () {
  if (this.type === 1) { // personalized
    if (this.design && this.threeDMockupCanvas) {
      const activeMockup = this.threeDMockupCanvas.getActiveMockup()
      if (activeMockup) {
        // Initial load if design has content
        if (this.design.hasDesign && this.design.hasDesign()) {
          activeMockup.updateDesignCanvas(this.design.exportCanvas())
        }

        // Set up the update callback
        this.design.onupdate = (design) => {
          if (design.exportCanvas) {
            activeMockup.updateDesignCanvas(design.exportCanvas())
          }
        }
      }
    }
  } else if (this.type === 2) {
    // Full printed design
    if (this.design && this.threeDMockupCanvas) {
      const activeMockup = this.threeDMockupCanvas.getActiveMockup()
      if (activeMockup) {
        activeMockup.updateDesignCanvas(this.design)
      }
    }
  }
  this.$emit('finishLoadingDesign')
}

function changeColor () {
  if (this.threeDMockupCanvas) {
    const activeMockup = this.threeDMockupCanvas.getActiveMockup()
    if (activeMockup && activeMockup.changeColor) {
      activeMockup.changeColor(this.color)
    }
  }
}

function loadMockup () {
  this.forceRender++
  this.$nextTick(async () => {
    this.isLoading = true
    this.threeDMockupCanvas = new this.$MockupManager('preview_mockup')
    const el = document.getElementById('viewBox')
    const mockupParams = {
      id: this.mockupData.id,
      name: 'default',
      maxSize: el?.clientWidth * 1.25 || 500,
      designImg: this.$imgUrl(this.designData.file_url, 'full_hd'),
      background: this.$imgUrl(this.mockupData.file_url, 'full_hd'),
      color: this.color ?? '#ffffff',
      mockupType: this.designData.type_detail ?? 'flat',
    }

    this.threeDMockupCanvas.locked = true
    this.$emit('updateMockup', this.threeDMockupCanvas)
    if (this.mockupData && this.mockupData.design_json) {
      const designJson = JSON.parse(this.mockupData.design_json)
      mockupParams.name = designJson.name
      mockupParams.colorImg = this.$imgUrl(designJson.color, 'list')
      mockupParams.cropImg = this.$imgUrl(designJson.crop, 'list')
      mockupParams.shadowImg = this.$imgUrl(designJson.shadow, 'list')
      mockupParams.glb = designJson.glb ? this.$config.cdnUrl + '/' + designJson.glb : ''
      await this.threeDMockupCanvas.loadMockup(mockupParams)
      this.threeDMockupCanvas.setActiveMockup(mockupParams.id)
    }

    this.loadDesign()
    this.isLoading = false
  })
}

function goToNext () {
  this.$emit('nextMockup')
}

function goToPrev () {
  this.$emit('prevMockup')
}

function reRender () {
  if (this.design && this.design.renderAll) {
    this.design.renderAll()
  }
  if (this.design && this.design._onupdate) {
    this.design._onupdate()
  }
}
</script>

<style lang="scss">
.control-custom-image-btn {
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;

  i {
    font-size: 1.5rem;
  }

  @media (min-width: 768px) {
    height: 40px;
    width: 40px;

    i {
      font-size: 2rem;
    }
  }
}

.three-d-canvas-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  canvas {
    max-width: 100%;
    height: auto;
    display: block;
  }
}

.thumb-canvas,
.mockup-canvas-item {
  height: 100%;
}

.mockup-canvas-item {
  width: 100%;
}

.mockup-navigation-arrows {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 2;
}

.mockup-arrow {
  color: transparent;
  position: absolute;
  cursor: pointer;
  z-index: 1;
  height: 40px;
  width: 40px;
  top: 50%;
  font-size: 2rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  pointer-events: auto;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.3);

  &.prev-arrow {
    left: 10px;
  }

  &.next-arrow {
    right: 10px;
  }

  i {
    line-height: 40px;
    color: white;
  }
}
</style>
