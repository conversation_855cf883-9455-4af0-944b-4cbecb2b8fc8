<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  name: 'FooterWraper',
  computed: {
    asyncComponent
  }
}
function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/components/order/PageFooterOrder`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/components/order/PageFooterOrder'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/components/order/PageFooterOrder'
      )
  }
}
</script>
