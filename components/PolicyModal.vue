<template>
  <b-modal
    centered
    hide-footer
    :visible="modal != null"
    size="xl"
    scrollable
    @hidden="$emit('close')"
  >
    <template #modal-title>
      {{ modal && modal.title }}
    </template>
    <div v-html="modal && modal.content" />
  </b-modal>
</template>

<script>

export default {
  name: 'PolicyModal',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      modal: null
    }
  },
  async created () {
    const { success, data } = await this.$store.dispatch('getPage', this.type)

    if (success) {
      this.modal = data
    } else {
      this.modal = {
        title: 'Error',
        content: 'There was an error loading the policy.'
      }
    }
  }
}
</script>
