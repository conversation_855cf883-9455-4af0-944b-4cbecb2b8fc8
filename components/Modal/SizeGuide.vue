<template>
  <b-modal
    id="modalSizeGuide"
    v-model="isShowModalSizeGuide"
    centered
    hide-footer
    size="lg"
    :title="currentTemplate && currentTemplate.name || ''"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalSizeGuide', null)"
    @hidden="$store.commit('updateModalSizeGuide', false); $tracking.newCustomTracking('modal-hidden', null, 'modalSizeGuide', null)"
  >
    <div class="row p-3 overflow-auto">
      <div class="col-12 col-lg-4">
        <div class="d-flex justify-content-center">
          <img
            class="sizeguide-img"
            :src="currentTemplate && currentTemplate.size_chart ? $imgUrl(currentTemplate.size_chart, 'list', 'webp'): `${$config.publicPath}/images/size_guide_sleeve.png`"
            alt="size guide"
            loading="lazy"
          >
        </div>
        <div class="row cursor-pointer text-center py-3 px-3">
          <div class="sizeguide-unit col-6" :class="{active: currentSizeGuideUnit==='in'}" @click="currentSizeGuideUnit = 'in'">
            {{ $t('Inches') }}
          </div>
          <div class="sizeguide-unit col-6" :class="{active: currentSizeGuideUnit==='cm'}" @click="currentSizeGuideUnit = 'cm'">
            {{ $t('Centimeters') }}
          </div>
        </div>
      </div>
      <div v-if="sizeGuideList && sizeGuideList.length" class="col-12 col-lg-8 p-0 p-lg-2">
        <table class="w-100 table table-bordered table-striped table-hover">
          <thead class="bg-info text-white">
            <tr>
              <th>{{ $t('Size') }}</th>
              <template v-for="sizeGuideProperty in sizeGuideProperties">
                <th v-if="sizeGuideList.some(item=>item[sizeGuideProperty.key])" :key="sizeGuideProperty.key">
                  {{ $t(sizeGuideProperty.title) }}
                </th>
              </template>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(sizeGuideItem, index) in sizeGuideList" :key="index">
              <td>{{ sizeGuideItem.size }}</td>
              <template v-for="sizeGuideProperty in sizeGuideProperties">
                <td v-if="sizeGuideList.some(item=>item[sizeGuideProperty.key])" :key="sizeGuideProperty.key">
                  {{ converseData(sizeGuideProperty.key, sizeGuideItem[[sizeGuideProperty.key]], sizeGuideItem.unit) }}
                </td>
              </template>
            </tr>
          </tbody>
        </table>
        <div class="mt-3">
          <span class="font-weight-500">{{ $t('Note') }}: </span>
          <span v-if="currentSizeGuideUnit === 'in'">{{ $t('Allow for a tolerance level of 2 inch') }}</span>
          <span v-else>{{ $t('Allow for a tolerance level of 5 cm') }}</span>
        </div>
      </div>
      <div class="mt-4">
        <span class="font-weight-500">{{ $t('Note: The size guide for most clothing products is as follows:') }}</span>
        <ul class="mt-2">
          <li>{{ $t('Inches are used for customers from North America (US and CA), Australia, and New Zealand.') }}</li>
          <li>{{ $t('Centimeters are used for customers from all other regions.') }}</li>
        </ul>
        <span class="mt-2">{{ $t('In cases of limited stock where products are shipped from Europe to North America or vice versa, we will send an email to inform customers and advise them to double-check the applicable size chart.') }}</span>
      </div>
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'ModalSizeGuide',
  data () {
    return {

      isShowModalSizeGuide: false,
      currentSizeGuideUnit: 'in',
      sizeGuideProperties: [{
        key: 'width',
        title: 'Chest Width'
      }, {
        key: 'length',
        title: 'Shirt Length'
      }, {
        key: 'sleeve',
        title: 'Sleeve Length'
      }, {
        key: 'height',
        title: 'Height'
      }, {
        key: 'weight',
        title: 'Weight'
      }, {
        key: 'waist',
        title: 'Waist'
      }, {
        key: 'hip ',
        title: 'Hip'
      }, {
        key: 'note',
        title: 'Note'
      }]
    }
  },
  computed: {
    currentTemplate,
    sizeGuideList,
    converseData
  },
  watch: {
    currentTemplate () {
      if (this.currentTemplate) {
        this.isShowModalSizeGuide = true
      }
    }
  }
}

function currentTemplate () {
  const visitInfo = this.$lscache.get('visitInfo')
  if (['BE', 'BG', 'CZ', 'DK', 'DE', 'EE', 'IE', 'EL', 'ES', 'FR', 'HR', 'IT', 'CY', 'LV', 'LT', 'LU', 'HU', 'MT', 'NL', 'AT', 'PL', 'PT', 'RO', 'SI', 'SK', 'FI', 'SE'].includes(visitInfo?.country)) {
    this.currentSizeGuideUnit = 'cm'
  }
  return this.$store.state.campaign.sizeGuideTemplate
}

function sizeGuideList () {
  const newSizeGuildList = this.currentTemplate.sizeGuideList?.filter(item => item.unit === this.currentSizeGuideUnit)
  if (newSizeGuildList && newSizeGuildList.length) {
    return newSizeGuildList
  } else {
    return this.currentTemplate.sizeGuideList
  }
}

function converseData () {
  return (key, value, unit) => {
    let converseSizeNumber = 1
    let converseWeightNumber = 1
    if (unit === 'cm' && this.currentSizeGuideUnit === 'in') {
      converseSizeNumber = 0.3937
      converseWeightNumber = 2.2046
    }
    if (unit === 'in' && this.currentSizeGuideUnit === 'cm') {
      converseSizeNumber = 2.54
      converseWeightNumber = 0.45359237
    }
    switch (key) {
      case 'width':
      case 'length':
      case 'sleeve':
      case 'waist':
      case 'hip':
        return `${this.currentSizeGuideUnit === unit ? value : Math.round(Number(value) * converseSizeNumber)} ${this.currentSizeGuideUnit}`
      case 'height':
        return `${this.currentSizeGuideUnit === unit ? value.replace(',', '-') : value.split(',').map(height => Math.round(Number(height) * converseSizeNumber)).join('-')} ${this.currentSizeGuideUnit}`
      case 'weight':
        return this.currentSizeGuideUnit === unit ? value.replace(',', '-') : `${value.split(',').map(weight => Math.round(Number(weight) * converseWeightNumber)).join('-')} kg`
      case 'note':
        return value || ''
    }
  }
}

</script>

<style scoped lang="scss">
.sizeguide-img {
  width: 100%;
  max-width: 300px;
}

.sizeguide-unit {
  border-bottom: 2px solid;
  padding: 0 !important;

  &.active {
    color: var(--primary-color);
  }
}
</style>
