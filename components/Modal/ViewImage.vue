<template>
  <b-modal
    id="modalViewImage"
    v-model="isShowModal"
    centered
    size="lg"
    hide-footer
    hide-header
    @hidden="$store.commit('changeViewImagePath', ''); $tracking.newCustomTracking('modal-hiden', null, 'modalViewImage', null)"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalViewImage', null)"
  >
    <span class="close-icon" @click="isShowModal = false"><i class="icon-sen-close-circle" /></span>
    <product-img :path="currentPath" type="full_hd" />
  </b-modal>
</template>

<script>
export default {
  name: 'ModalViewImage',
  data () {
    return {
      isShowModal: false
    }
  },
  computed: {
    currentPath
  },
  watch: {
    currentPath () {
      if (this.currentPath) {
        this.isShowModal = true
      }
    }
  }
}

function currentPath () {
  return this.$store.state.viewImagePath
}
</script>
<style lang="scss">
#modalViewImage {
  img {
    width: 100%;
    height: 100%;
  }

  .close-icon {
    cursor: pointer;
    position: absolute;
    right: 1rem;
    top: 0.5rem;
    font-size: 2rem;
  }
}
</style>
