<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  name: 'ModalCampaignWraper',
  computed: {
    asyncComponent
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/components/modalCampaign`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/components/modalCampaign'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/components/modalCampaign'
      )
  }
}
</script>
