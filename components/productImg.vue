<template>
  <div v-if="isLoading" ref="image" class="spinner d-flex justify-content-center align-items-center bg-white">
    <b-spinner variant="gray" />
  </div>
  <picture v-else>
    <source v-if="typeMobile" :srcset="currentMobilePath" media="(max-width: 767px)">
    <img
      ref="image"
      :key="pictureType"
      class="product-img"
      :src="currentPath"
      :loading="loadingType"
      :alt="alt"
      :title="alt"
      draggable="false"
      ondragstart="return false"
      @error="handerErrror"
    >
  </picture>
</template>

<script>
export default {
  name: 'ProductImage',
  // eslint-disable-next-line vue/require-prop-types
  props: ['path', 'type', 'alt', 'errorPath', 'color', 'hideLoading', 'typeMobile'],
  data () {
    return {
      pictureType: 1,
      isLoading: true,
      loadingType: 'lazy'
    }
  },
  computed: {
    currentPath,
    currentMobilePath
  },
  watch: {
    path: resetData,
    type: resetData
  },
  mounted: resetData,
  methods: {
    handerErrror,
    onload
  }
}

function currentPath () {
  // return static image
  if (this.path && this.path.startsWith('/images/')) {
    return this.path
  }
  if (this.pictureType === 1) {
    return this.$imgUrl(this.path, this.type || 'thumb', 'webp', this.color)
  } else if (this.pictureType === 2) {
    return this.$imgUrl(this.path, this.type || 'thumb', null, this.color)
  }
  if (this.errorPath) {
    return this.errorPath
  }
  return `${this.$config.publicPath}/images/no-image.webp`
}

function currentMobilePath () {
  // return static image
  if (this.path && this.path.startsWith('/images/')) {
    return this.path
  }
  if (this.pictureType === 1) {
    return this.$imgUrl(this.path, this.typeMobile || 'thumb', 'webp', this.color)
  } else if (this.pictureType === 2) {
    return this.$imgUrl(this.path, this.typeMobile || 'thumb', null, this.color)
  }
  if (this.errorPath) {
    return this.errorPath
  }
  return `${this.$config.publicPath}/images/no-image.webp`
}

function resetData () {
  this.isLoading = true
  if (this.type === 'full_hd') {
    this.loadingType = 'eager'
  }

  this.$nextTick(async () => {
    this.pictureType = 1
    await this.$preloadImg(this.path, this.type || 'thumb', this.color)
    this.isLoading = false
  })
}

function handerErrror () {
  if (this.pictureType <= 2) { this.pictureType += 1 }
}

function onload (e) {
  if (this.path && this.path.startsWith('/images/')) { return }
  this.isLoading = false
}
</script>
