<template>
  <div class="loading-dots" :class="`loading-dots-${variant}`">
    <div class="loading-dots--dot" />
    <div class="loading-dots--dot" />
    <div class="loading-dots--dot" />
    <div class="loading-dots--dot" />
  </div>
</template>

<script>
export default {
  name: 'LoadingDots',
  // eslint-disable-next-line vue/require-prop-types
  props: {
    variant: {
      type: String,
      require: false,
      default: 'custom'
    }
  }
}

</script>
<style lang="scss" scoped>
.loading-dots {
  display: inline;

  &--dot {
    animation: dot-keyframes 1.2s infinite ease-in-out;
    background-color: gray;
    border-radius: 5px;
    display: inline-block;
    height: 7px;
    width: 7px;
    margin: 3px;

    &:nth-child(2) {
      animation-delay: 0.3s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }

    &:nth-child(4) {
      animation-delay: 0.9s;
    }
  }

  &.loading-dots-light {
    .loading-dots--dot {
      background-color: #fff;
    }
  }

  &.loading-dots-custom {
    .loading-dots--dot {
      background-color: var(--primary-color);
    }
  }
}

@keyframes dot-keyframes {
  0% {
    opacity: 0.4;
    transform: scale(1, 1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2, 1.2);
  }

  100% {
    opacity: 0.4;
    transform: scale(1, 1);
  }
}
</style>
