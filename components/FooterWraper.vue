<template>
  <Component
    :is="asyncComponent"
    :is-blocked-store="isBlockedStore"
  />
</template>

<script>
export default {
  name: 'FooterWraper',
  props: {
    isBlockedStore: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    asyncComponent
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/components/common/PageFooter`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/components/common/PageFooter'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/components/common/PageFooter'
      )
  }
}
</script>
