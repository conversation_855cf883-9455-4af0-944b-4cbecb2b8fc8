<script>
export default {
  name: 'GenericReprintsRefundPolicy',
  // eslint-disable-next-line vue/require-prop-types
  props: ['orderNote'],
  computed: {
    hasOrderNote () {
      return this.orderNote && this.orderNote.trim().length > 0
    }
  }
}
</script>
<template>
  <div id="GenericReprintsRefundPolicy">
    <div v-if="hasOrderNote" class="grrp-wrapper mb-2 alert alert-secondary">
      <h6 class="grrp-title">
        {{ $t('Order Note') }}
      </h6>
      <div>
        <small>{{ orderNote }}</small>
      </div>
    </div>
    <div class="grrp-wrapper mb-2 alert alert-secondary">
      <h6 class="grrp-title">
        {{ $t('Shop with confidence') }}
      </h6>
      <div><small><strong>{{ $t('Shopping on {domain} are safe and secure. Guaranteed!', {domain: $getHost()}) }}</strong></small></div>
      <div><small>{{ $t("You'll pay nothing if unauthorized charges are made to your credit card as a result of shopping at {domain}", {domain: $getHost()}) }}</small></div>
    </div>
    <div class="grrp-wrapper alert alert-secondary">
      <h6 class="grrp-title">
        {{ $t('Generic reprint & refund policies') }}
      </h6>
      <div><small>{{ $t('If, for any encounter product quality issues or defects. We can offer reproductions or refunds for your orders if there are order mistakes.') }}</small></div>
      <a class="text-info" target="_blank" :href="localePath(`/page/return-policy`)">
        <small>{{ $t('Read our shipping and return policies') }}</small>
      </a>
    </div>
  </div>
</template>
<style scoped>
.grrp-title {
  text-transform: uppercase;
}

.grrp-wrapper {
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 0;
  background-color: #f6f7f9 !important;
}
</style>
