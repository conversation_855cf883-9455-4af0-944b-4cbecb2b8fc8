<template>
  <div class="d-flex align-items-center">
    <input
      :id="inputId"
      type="radio"
      :aria-describedby="ariaDescribedby"
      :checked="value === compareValue"
      :data-test-id="dataTestId"
      name="payment_method"
      style="opacity: 0; width: 0;"
      :disabled="isDisabled"
      @change="updateValue"
    >
    <div>
      <icon-check class="custom-icon-checkbox" :class="{ 'active': value === compareValue }" />
    </div>
    <label v-if="labelInput" :for="inputId" style="margin-bottom: 0.1rem;">
      {{ labelInput }}
    </label>
  </div>
</template>
<script>
export default {
  props: {
    compareValue: {
      type: String,
      required: true,
    },
    value: {
      type: String,
      required: true,
    },
    ariaDescribedby: {
      type: String,
      default: null,
    },
    dataTestId: {
      type: String,
      default: null,
    },
    inputId: {
      type: String,
      required: true,
    },
    labelInput: {
      type: String,
      default: null,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    updateValue (event) {
      if (event.target.checked) {
        this.$emit('input', this.gateway.gateway)
      }
    }
  }
}
</script>

<style>
.custom-icon-checkbox {
  width: 18px;
  height: 18px;
  fill: white;
  border: 1px solid #ced4da;
  border-radius: 100%;
  margin-right: 8px;
  margin-top: -4px;
  padding: 3px;
}

.custom-icon-checkbox.active {
  border: 1px solid transparent;
  background: var(--primary-color);
}
</style>
