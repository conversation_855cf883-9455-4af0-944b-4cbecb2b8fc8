FROM node:16.19.0-alpine as builer
WORKDIR /home/<USER>/app

RUN apk add --update --no-cache pkgconfig libaio libnsl libtool autoconf automake nasm build-base curl zlib-dev libc6-compat gcompat ffmpeg opus pixman cairo pango giflib ca-certificates \
  git \
  openssh \
  python3 \
  python3-dev \
  py-pip \
  curl \
  && apk add --no-cache --virtual .build-deps git pkgconfig libaio libtool autoconf automake nasm build-base curl zlib-dev libc6-compat gcompat jpeg-dev pixman-dev \
  cairo-dev pango-dev pangomm-dev libjpeg-turbo-dev giflib-dev freetype-dev python3 g++ make \
  && cd /tmp && \
  curl -o instantclient-basiclite.zip https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip -SL && \
  unzip instantclient-basiclite.zip && \
  mv instantclient*/ /usr/lib/instantclient && \
  rm instantclient-basiclite.zip && \
  ln -s /usr/lib/instantclient/libclntsh.so.19.1 /usr/lib/libclntsh.so && \
  ln -s /usr/lib/instantclient/libocci.so.19.1 /usr/lib/libocci.so && \
  ln -s /usr/lib/instantclient/libociicus.so /usr/lib/libociicus.so && \
  ln -s /usr/lib/instantclient/libnnz19.so /usr/lib/libnnz19.so && \
  ln -s /usr/lib/libnsl.so.2 /usr/lib/libnsl.so.1 && \
  ln -s /lib/libc.so.6 /usr/lib/libresolv.so.2 && \
  ln -s /lib64/ld-linux-x86-64.so.2 /usr/lib/ld-linux-x86-64.so.2 \
  && yarn global add node-gyp \
  && yarn global add node-sass@7.0.3 \
  && rm -rf /var/cache/apk/*

ENV ORACLE_BASE /usr/lib/instantclient
ENV LD_LIBRARY_PATH /usr/lib/instantclient
ENV TNS_ADMIN /usr/lib/instantclient
ENV ORACLE_HOME /usr/lib/instantclient

RUN yarn add --ignore-engines global canvas

WORKDIR /home/<USER>/app

ADD ./package.json ./package.json
ADD ./yarn.lock ./yarn.lock
ADD ./start.sh /usr/local/bin/start-nuxt.sh
RUN yarn install --build-from-source --unsafe-perm --allow-root \
    && apk del .build-deps \
    && chmod 777 /usr/local/bin/start-nuxt.sh \
    && ln -s /usr/local/bin/start-nuxt.sh

#FROM builer as base

ARG APP_ENV="production"
RUN echo "APP_ENV: ${APP_ENV}"

#WORKDIR /home/<USER>/app
#COPY --from=builer /home/<USER>/app .
#COPY --from=builer /usr/local/bin/start-nuxt.sh ./start-nuxt.sh
ARG ARG_SENTRY_ENVIRONMENT
ARG ARG_SENTRY_DSN
ARG ARG_SENTRY_SAMPLE_RATE
ENV SENTRY_ENVIRONMENT=$ARG_SENTRY_ENVIRONMENT
ENV SENTRY_DSN=$ARG_SENTRY_DSN
ENV SENTRY_SAMPLE_RATE=$ARG_SENTRY_SAMPLE_RATE

ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=3000
EXPOSE 3000
COPY . .
#RUN yarn build
#RUN ./start-nuxt.sh ${APP_ENV}
CMD ["yarn","dev"]
