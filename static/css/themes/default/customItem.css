/* Firefox */
input[type='number'] {
    -moz-appearance: textfield;
}

body:not(.classic) input.form-control,
body:not(.classic) textarea.form-control,
body:not(.classic) select.custom-select,
body:not(.classic) button.btn,
body:not(.classic) button.btn.btn-custom,
body:not(.classic) button.btn.btn-outline-custom {
    border-radius: unset !important;
}

.classic .border-radius-none {
    border-radius: 0.3rem;
}

input.form-control:focus,
textarea.form-control:focus,
select.custom-select:focus,
button.btn:focus,
input.form-control:hover,
textarea.form-control:hover,
select.custom-select:hover,
button.btn:hover {
    box-shadow: unset;
}

button.btn.btn-custom {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color) !important;
}

button.btn.btn-custom:focus {
    box-shadow: unset;
}

button.btn.btn-custom:active {
    box-shadow: unset;
}

button.btn.btn-custom:hover {
    box-shadow: 0 0 5px 0 var(--primary-color);
}

button.btn.btn-custom:active {
    box-shadow: 0 0 15px 0 var(--primary-color);
}

button.btn.btn-outline-custom:focus {
    box-shadow: unset;
}

button.btn.btn-outline-custom:active {
    box-shadow: unset;
}

button.btn.btn-outline-custom:hover {
    box-shadow: 0 0 5px 0 var(--primary-color);
    color: var(--primary-color);
}

button.btn.btn-outline-custom:hover.border {
    border-color: var(--primary-color) !important;
}

button.btn.btn-outline-custom:hover.border:active {
    box-shadow: 0 0 15px 0 var(--primary-color);
}

button.btn.btn-outline-custom2 {
    border-radius: unset;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

button.btn.btn-outline-custom2:focus {
    box-shadow: unset;
}

button.btn.btn-outline-custom2:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 5px 0 var(--primary-color);
}

button.btn.btn-outline-custom2:active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 15px 0 var(--primary-color);
}

button.btn.btn-outline-custom3 {
    border: 1px solid black;
    color: black;
}

button.btn.btn-outline-custom3:focus {
    box-shadow: unset;
}

button.btn.btn-outline-custom3:hover {
    border-color: var(--primary-color) !important;
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 5px 0 var(--primary-color);
}

button.btn.btn-outline-custom3:active {
    border-color: var(--primary-color) !important;
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 15px 0 var(--primary-color);
}

.campaign-carousel-images img {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-drag: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-drag: none;
}

.campaign-carousel-images .zoom {
    cursor: zoom-in;
}