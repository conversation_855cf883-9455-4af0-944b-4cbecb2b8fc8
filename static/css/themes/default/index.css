@import 'customItem.min.css';
@import 'font.min.css';

.text-color-primary {
    color: var(--primary-color) !important;
}

body {
    font-family: 'Montserrat', sans-serif !important;
}

body.classic {
    font-family: 'AmazonEmber', sans-serif !important;
}

.text-custom {
    color: var(--primary-color) !important;
}

.text-hover-custom:hover {
    color: var(--primary-color) !important;
}

.category-item,
.campaign-item {
    cursor: pointer;
}

.category-item .thumb,
.campaign-item .thumb {
    position: relative;
    width: 100%;
    padding-top: 125%;
    overflow: hidden;
    background-color: transparent;
}

.category-item img,
.campaign-item img,
.category-item .spinner,
.campaign-item .spinner {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: transform 0.2s;
}

.category-item:hover img,
.campaign-item:hover img {
    transform: scale(1.05);
    /* (150% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
}

.category-item:hover h5,
.campaign-item:hover h5,
.category-item:hover a,
.campaign-item:hover a {
    color: var(--primary-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
    border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #e6e6e6;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #b3b3b3;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

div {
    scrollbar-width: thin;
    scrollbar-color: #b3b3b3 #e6e6e6;
}

.dropdown-item.active {
    background-color: var(--primary-color) !important;
}

@media (min-width: 769px) {
  .livechatbtn {
    display: none;
  }
}

@media (max-width: 768px) {
    .crisp-client {
        display: none !important;
    }

    .crisp-client.opened {
        display: block !important;
    }

    .livechatbtn {
        display: block !important;
        position: fixed;
        right: 0px;
        top: 0px;
        height: 100%;
        transform: translateY(50%);
        z-index: 9;
    }

    .livechatbtn span {
        white-space: nowrap;
        position: absolute;
        transform: rotate(-90deg) translateY(-200%);
        padding: 0.3rem 1rem;
        border: 0;
        border-radius: 0.3rem 0.3rem 0 0;
        border: 1px solid var(--primary-color);
        background: white;
    }
}
