<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script rel="stylesheet" src="https://js.stripe.com/v3/" hid="stripe-js"></script>
  <style>
    :root {
      --grid-col: 1;
    }

    #stripe-items-container {
      display: none;
    }
    #stripe-items-container:has(iframe) {
      display: grid;
      grid-template-columns: repeat(2,minmax(0,1fr));
      gap: 0.75rem;
      grid-gap: 0.75rem;
    }
    #stripe-items-container > * {
      padding: 0.75rem;
      border-radius: 0.3rem;
      border: 1px solid #e5e7eb;
      grid-column: span var(--grid-col) / span var(--grid-col);
    }

    .input-error {
      padding-right: calc(1.5em + 0.75rem) !important;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right calc(0.375em + 0.1875rem) center;
      background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
    .input-error > .icon {
      display: none;
    }

    .icon {
      position: absolute;
      right: 0;
      transform: translate(-100%, -100%);
      display: block;
      width: 1em;
      height: 1em;
      stroke-width: 0;
      stroke: #000;
      fill: #000;
    }
  </style>
</head>
<body>
  <svg style="display: none;">
    <defs>
      <g id="icon-sen-info-outline">
        <path d="M24 0c6.627 0 12.628 2.687 16.971 7.029s7.029 10.343 7.029 16.971-2.687 12.628-7.029 16.971c-4.343 4.343-10.343 7.029-16.971 7.029s-12.628-2.687-16.971-7.029c-4.343-4.343-7.029-10.343-7.029-16.971s2.687-12.627 7.029-16.971c4.342-4.343 10.343-7.029 16.971-7.029zM25.664 32.086h2.733v2.749h-8.793v-2.749h2.696v-8.657h-2.696v-2.749h6.060v11.406zM23.045 17.989c-0.664-0.005-1.23-0.237-1.706-0.709-0.476-0.47-0.709-1.042-0.709-1.711 0-0.657 0.232-1.224 0.709-1.695s1.043-0.708 1.706-0.708c0.652 0 1.218 0.237 1.695 0.708 0.481 0.471 0.719 1.038 0.719 1.695 0 0.443-0.113 0.85-0.334 1.213-0.221 0.368-0.511 0.663-0.873 0.878s-0.766 0.323-1.207 0.328zM39.16 8.84c-3.879-3.878-9.24-6.278-15.16-6.278s-11.281 2.4-15.16 6.278c-3.878 3.88-6.278 9.24-6.278 15.16s2.4 11.281 6.278 15.16c3.879 3.878 9.24 6.278 15.16 6.278s11.281-2.4 15.16-6.278c3.878-3.879 6.278-9.24 6.278-15.16s-2.4-11.28-6.278-15.16z"></path>
      </g>
      <g id="icon-sen-lock">
        <path d="M9.063 17.837h1.129v-3.406c0-3.957 1.556-7.558 4.063-10.173 2.517-2.627 5.993-4.257 9.819-4.257s7.302 1.63 9.82 4.256c2.507 2.615 4.063 6.217 4.063 10.173v3.406h1.129c0.937 0 1.788 0.382 2.405 0.999s0.999 1.468 0.999 2.405v23.309c0 0.937-0.382 1.788-0.999 2.405s-1.468 0.999-2.405 0.999h-30.023c-0.937 0-1.788-0.382-2.405-0.999s-0.999-1.467-0.999-2.404v-23.309c0-0.937 0.383-1.788 0.999-2.405s1.468-0.999 2.405-0.999v0zM22.829 34.069l-1.635 4.284h5.761l-1.516-4.343c0.962-0.496 1.62-1.499 1.62-2.655 0-1.648-1.337-2.984-2.984-2.984s-2.985 1.337-2.985 2.984c-0 1.204 0.713 2.242 1.739 2.714v0zM12.587 17.837h22.976v-3.406c0-3.321-1.298-6.336-3.39-8.517-2.081-2.171-4.947-3.518-8.098-3.518s-6.018 1.347-8.098 3.517c-2.091 2.181-3.39 5.196-3.39 8.517v3.407h0zM39.086 20.231h-30.023c-0.276 0-0.528 0.114-0.711 0.297s-0.297 0.436-0.297 0.711v23.309c0 0.276 0.114 0.528 0.297 0.711s0.436 0.297 0.711 0.297h30.024c0.276 0 0.528-0.114 0.711-0.297s0.297-0.435 0.297-0.711v-23.309c0-0.276-0.114-0.528-0.297-0.711s-0.436-0.297-0.712-0.297v0z"></path>
      </g>
    </defs>
  </svg>
  <div id="stripe-items-container">
    <div style="--grid-col: 2;">
      <div id="card-payment-number"></div>
      <svg viewBox="0 0 48 48" class="icon icon-sen-lock"><use xlink:href="#icon-sen-lock"></use></svg>
    </div>
    <div id="card-payment-expiry"></div>
    <div>
      <div id="card-payment-cvc"></div>
      <!-- <svg viewBox="0 0 48 48" class="icon icon-sen-info-outline"><use xlink:href="#icon-sen-info-outline"></use></svg> -->
    </div>
  </div>
  <div id="payment-element"></div>
  <script>
let stripeData = {};
let stripeObj = null;
let stripeReturnUrl = null;
let i18nDetail = {
  locale: "en",
  card_number: "Card number",
  exp_date: "Expiration date",
  mm_yy: "MM/YY",
  cvc: "Security code"
};

const searchParams = (new URL(document.URL)).searchParams;
const frameType = (searchParams.get("ewallet") === "true") ? "ewallet" :
                  (searchParams.get("banks") === "true") ? "banks" : "card"

function sendMessage(objMessageDetail) {
  // property "name" & "args" is required. value of "args" must be an array
  // { name: "handle_error", args: ["No clientId was supplied"] }
  if (!objMessageDetail.args) { objMessageDetail.args = []; }
  if (!Array.isArray(objMessageDetail.args)) { objMessageDetail.args = [objMessageDetail.args]; }

  window.parent.postMessage(objMessageDetail, "*");
}

let stripeCard, stripeElements;

// Stripe init elements
function initStripeCard() {
  const element = stripeObj.elements({
    fonts: [
      {
        family: 'Montserrat',
        cssSrc: 'https://fonts.googleapis.com/css?family=Montserrat:400,500'
      }
    ],
    clientSecret: stripeData.clientSecret,
    locale: i18nDetail.locale,
  });

  const style = {
    base: {
      color: '#32325d',
      fontFamily: 'Montserrat, sans-serif', // set integrated font family
      fontSmoothing: 'antialiased'
    }
  }

  // cardNumber
  stripeCard = element.create('cardNumber', {
    placeholder: `${i18nDetail.card_number} *`,
    showIcon: true,
    style
  });

  stripeCard.mount('#card-payment-number');
  // cardExpiry
  const cardExpiry = element.create('cardExpiry', {
    placeholder: `${i18nDetail.exp_date} (${i18nDetail.mm_yy}) *`,
    style
  });
  cardExpiry.mount('#card-payment-expiry')
  // cardCvc
  const cardCvc = element.create('cardCvc', {
    placeholder: `${i18nDetail.cvc} *`,
    style
  });
  cardCvc.mount('#card-payment-cvc');

  stripeCard.on('ready', () => {
    sendMessage({
      name: "stripe_set_container_height",
      args: [
        document.body.clientHeight,
        frameType,
      ]
    });
  });
  stripeCard.on('change', function (event) {
    sendMessage({ name: "stripe_card_on_change", args: [event] });
  });
  stripeCard.on('focus', function () {
    sendMessage({ name: "stripe_focus", args: ['card'] });
  });
}
function initStripeEWallet() {
  const appearance = {
    fonts: [
      {
        family: 'Montserrat',
        cssSrc: 'https://fonts.googleapis.com/css?family=Montserrat:400,500'
      }
    ],
    variables: {
      fontFamily: 'Montserrat, sans-serif',
      borderRadius: '0px'
    }
  };

  stripeElements = stripeObj.elements({
    clientSecret: stripeData.clientSecret,
    appearance,
    locale: i18nDetail.locale,
  });

  const paymentElement = stripeElements.create('payment');
  paymentElement.mount('#payment-element');

  // update payment method when changed
  paymentElement.on('focus', () => {
    sendMessage({ name: "stripe_focus", args: ['ewallet'] });
  });

  paymentElement.on('ready', () => {
    sendMessage({
      name: "stripe_set_container_height",
      args: [
        document.body.clientHeight,
        frameType,
      ]
    });
  })
}
function initStripeBanks() {} // TODO: banks

// Handle Payment
async function handleStripePayment(response) {
  Array.from(document.querySelectorAll(".input-error")).forEach((el) => el.classList.remove("input-error"));

  if (response.error) {
    Array.from(document.querySelectorAll("#stripe-items-container > div")).forEach((el) => el.classList.add("input-error"));

    sendMessage({ name: "handle_error", args: [response] });
    return;
  }

  if (response.paymentIntent.status === "requires_action") {
    const result = await stripe.confirmCardPayment(stripeData.clientSecret);
    await handleStripePayment(result);
    return;
  }

  sendMessage({ name: 'stripe_create_order_succeed', args: [response] });
}

const evTable = {
  stripe_set_data: (data, i18n, returnUrl) => {
    stripeData = data;
    i18nDetail = i18n;
    stripeReturnUrl = returnUrl;

    if (!stripeData.publishableKey) {
      sendMessage({ name: "handle_error", args: ["Cannot init stripe iframe", "Missing publishableKey"] });
      return;
    }
    if (!stripeData.clientSecret) {
      sendMessage({ name: "handle_error", args: ["Cannot init stripe iframe", "Missing clientSecret"] });
      return;
    }

    stripeObj = window.Stripe(stripeData.publishableKey);

    if (frameType === "card") initStripeCard()
    if (frameType === "ewallet") initStripeEWallet()
    if (frameType === "banks") initStripeBanks()
  },
  stripe_confirm_card: async () => {
    const response = await stripeObj.confirmCardPayment(stripeData.clientSecret, {
      payment_method: {
        card: stripeCard,
      }
    });

    await handleStripePayment(response);
  },
  stripe_confirm_ewallet: async (returnUrl) => {
    if (!returnUrl) {
      sendMessage({ name: "handle_error", args: ["Missing returnUrl"] });
      return;
    }

    const response = await stripeObj.confirmPayment({
      elements: stripeElements,
      confirmParams: {
        return_url: returnUrl,
      },
      redirect: "if_required",
    });

    await handleStripePayment(response);
  }
}

function postMessageHandler (messageEvent) {
  const { data: event } = messageEvent;
  if (
    typeof event !== "object" ||
    !event.name ||
    !event.args || !Array.isArray(event.args)
  ) { return; }

  if (Object.hasOwnProperty.call(evTable, event.name)) {
    evTable[event.name].apply(null, event.args)
  }
}

(() => {
  if (window.addEventListener) {
    window.addEventListener("message", postMessageHandler);
  } else {
    window.attachEvent("onmessage", postMessageHandler);
  }

  const itv = setInterval(() => {
    if (!window.Stripe) { return }

    clearInterval(itv);
    sendMessage({ name: "stripe_retrieve_data" });
  }, 500)
})()
  </script>
</body>
</html>