FROM senasia/node:16.9.0-alpine as builer
WORKDIR /home/<USER>/app

ADD ./package.json ./package.json
ADD ./yarn.lock ./yarn.lock
ADD ./start.sh /usr/local/bin/start-nuxt.sh
RUN yarn install --build-from-source --unsafe-perm --allow-root \
    && apk del .build-deps \
    && chmod 777 /usr/local/bin/start-nuxt.sh \
    && ln -s /usr/local/bin/start-nuxt.sh

#FROM builer as base

ARG APP_ENV="production"
RUN echo "APP_ENV: ${APP_ENV}"

#WORKDIR /home/<USER>/app
#COPY --from=builer /home/<USER>/app .
#COPY --from=builer /usr/local/bin/start-nuxt.sh ./start-nuxt.sh
ARG ARG_SENTRY_ENVIRONMENT
ARG ARG_SENTRY_DSN
ARG ARG_SENTRY_SAMPLE_RATE
ENV SENTRY_ENVIRONMENT=$ARG_SENTRY_ENVIRONMENT
ENV SENTRY_DSN=$ARG_SENTRY_DSN
ENV SENTRY_SAMPLE_RATE=$ARG_SENTRY_SAMPLE_RATE

ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=3000
EXPOSE 3000
COPY . .
RUN yarn build
#RUN ./start-nuxt.sh ${APP_ENV}