module.exports = {
  root: true,
  env: {
    browser: true,
    node: true
  },
  parserOptions: {
    parser: 'babel-eslint'
  },
  extends: [
    '@nuxtjs',
    'plugin:nuxt/recommended'
  ],
  plugins: [
  ],
  // add your custom rules here
  rules: {
    'vue/no-v-html': 'off', // warn for production
    'no-console': 'off', // warn for production
    'import/no-named-as-default': 0,
    'comma-dangle': ['error', 'only-multiline']
  }
}
