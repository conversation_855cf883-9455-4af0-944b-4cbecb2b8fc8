# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `yarn dev` - Start development server at localhost:3000
- `yarn build` - Build for production
- `yarn start` - Start production server
- `yarn generate` - Generate static project

### Code Quality
- `yarn lint` - Run both JavaScript and style linting
- `yarn lint:js` - Lint JavaScript/Vue files with ESLint
- `yarn lint:style` - Lint CSS/SCSS/Vue styles with Stylelint

### Theme Development
- `yarn build:theme` - Compile theme SCSS and copy fonts (removes static/css and static/fonts first)

## Architecture Overview

This is a **Nuxt.js 2** e-commerce storefront built with Vue.js, using a modular architecture for scalability and maintainability.

### Core Framework Stack
- **Nuxt.js 2.15.8** - Vue.js framework with SSR
- **Vue.js** with composition patterns via mixins
- **Bootstrap Vue** for UI components
- **Vuex** for state management (modular structure)
- **Nuxt i18n** for internationalization (27+ languages)

### State Management (Vuex Store)
The application uses a **modular Vuex store** architecture with 10 distinct modules:

- **Root Store** - Global state (currency, theme, country detection)
- **Cart** - Shopping cart with complex product configurations and personalization
- **Campaign** - Product campaigns and variant management
- **Order** - Checkout process and payment gateway integration
- **General Info** - Application configuration (categories, countries, currencies)
- **Store Info** - Store-specific settings and branding
- **Listing** - Product listing, filtering, and search
- **Product Review** - Review system with caching
- **Deliver To Location** - Geographic location detection
- **Unsubscribe** - Email management

Key patterns: Local storage caching with `lscache`, multiple HTTP clients for different API operations, and extensive internationalization support.

### Mixin Architecture
Vue components use **27 specialized mixins** for code reusability:

**Core Business Logic:**
- `campaign.js` - Product management, pricing, add-to-cart logic
- `cart.js` - Shopping cart operations and bundle management  
- `checkout.js` - Payment processing (Stripe, PayPal, Tazapay), address validation
- `addressForm.js` - Country-specific address form handling

**UI and Experience:**
- `header.js` - Search, cart display, mobile detection
- `listing.js` - Product filtering, sorting, pagination
- `tracking.js` - Analytics integration (Google, Facebook, TikTok, Pinterest)

### Directory Structure Patterns

**Components:** Theme-based component organization in `/themes/default/components/`
- `campaign/` - Product display and interaction components
- `common/` - Shared UI components (headers, footers, breadcrumbs)
- `cart/` - Shopping cart components
- `checkout/` - Checkout flow components

**Pages:** Nuxt.js routing in `/pages/` and `/themes/default/pages/`
- Dynamic routing for campaigns (`_campaign/`), collections, custom products
- Internationalized routing with i18n module

**Business Logic:**
- `/helpers/` - Utility functions for campaigns, strings, storage
- `/plugins/` - Nuxt plugins for HTTP clients, tracking, canvas manipulation
- `/server-middleware/` - Server-side middleware for payments and logging

### Key Technical Features

**Multi-currency & i18n:** 27+ languages with country-specific configurations and currency conversion

**Complex Product Configuration:** Support for 3 types of personalized products with custom designs, variants, and bundling

**Payment Integration:** Multiple payment gateways with regional API support and security features (CAPTCHA, CSRF)

**Performance:** Extensive caching strategies, lazy loading, and optimized asset delivery

**Analytics:** Comprehensive tracking integration with major platforms

## Important Notes

- Always run linting commands before committing code
- The application uses theme-based component organization - prefer editing existing components in `/themes/default/` over creating new ones
- State management follows strict patterns - use appropriate Vuex modules for data operations
- Internationalization is required for user-facing content - use i18n keys rather than hardcoded strings
- Local storage caching is extensively used - be aware of cache invalidation when modifying data structures