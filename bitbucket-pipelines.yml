image: atlassian/default-image:2
options:
  max-time: 30
  size: 2x
definitions:
  services:
    docker:
      image: docker:20.10.12-dind
      memory: 6000
      
run-test-staging: &run-test-staging
        - step:
            name: "Pre Test (Commit [skip test] to disable) Staging: staging1.senprints.com"
            # image: itlboy/docker:dind-buildx
            image: senasia/base:docker-kubectl-alpine
            clone:
             depth: 1
            runs-on: 
              - 'self.hosted'  
            script:
                - COMMIT_MESSAGE=`git log --format=%B -n 1 $BITBUCKET_COMMIT`
                - echo $COMMIT_MESSAGE
                - word="\[skip test\]"
                - | 
                  case "$COMMIT_MESSAGE" in
                    *$word*) echo "SKIP TEST!"; exit 0 ;;
                    *      ) echo "CONTINUE TEST" ;;
                  esac
                - ls -la
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/browser-test-2
                - export TEST_DOMAIN=staging1.senprints.com
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker run -i $DOCKER_HUB_USER/browser-test-2 -d $TEST_DOMAIN -b $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH -m $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH:#$BITBUCKET_BUILD_NUMBER -i $BITBUCKET_REPO_SLUG/$BITBUCKET_BUILD_NUMBER
            services:
             - docker
             
run-test-production: &run-test-production
        - step:
            name: "Post Test Production: v3.senprints.com"
            image: itlboy/docker:dind-buildx
            clone:
             enabled: false
            runs-on: 
              - 'self.hosted'  
            script:
                - ls -la
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/browser-test-2
                - export TEST_DOMAIN=v3.senprints.com
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker run -i $DOCKER_HUB_USER/browser-test-2 -d $TEST_DOMAIN -b $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH -m $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH:#$BITBUCKET_BUILD_NUMBER -i $BITBUCKET_REPO_SLUG/$BITBUCKET_BUILD_NUMBER:prod
            services:
             - docker

deploy-production: &deploy-production 
        - step:
            name: Deploy to Production
            image: senasia/base:docker-kubectl-alpine
            runs-on: 
              - 'self.hosted' 
            clone:
             enabled: false
            deployment: Production 
            services:
              - docker
            script:
              - export SOURCE_DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-prod:$BITBUCKET_BUILD_NUMBER
              - export TARGET_DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-prod
              - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
              - docker manifest create $TARGET_DOCKER_IMAGE $SOURCE_DOCKER_IMAGE
              - docker manifest push $TARGET_DOCKER_IMAGE

              - ls -la
              - export KUBECONFIG=./kubeconfig
              ######
              # - echo "$KUBECONFIG_GG_US" | base64 -d > ./kubeconfig
              # - kubectl get node
              # - kubectl get deployment -n production | grep frontend-storefront | awk '{print$1}' | xargs -r  kubectl rollout restart deployment -n production
              # - kubectl rollout status deployment frontend-storefront -n production
              ######
              - echo "$KUBECONFIG_BOSTON_1" | base64 -d > ./kubeconfig
              - kubectl get node
              - kubectl get deployment -n production | grep frontend-storefront | awk '{print$1}' | xargs -r  kubectl rollout restart deployment -n production
              #######

               # Deploy US VULTR
              # - echo "$KUBECONFIG_PRODUCTION_VULTR_US" | base64 -d > ./kubeconfig
              # - kubectl  get node
              # - kubectl set image deployment/frontend-storefront nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER -n production
              # # - kubectl  rollout status deployment frontend-storefront -n production
              # # Deploy Sing
              # - echo "$KUBECONFIG_SING_1" | base64 -d > ./kubeconfig
              # - export KUBECONFIG=./kubeconfig
              # - kubectl  get node

              # - kubectl set image deployment/frontend-storefront-cdnjs-docker-hub nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER
              # - kubectl set image deployment/frontend-storefront-docker-hub nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER



              # Deploy US CONTABO
              # - echo "$KUBECONFIG_PRODUCTION_NY" | base64 -d > ./kubeconfig
              # - kubectl  get node
              # - kubectl set image deployment/frontend-storefront-cdnjs-docker-hub nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER
              # - kubectl set image deployment/frontend-storefront-2-docker-hub nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER
              # - kubectl set image deployment/frontend-storefront-docker-hub nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER
              
              ####### Run deploy k8s-config
              - docker run -e SOURCE_DOCKER_IMAGE=$SOURCE_DOCKER_IMAGE  senasia/k8s-config senprints-store-v3
              # Delete html caches
              -  |
               curl --location --request POST 'https://cache-manager.senprints.net/delete' \
               --header 'Authorization: Bearer VSQQa7dT94EkkmDR' \
               --header 'Content-Type: application/json' \
               --data '[{
                              "type": "all",
                              "storeVersion": "3"
                          }]'
              # Delete cache 2nd    
              -  |
               curl --location --request POST 'https://cache-manager.senprints.net/delete' \
               --header 'Authorization: Bearer VSQQa7dT94EkkmDR' \
               --header 'Content-Type: application/json' \
               --data '{
                      "data": [
                          {
                              "type": "all",
                              "storeVersion": "3"
                          }
                      ],
                      "delay": 30
                  }'
              - echo 'Deploy successful!'
build-image-production: &build-image-production
        - step:
            name: Build Image
            image: itlboy/docker:dind-buildx
            runs-on: 
              - 'self.hosted'
            script:
                  - ls -la
                  - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                  - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-prod:$DOCKER_TAG
                  - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-prod"
                  - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                  - docker buildx create --use
                  -  >
                   docker buildx build
                   --platform=linux/amd64
                   --build-arg APP_ENV=production
                   --build-arg ARG_SENTRY_ENVIRONMENT=production
                   --build-arg ARG_SENTRY_DSN=https://<EMAIL>/11
                   --build-arg ARG_SENTRY_SAMPLE_RATE=0.01
                   --build-arg BUILDKIT_INLINE_CACHE=1
                   --cache-from $DOCKER_CACHE_IMAGE
                   --cache-to $DOCKER_CACHE_IMAGE
                   -o type=docker,name=$DOCKER_IMAGE
                   -t $DOCKER_IMAGE .
                  - docker push $DOCKER_IMAGE
                  # - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                  # - docker push $DOCKER_IMAGE:$DOCKER_TAG
            services:
             - docker              
                 
pipelines:
  branches:
    feature/nodejs_16.19.0:
      - parallel:
        - step:
            name: Build DEV-BUILD Image
            image: itlboy/docker:dind-buildx
            runs-on: 
              - 'self.hosted'
              - 'us'
            script:
                - ls -la
                - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-build
                - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-build"
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker buildx create --use
                -  >
                 docker buildx build
                 --platform=linux/amd64
                 --build-arg APP_ENV=dev
                 --build-arg RUN_TYPE=build
                 --build-arg ARG_SENTRY_ENVIRONMENT=dev-build
                 --build-arg ARG_SENTRY_DSN=https://<EMAIL>/11
                 --build-arg ARG_SENTRY_SAMPLE_RATE=1.0
                 --build-arg BUILDKIT_INLINE_CACHE=1
                 --cache-from $DOCKER_IMAGE
                 -o type=docker,name=$DOCKER_IMAGE
                 -t $DOCKER_IMAGE .
                - docker push $DOCKER_IMAGE
                - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                - docker push $DOCKER_IMAGE:$DOCKER_TAG
            services:
             - docker
        - step:
            name: Build DEV-BUILD Image
            image: itlboy/docker:dind-buildx
            runs-on: 
              - 'self.hosted'
              - 'us'
            script:
                - ls -la
                - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-build
                - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-build"
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker buildx create --use
                -  >
                 docker buildx build
                 --platform=linux/amd64
                 --build-arg APP_ENV=dev
                 --build-arg RUN_TYPE=build
                 --build-arg ARG_SENTRY_ENVIRONMENT=dev-build
                 --build-arg ARG_SENTRY_DSN=https://<EMAIL>/11
                 --build-arg ARG_SENTRY_SAMPLE_RATE=1.0
                 --cache-from $DOCKER_IMAGE
                 --build-arg BUILDKIT_INLINE_CACHE=1
                 -o type=docker,name=$DOCKER_IMAGE
                 -t $DOCKER_IMAGE .
                - docker push $DOCKER_IMAGE
                - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                - docker push $DOCKER_IMAGE:$DOCKER_TAG
            services:
             - docker

    develop:
      - parallel:
        - step:
            name: Build DEV Image
            image: itlboy/docker:dind-buildx
            runs-on: 
              - 'self.hosted'  
  #            - 'test'
            script:
                - ls -la
                
                - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-dev
                - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-dev"
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker buildx create --use
                -  >
                 docker buildx build
                 --platform=linux/amd64
                 --build-arg APP_ENV=dev
                 --build-arg RUN_TYPE=dev
                 --build-arg ARG_SENTRY_ENVIRONMENT=dev
                 --build-arg ARG_SENTRY_DSN=https://<EMAIL>/11
                 --build-arg ARG_SENTRY_SAMPLE_RATE=1.0
                 --cache-from $DOCKER_IMAGE
                 --build-arg BUILDKIT_INLINE_CACHE=1
                 -o type=docker,name=$DOCKER_IMAGE
                 -t $DOCKER_IMAGE .
                - docker push $DOCKER_IMAGE
                - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                - docker push $DOCKER_IMAGE:$DOCKER_TAG
            services:
             - docker
           
        - step:
            name: Build DEV-BUILD Image
            image: itlboy/docker:dind-buildx
            runs-on: 
              - 'self.hosted'  
  #            - 'test'
            script:
                - ls -la
                
                - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-build
                - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-build"
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker buildx create --use
                -  >
                 docker buildx build
                 --platform=linux/amd64
                 --build-arg APP_ENV=dev
                 --build-arg RUN_TYPE=build
                 --build-arg ARG_SENTRY_ENVIRONMENT=dev-build
                 --build-arg ARG_SENTRY_DSN=https://<EMAIL>/11
                 --build-arg ARG_SENTRY_SAMPLE_RATE=1.0
                 --cache-from $DOCKER_IMAGE
                 --build-arg BUILDKIT_INLINE_CACHE=1
                 -o type=docker,name=$DOCKER_IMAGE
                 -t $DOCKER_IMAGE .
                - docker push $DOCKER_IMAGE
                - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                - docker push $DOCKER_IMAGE:$DOCKER_TAG
            services:
             - docker


      - step:
          name: Deploy to new DEV server
          image: senasia/base:docker-kubectl-alpine
          deployment: Dev
          runs-on: 
            - 'self.hosted'
          clone:
           enabled: false
          services:
              - docker
          script:
            - ls -la
            - echo "$KUBECONFIG_SG_HOME" | base64 -d > ./kubeconfig
            - export KUBECONFIG=./kubeconfig
            - kubectl --insecure-skip-tls-verify get node
#            - kubectl --insecure-skip-tls-verify rollout restart deployment frontend-storefront -n development
#            - kubectl --insecure-skip-tls-verify rollout restart deployment frontend-storefront-build -n development
            - kubectl set image deployment/frontend-storefront-build nodejs=senasia/storefront-build:$BITBUCKET_BUILD_NUMBER -n senprints-dev
            - kubectl --insecure-skip-tls-verify rollout status deployment frontend-storefront-build -n senprints-dev

            ####
            - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
            - docker run -e SOURCE_DOCKER_IMAGE=senasia/storefront-build:$BITBUCKET_BUILD_NUMBER senasia/k8s-config senprints-store-v3-dev
            # - docker run -e SOURCE_DOCKER_IMAGE=$SOURCE_DOCKER_IMAGE senasia/k8s-config senprints-store-v4-dev
            # Delete html caches
            -  |
             curl --location --request POST 'https://cache-manager-dev.senprints.net/delete' \
             --header 'Authorization: Bearer VSQQa7dT94EkkmDR' \
             --header 'Content-Type: application/json' \
             --data '{
                    "data": [
                        {
                            "type": "all",
                            "storeVersion": "3"
                        }
                    ],
                    "delay": 0
                }'
            - echo 'Deploy successful!'
    staging:
        - step:
            name: Build Image
            image: itlboy/docker:dind-buildx
            runs-on: 
              - 'self.hosted'
            script:
                  - ls -la
                  - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                  - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-prod
                  - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-prod"
                  - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                  - docker buildx create --use
                  -  >
                   docker buildx build
                   --platform=linux/amd64
                   --build-arg APP_ENV=production
                   --build-arg ARG_SENTRY_ENVIRONMENT=production
                   --build-arg ARG_SENTRY_DSN=https://<EMAIL>/11
                   --build-arg ARG_SENTRY_SAMPLE_RATE=0.01
                   --build-arg BUILDKIT_INLINE_CACHE=1
                   --cache-from $DOCKER_IMAGE
                   -o type=docker,name=$DOCKER_IMAGE
                   -t $DOCKER_IMAGE .
                  - docker push $DOCKER_IMAGE
                  - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                  - docker push $DOCKER_IMAGE:$DOCKER_TAG
            services:
             - docker
        - step:
            name: Deploy to Staging
            image: itlboy/kubectl
            clone:
             enabled: false
            deployment: Staging 
            script:
              - ls -la
              - echo "Deploy to Staging"
              - echo "$KUBECONFIG_PRODUCTION_NY" | base64 -d > ./kubeconfig
              - export KUBECONFIG=./kubeconfig

              - kubectl  get node
              - kubectl set image deployment/frontend-storefront nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER -n staging
              - kubectl rollout status deployment frontend-storefront -n staging

              - echo "Delete staging html cache"
              - kubectl rollout restart deployment cache-storefront -n staging
              - kubectl rollout status deployment cache-storefront -n staging

        - parallel:
            fail-fast: true
            steps:
                - <<: *run-test-staging
                - <<: *run-test-staging
                - <<: *run-test-staging
                
    master:
        - <<: *build-image-production  
        - step:
            name: Deploy to Staging
            image: itlboy/kubectl
            clone:
             enabled: false
            deployment: Staging 
            script:
              - ls -la
              - echo "Deploy to Staging"
              - echo "$KUBECONFIG_PRODUCTION_NY" | base64 -d > ./kubeconfig
              - export KUBECONFIG=./kubeconfig

              - kubectl  get node
              - kubectl set image deployment/frontend-storefront nodejs=senasia/storefront-prod:$BITBUCKET_BUILD_NUMBER -n staging
              - kubectl rollout status deployment frontend-storefront -n staging

              - echo "Delete staging html cache"
              - kubectl rollout restart deployment cache-storefront -n staging
              - kubectl rollout status deployment cache-storefront -n staging

        - parallel:
            fail-fast: true
            steps:
                - <<: *run-test-staging
                - <<: *run-test-staging
                - <<: *run-test-staging
        - <<: *deploy-production
        - parallel:
            fail-fast: true
            steps:
                - <<: *run-test-production
                - <<: *run-test-production
                
    MASTER-HOTFIX:
        - <<: *build-image-production
        - <<: *deploy-production
        - parallel:
            fail-fast: true
            steps:
                - <<: *run-test-production
                - <<: *run-test-production