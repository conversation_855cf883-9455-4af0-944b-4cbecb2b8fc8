<template>
  <div class="container">
    <div class="row">
      <div class="col">
        <div class="juno_okyo">
          Please wait...
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Payment',
  async asyncData ({ $axios, query, redirect }) {
    if (!query.token) {
      redirect('/cart')
      return
    }

    // create order
    const { success, data } = await $axios.$post('/public/order/create/paypal', {
      order_token: query.token,
      domain: query.domain
    })

    if (!success) {
      redirect('/cart')
      return
    }

    return {
      redirectUrl: data
    }
  },
  mounted () {
    // redirect in mounted() to change "referer" header in paypal.com
    window.top.location.href = this.redirectUrl
  }
}
</script>

<style scoped>
.juno_okyo {
  text-align: center;
  line-height: 100px;
  height: 100px;
  font-size: 24px;
}
</style>
