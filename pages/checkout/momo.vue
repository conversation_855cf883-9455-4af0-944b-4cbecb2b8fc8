<template>
  <main class="container">
    <div class="juno_okyo">
      <div>
        <loading-dot variant="dark" />
      </div>
      <div>
        {{ $t('Verifying payment status...') }}
      </div>
    </div>
  </main>
</template>

<script>
export default {
  async mounted () {
    const { orderToken, requestId } = this.$route.query

    const endpoint = `/public/order/callback/momo/${orderToken}?request_id=${requestId}`

    const { success } = await this.$axios.$get(this.$masterUrl(endpoint))

    const url = success ? `/order/thank-you/${orderToken}` : `/checkout/${orderToken}`

    window.location.href = this.localePath(url)
  }
}
</script>

<style scoped>
.juno_okyo {
  text-align: center;
  padding: 50px 0;
  height: 100px;
  font-size: 24px;
}
</style>
