<template>
  <Component
    :is="asyncComponent"
    :order="order"
    :gateways="gateways"
    :shipping-methods="shippingMethods"
  />
</template>

<script>
export default {
  layout: 'checkout',
  asyncData,
  head,
  computed: {
    asyncComponent,
    order,
    gateways,
    shippingMethods
  }
}

async function asyncData ({ store, params, error }) {
  const { success, data } = await store.dispatch('order/getOrderData', params.token)

  if (!(success || data) || !data.order) {
    return error({
      statusCode: 404,
      message: 'Page Not Found'
    })
  }
}

function head () {
  const title = 'Checkout'

  return {
    title: `${title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title, description: title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/checkout`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/checkout'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/checkout'
      )
  }
}

function order () {
  return this.$store.state.order.order
}

function gateways () {
  return this.$store.state.order.gateways
}

function shippingMethods () {
  return this.$store.state.order.shippingMethods
}
</script>
