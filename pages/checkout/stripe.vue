<template>
  <main class="container">
    <div class="juno_okyo">
      <div>
        <loading-dot variant="dark" />
      </div>
      <div>
        {{ $t('Verifying payment status...') }}
      </div>
    </div>
  </main>
</template>

<script>
export default {
  head () {
    return {
      title: this.$t('Verifying payment status...'),
      script: [
        {
          src: 'https://js.stripe.com/v3/',
          hid: 'stripe-js',
          defer: true
        }
      ]
    }
  },
  async mounted () {
    const params = this.$route.query
    const orderToken = params.order_token
    const clientSecret = params.payment_intent_client_secret
    const publishableKey = params.publishable_key

    const stripe = window.Stripe(publishableKey)

    let url = ''
    let success = true
    const _this = this
    await stripe.retrievePaymentIntent(clientSecret).then(async function (response) {
      const intent = response.paymentIntent
      if (intent && (intent.status === 'succeeded' || intent.status === 'processing')) {
        // if processing will update pending order
        if (intent.status === 'processing') {
          const endpoint = `/public/order/callback/stripe/${orderToken}?token=${orderToken}&transactionReference=${intent.id}`
          const data = await _this.$axios.$get(_this.$masterUrl(endpoint))
          success = data.success
        }
        if (success) {
          url = `/order/thank-you/${orderToken}`
        } else {
          url = `/checkout/${orderToken}`
        }
      } else {
        url = `/checkout/${orderToken}`
      }
    })

    window.location.href = this.localePath(url)
  }
}
</script>

<style scoped>
.juno_okyo {
  text-align: center;
  padding: 50px 0;
  height: 100px;
  font-size: 24px;
}
</style>
