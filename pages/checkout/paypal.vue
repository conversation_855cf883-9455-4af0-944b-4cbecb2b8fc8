<template>
  <main class="container">
    <div class="verify-payment">
      <div>
        <loading-dot variant="dark" />
      </div>
      <div>
        {{ $t('Verifying payment status...') }}
      </div>
    </div>
  </main>
</template>

<script>
import lscache from 'lscache'
import { API_LIST } from '~/helpers/variableConst'
export default {
  head () {
    return {
      title: this.$t('Verifying payment status...')
    }
  },
  async mounted () {
    const query = this.$route.query
    const token = query.token ?? null
    let orderToken = query.orderToken ?? null
    const PayerID = query.PayerID ?? null
    const paymentId = query.paymentId ?? null
    const gateId = query.gateId ?? null
    const region = query.region ?? null
    if (!orderToken) {
      if (lscache && lscache.supported()) {
        const cartData = lscache.get('cartData')
        orderToken = cartData?.token
      }
      if (orderToken) {
        await this.handleError(orderToken, 'Log:: Order token is missing')
      }
      window.location.href = this.localePath('/cart')
      return
    }
    const endpoint = `/public/order/callback/paypal/${orderToken}?token=${token}&payerId=${PayerID}&transactionReference=${paymentId}&gateId=${gateId}&region=${region}`

    const { success } = await this.$axios.$get(this.$masterUrl(endpoint))

    let url = ''

    if (success) {
      url = `/order/thank-you/${orderToken}`
    } else {
      url = `/checkout/${orderToken}`
    }

    this.$store.dispatch('cart/resetData')
    window.location.href = this.localePath(url)
  },
  methods: {
    async handleError (orderToken, errorMessage) {
      if (!errorMessage) {
        return
      }
      const message = JSON.stringify(errorMessage)
      // const storeInfo = this.$store.state.storeInfo
      // const updateCheckoutLogException = (this.$config?.appRegion === 'us' || !this.$config?.appRegion) ? (storeInfo?.enable_distributed_checkout === 1 ? API_LIST.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION : API_LIST.API_CHECKOUT_LOG_EXCEPTION) : API_LIST.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION
      const updateCheckoutLogException = API_LIST.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION

      await this.$axios.$post(updateCheckoutLogException, {
        message,
        token: orderToken
      }).then(() => {
        window.location.href = this.localePath(`/checkout/${orderToken}`)
      })
    }
  }
}
</script>

<style scoped>
.verify-payment {
  text-align: center;
  padding: 50px 0;
  height: 100px;
  font-size: 24px;
}
</style>
