<template>
  <Component
    :is="asyncComponent"
    v-if="status !== 'blocked'"
    :campaign-slug="campaignSlug"
  />
  <main v-else class="py-5">
    <div class="error-page mt-5 pt-5 text-center">
      <h2> {{ $t('This campaign was taken down due to a content violation') }} </h2>
    </div>
  </main>
</template>

<script>
export default {
  middleware: [
    'discount-code'
  ],
  asyncData,
  head,
  computed: {
    asyncComponent,
    title
  },
  watchQuery: ['product', 'color', 'size']
}

async function asyncData ({ params, error, store, query }) {
  const campaignSlug = params.campaign
  const data = await store.dispatch('campaign/getCampaign', {
    campaignSlug,
    productSlug: query.product
  }).catch((e) => {
    return error({
      statusCode: 400,
      error: e
    })
  })
  if (!data.success) {
    if (data.message === 'blocked') {
      return {
        status: 'blocked'
      }
    } else {
      return error({
        statusCode: 404,
        message: 'Page Not Found'
      })
    }
  }

  if (data.campaign && data.campaign.seller_id) {
    store.commit('changeSellerId', data.campaign.seller_id)
  }

  store.commit('campaign/UPDATE_CURRENT_CAMPAIGN_ID', data.campaign.id)
  store.commit('campaign/UPDATE_CURRENT_CAMPAIGN_SLUG', campaignSlug)

  return {
    status: 'success',
    campaignSlug,
    campaign: data.campaign,
    blocked: false
  }
}

function head () {
  if (this.status === 'blocked') {
    return {
      title: this.$t('This campaign is blocked')
    }
  }

  const title = (this.campaign && this.campaign.name) || ''
  const description = (this.campaign && ((this.campaign.description && this.campaign.description.replace(/<[^>]+>/g, '').replace(/\s\s+/g, ' ')) || this.campaign.name)) || ''
  let currentProduct = this.campaign.products[0]
  const attributes = this.campaign.attributes ? JSON.parse(this.campaign.attributes) : null
  const metaTitle = attributes && attributes?.meta_data?.meta_title && attributes?.meta_data?.meta_title.length ? attributes.meta_data.meta_title.slice(-1)[0] : title
  const metaDescription = attributes && attributes?.meta_data?.meta_description && attributes?.meta_data?.meta_description.length ? attributes.meta_data.meta_description.slice(-1)[0] : description
  if (this.$route.query.product) {
    currentProduct = this.campaign.products.find(product => this.$toKey(product.name) === this.$toKey(this.$route.query.product)) || this.campaign.products[0]
  } else if (this.campaign?.default_product_id) {
    currentProduct = this.campaign.products.find(product => product.id === this.campaign.default_product_id) || this.campaign.products[0]
  }

  const color = this.$route.query.color
  const image = this.$imgUrl(((currentProduct && (currentProduct.thumb_url || currentProduct.full_path))), 'share', 'webp', color ? this.$colorVal(color) : '') || ''
  const keywords = this.campaign.collections && this.campaign.collections.length && this.campaign.collections.map(item => item.name).join(', ')
  const metaKeywords = attributes && attributes?.meta_data?.meta_keywords && attributes?.meta_data?.meta_keywords.length ? attributes.meta_data.meta_keywords.slice(-1)[0] : keywords
  const storeName = this.campaign.store_name ?? ''
  const price = currentProduct.price
  const currency = currentProduct.currency_code
  const name = Math.max(...(this.campaign.collections && this.campaign.collections.length && this.campaign.collections.map(item => item.id)) || [])
  const SKU = this.campaign.id
  const campaignReviews = this.campaign.reviews
  const averageRating = campaignReviews && campaignReviews.average_rating ? campaignReviews.average_rating === 0 ? 4.5 : campaignReviews.average_rating : 4.5
  const bestRating = campaignReviews && campaignReviews.best_rating ? campaignReviews.best_rating : ''
  const worstRating = campaignReviews && campaignReviews.worst_rating ? campaignReviews.worst_rating : ''
  const ratingCount = campaignReviews && campaignReviews.review_count ? campaignReviews.review_count === 0 ? 5 : campaignReviews.review_count : 5
  return {
    title: `${this.title} | ${this.$store.state.storeInfo.name}`,
    meta: this.$createSEOMeta({ title: metaTitle ?? title, description: metaDescription ?? description, image, keywords: metaKeywords ?? keywords, price, currency, SKU, name }),
    script: [
      {
        type: 'application/ld+json',
        innerHTML: JSON.stringify({
          '@context': 'https://schema.org/',
          '@type': 'Product',
          url: `https://${this.$store.state.storeInfo.domain}/${this.campaignSlug}`,
          productID: SKU,
          name: title,
          image,
          description,
          brand: storeName,
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: averageRating,
            bestRating,
            worstRating,
            ratingCount
          }
        })
      }
    ]
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/campaign`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/campaign'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/campaign'
      )
  }
}

function title () {
  return `${(this.campaign && this.campaign.name) || 'product'} `
}
</script>
