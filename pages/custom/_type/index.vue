<template>
  <Component
    :is="asyncComponent"
    :custom-page="customPage"
    :title="title"
  />
</template>

<script>
import { API_LIST } from '~/helpers/variableConst'
export default {
  asyncData,
  head,
  computed: {
    asyncComponent
  }
}

async function asyncData ({ route, params, error, $axios, query }) {
  const fullPath = route.fullPath
  let paths = fullPath.split('/')
  paths = `${paths[paths.length - 2]}/${paths[paths.length - 1]}`
  const { data, success } = await $axios.$get(API_LIST.API_GENERATE_SEO, {
    params: {
      path: paths
    }
  }).catch((e) => {
    return error({
      statusCode: 400,
      error: e
    })
  })

  if (!success) {
    return error({
      statusCode: 404,
      message: 'Page Not Found'
    })
  }

  return {
    customPage: data,
    title: data.title || 'Custom page'
  }
}

function head () {
  return {
    title: `${this.title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title: this.title, description: this.title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.storeInfo.theme}/pages/custom/index`)
    return () =>
      import(
        '~/themes/' + this.storeInfo.theme + '/pages/custom/index'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/custom/index'
      )
  }
}
</script>
