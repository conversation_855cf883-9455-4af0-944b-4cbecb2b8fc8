<template>
  <Component
    :is="asyncComponent"
    :best-seller-product="best_seller"
    :new-arrivals-product="new_arrivals"
    :featured-product="featured"
    :other-featured="other"
  />
</template>

<script>
export default {
  name: 'HomeWraper',
  async asyncData ({ store }) {
    // eslint-disable-next-line camelcase
    const { best_seller, new_arrivals, featured, other } = await store.dispatch('listing/getHomePageCampaigns')
    return {
      best_seller,
      new_arrivals,
      featured,
      other
    }
  },
  data () {
    return {
      storeInfo: this.$store.state.storeInfo
    }
  },
  head,
  computed: { asyncComponent }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.storeInfo.theme}/pages/home`)
    return () =>
      import(
        '~/themes/' + this.storeInfo.theme + '/pages/home'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/home'
      )
  }
}

function head () {
  const title = this.storeInfo.name || ''
  const description = this.storeInfo.description || this.storeInfo.name || ''
  const image = this.$imgUrl((this.storeInfo.banners[0] && this.storeInfo.banners[0].banner_url) || '', 'share')
  return {
    title: this.storeInfo.name,
    meta: this.$createSEOMeta({ title, description, image })
  }
}
</script>
