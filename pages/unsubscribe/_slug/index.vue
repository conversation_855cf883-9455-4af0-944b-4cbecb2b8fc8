<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  const title = 'Unsubscribe'

  return {
    title: `${title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title, description: title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.storeInfo.theme}/pages/unsubscribe`)
    return () =>
      import(
        '~/themes/' + this.storeInfo.theme + '/pages/unsubscribe'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/unsubscribe'
      )
  }
}
</script>
