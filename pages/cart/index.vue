<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  name: 'Cart<PERSON>rap<PERSON>',
  middleware: [
    'discount-code'
  ],
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  const title = 'Shopping Bag'
  const description = 'Shopping Bag'

  return {
    title: 'Shopping Bag | ' + this.$store.state.storeInfo.name || '',
    meta: this.$createSEOMeta({ title, description })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/cart`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/cart'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/cart'
      )
  }
}
</script>
