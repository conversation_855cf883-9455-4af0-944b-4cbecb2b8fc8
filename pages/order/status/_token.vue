<template>
  <Component
    :is="asyncComponent"
    :tracking="tracking"
    :order="order"
    :timeframe="timeframe"
    :statement_descriptor="statement_descriptor"
  />
</template>
<script>
export default {
  asyncData,
  head,
  computed: { asyncComponent }
}

async function asyncData ({ store, params, error }) {
  // const campaignsSlide = await $axios.$get(API_LIST.API_GET_PRODUCT)
  const { success, data } = await store.dispatch('order/getOrderStatus', params.token)
  if (!(success || data)) {
    return error({
      statusCode: 404,
      message: 'Page Not Found'
    })
  }
  return {
    order: data.order || '',
    tracking: data.fulfillments || '',
    timeframe: data.timeframe || '',
    statement_descriptor: data.statement_descriptor || '',
    // related: campaignsSlide,
    title: 'Checkout status'
  }
}

function head () {
  return {
    title: `${this.title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title: this.title, description: this.title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/order/status`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/order/status'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/order/status'
      )
  }
}
</script>
