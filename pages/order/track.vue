<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  const title = 'Track order'

  return {
    title: `${title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title, description: title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/order/track`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/order/track'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/order/track'
      )
  }
}
</script>
