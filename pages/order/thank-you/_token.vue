<template>
  <Component
    :is="asyncComponent"
    :order="order"
    :gateways="gateways"
    :shipping-methods="shippingMethods"
    :statement_descriptor="statement_descriptor"
  />
</template>

<script>
export default {
  asyncData,
  head,
  computed: {
    asyncComponent
  },
  created
}

async function asyncData ({ store, params, error }) {
  const { success, data } = await store.dispatch('order/getOrderThankYou', params.token)
  if (!(success || data)) {
    return error({
      statusCode: 404,
      message: 'Page Not Found'
    })
  }

  return {
    order: data.order,
    gateways: data.payment_gateways,
    shippingMethods: data.shipping_methods,
    statement_descriptor: data.statement_descriptor || '',
    title: 'Order Summary'
  }
}

function head () {
  return {
    title: `${this.title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title: this.title, description: this.title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/order/thank-you`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/order/thank-you'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/order/thank-you'
      )
  }
}

function created () {
  if (process.browser) {
    const hash = this.$route.hash
    if (hash !== `#${this.order.id}`) {
      this.$router.push({ hash: `#${this.order.id}` })
    }
  }
}
</script>
