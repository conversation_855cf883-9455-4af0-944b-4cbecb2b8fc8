<template>
  <Component
    :is="asyncComponent"
    :blogs="blogs"
    @changePage="changePage"
  />
</template>

<script>
import { API_LIST } from '~/helpers/variableConst'

export default {
  name: '<PERSON>log<PERSON>llWrapper',
  asyncData,
  head,
  computed: {
    asyncComponent
  },
  methods: {
    changePage
  }
}

async function asyncData ({ $httpDefault, query }) {
  const page = query.page || 1
  const { data: blogs } = await $httpDefault('GET', `${API_LIST.API_BLOG}?page=${page}`)
  return {
    blogs
  }
}

function changePage (page) {
  this.$router.push({
    query: { ...this.$route.query, page }
  })
}

function head () {
  const title = 'Blog'
  const description = 'Blog'

  return {
    title: 'Blog | ' + this.$store.state.storeInfo.name || '',
    meta: this.$createSEOMeta({ title, description })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/blog/all`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/blog/all'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/blog/all'
      )
  }
}
</script>
