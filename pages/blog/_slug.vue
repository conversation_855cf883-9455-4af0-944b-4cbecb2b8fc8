<template>
  <Component
    :is="asyncComponent"
    :blog="blog"
  />
</template>

<script>
import { API_LIST } from '~/helpers/variableConst'

export default {
  name: 'CartWrapper',
  middleware: [
    'discount-code'
  ],
  asyncData,
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  console.log(this.$store.state.storeInfo)
  const title = 'Blog'
  const description = 'Blog'

  return {
    title: 'Blog | ' + this.blog.title,
    meta: this.$createSEOMeta({ title, description })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/blog/detail`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/blog/detail'
      )
  } catch (e) {
    console.log(e)
    return () =>
      import(
        '~/themes/default/pages/blog/detail'
      )
  }
}

async function asyncData ({ $httpDefault, params }) {
  const { data: blog } = await $httpDefault('GET', `${API_LIST.API_BLOG}/${params.slug}`)
  return {
    blog
  }
}
</script>
