<template>
  <Component
    :is="asyncComponent"
    :title="title"
    :content="content"
    :slug="slug"
  />
</template>

<script>
import lscache from 'lscache'

export default {
  async asyncData ({ store, params, error }) {
    const { domain } = store.state.storeInfo
    const cachedPage = lscache.get(`${domain}/${params.slug}`)
    if (cachedPage) {
      return cachedPage
    }
    const { success, data } = await store.dispatch('getPage', params.slug)

    if (!success) {
      return error({
        statusCode: 404,
        message: 'Page Not Found'
      })
    }
    const pageData = {
      title: data.title,
      slug: data.slug,
      content: data.content
    }
    lscache.set(`${domain}/${params.slug}`, pageData, 60 * 24 * 7)
    return pageData
  },
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  return {
    title: `${this.title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title: this.title, description: this.title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.storeInfo.theme}/pages/page`)
    return () =>
      import(
        '~/themes/' + this.storeInfo.theme + '/pages/page'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/page'
      )
  }
}
</script>

<style>
span#spemail {
  display: inline-block;
}

span#spemail span {
  float: right;
}
</style>
