<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  data () {
    return {
      title: 'FAQ'
    }
  },
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  return {
    title: `${this.title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title: this.title, description: this.title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.storeInfo.theme}/pages/page/faq`)
    return () =>
      import(
        '~/themes/' + this.storeInfo.theme + '/pages/page/faq'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/page/faq'
      )
  }
}
</script>
