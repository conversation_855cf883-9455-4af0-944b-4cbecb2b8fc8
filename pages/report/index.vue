<template>
  <Component
    :is="asyncComponent"
  />
</template>

<script>
export default {
  head,
  computed: {
    asyncComponent
  }
}

function head () {
  const title = 'Report'

  return {
    title: `${title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title, description: title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.storeInfo.theme}/pages/report`)
    return () =>
      import(
        '~/themes/' + this.storeInfo.theme + '/pages/report'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/report'
      )
  }
}
</script>
