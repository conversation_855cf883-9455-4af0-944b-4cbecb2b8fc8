import qs from 'qs'
import { API_LIST, METHOD } from '~/helpers/variableConst'

const state = getDefaultState

const mutations = {
  UPDATE_PRODUCT_REVIEW_STATE,
  RESET_DATA
}

const actions = {
  resetData,
  getReviewSummary,
  getReviews,
  createProductReview
}

function getDefaultState () {
  return {
    productReviews: {}
  }
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
}

function UPDATE_PRODUCT_REVIEW_STATE (state, { key, data }) {
  if (state.productReviews[key]) {
    Object.assign(state.productReviews[key], data)
  } else {
    state.productReviews[key] = data
  }
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

function getReviewSummary ({ commit, state }, { campaignId, templateId }) {
  const key = campaignId + '_' + templateId

  if (state.productReviews[key] && state.productReviews[key].reviewSummary) {
    return ({
      success: true,
      reviewSummary: state.productReviews[key].reviewSummary
    })
  }

  const api = `${API_LIST.API_PRODUCT_REVIEW}/${encodeURIComponent(templateId)}/summary?${qs.stringify({ campaignId })}`
  return this.$httpDefault(METHOD.get, api).then((result) => {
    if (result && result.success) {
      commit('UPDATE_PRODUCT_REVIEW_STATE', {
        key,
        data: {
          reviewSummary: result.data
        }
      })

      return {
        success: true,
        reviewSummary: result.data
      }
    }

    return result
  })
}

function getReviews ({ commit, state }, { campaignId, templateId, filter, page }) {
  const key = campaignId + '_' + templateId + '_' + filter + '_' + page

  if (state.productReviews[key] && state.productReviews[key].reviews) {
    return ({
      success: true,
      data: state.productReviews[key].reviews
    })
  }

  const api = `${API_LIST.API_PRODUCT_REVIEW}/${encodeURIComponent(templateId)}/reviews?${qs.stringify({ campaignId, filter, page })}`
  return this.$httpDefault(METHOD.get, api).then((result) => {
    if (result && result.success) {
      commit('UPDATE_PRODUCT_REVIEW_STATE', {
        key,
        data: {
          reviews: result.data
        }
      })

      return {
        success: true,
        data: result.data
      }
    }
  })
}

function createProductReview (context, data) {
  return this.$httpWrite(METHOD.post, API_LIST.API_CREATE_PRODUCT_REVIEW, data)
}

export {
  state,
  mutations,
  actions
}
