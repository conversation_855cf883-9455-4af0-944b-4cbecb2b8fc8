// import axios from 'axios'
const state = getDefaultState

const getters = {
  getDisabledCountryCheckout
}

const mutations = {
  UPDATE_DATA,
  RESET_DATA
}

const actions = {
  resetData,
  // getData
}

function getDefaultState () {
  return {
    categories: [],
    colors: [],
    countries: [],
    currencies: [],
    languages: [],
    size_guides: [],
    templates: [],
    country_disabled_checkout: [],
    checkout_form_config: null,
  }
}

function UPDATE_DATA (state, data) {
  Object.assign(state, data)
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

function getDisabledCountryCheckout (state) {
  if (state.country_disabled_checkout && state.country_disabled_checkout.length > 0) {
    return state.country_disabled_checkout
  }
  return ['MX', 'RU', 'UA', 'BR', 'CU', 'FK', 'IR', 'KP', 'SO', 'SD']
}

// function getData ({ commit }) {
//   return this.$httpDefault(METHOD.get, API_LIST.API_GET_GENERAL_INFO).then((result) => {
//     commit('UPDATE_DATA', result.data)
//     return result
//   })
// }
export {
  state,
  getters,
  mutations,
  actions
}
