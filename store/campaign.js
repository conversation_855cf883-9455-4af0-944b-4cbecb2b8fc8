import cloneDeep from 'lodash/cloneDeep'
import qs from 'qs'
import lscache from 'lscache'
import { API_LIST, DOMAIN_MARKET_PLACE, METHOD } from '~/helpers/variableConst'

const state = getDefaultState

const getters = {
  getCampaignBySlug
}

const mutations = {
  UPDATE_CAMPAIGN_LIST,
  UPDATE_SHOW_MODAL,
  UPDATE_CURRENT_CAMPAIGN_ID,
  UPDATE_CURRENT_CAMPAIGN_SLUG,
  UPDATE_CURRENT_MODAL_CAMPAIGN_SLUG,
  UPDATE_CURRENT_SIZEGUIDE_TEMPLATE,
  RESET_DATA,
  UPDATE_CAMPAIGN_DATA_FIELD,
  UPDATE_CURRENT_PRODUCT_SLUG,
  UPDATE_CAMPAIGN_FIELD_DATA,
  UPDATE_PRODUCT_VARIANT_STATE
}

const actions = {
  resetData,
  getCampaign,
  getPromotion,
  getBundleDiscount,
  getProductStats,
  getProductCustom,
  openCampaignModal,
  getVariantsCampaignProduct,
  getVariantsProduct,
  getVariantsOfProduct
}

function getDefaultState () {
  return {
    isShowModal: false,
    sizeGuideTemplate: false,
    currentCampaignId: '',
    currentCampaignSlug: '',
    currentModalCampaignSlug: '',
    campaignList: {},
    currentProductSlug: '',
    productVariants: {}
  }
}

// getters
function getCampaignBySlug (state) {
  return function (slug) {
    return cloneDeep(state.campaignList[slug])
  }
}

function getVariantsOfProduct (state) {
  return function (productId) {
    return state.productVariants[productId]
  }
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
}

function UPDATE_CAMPAIGN_LIST (state, { campaignSlug, data }) {
  if (state.campaignList[campaignSlug]) {
    Object.assign(state.campaignList[campaignSlug], data)
  } else {
    state.campaignList[campaignSlug] = data
  }
}

function UPDATE_SHOW_MODAL (state, value) {
  state.isShowModal = !!value
}

function UPDATE_CURRENT_CAMPAIGN_ID (state, value) {
  state.currentCampaignId = value
}

function UPDATE_CURRENT_CAMPAIGN_SLUG (state, value) {
  state.currentCampaignSlug = value
}

function UPDATE_CURRENT_PRODUCT_SLUG (state, value) {
  state.currentProductSlug = value
}

function UPDATE_CURRENT_MODAL_CAMPAIGN_SLUG (state, value) {
  state.currentModalCampaignSlug = value
}

function UPDATE_CURRENT_SIZEGUIDE_TEMPLATE (state, value) {
  state.sizeGuideTemplate = value
}

function UPDATE_CAMPAIGN_DATA_FIELD (state, { campaignSlug, key, value }) {
  if (state.campaignList[campaignSlug]) {
    state.campaignList[campaignSlug].campaignData[key] = value
  }
}

function UPDATE_CAMPAIGN_FIELD_DATA (state, { campaignSlug, field, key, value }) {
  state.campaignList[campaignSlug][field][key] = value
}

function UPDATE_PRODUCT_VARIANT_STATE (state, { key, data }) {
  key = parseInt(key)
  let productVariants = lscache.get('product-variants') ?? null
  if (productVariants && productVariants[key]) {
    productVariants[key] = data
  } else {
    productVariants = { ...productVariants, [key]: data }
  }
  lscache.set('product-variants', productVariants)
  lscache.set(`${key}-saved-at`, Date.now())
  // if (state.productVariants[key]) {
  //   Object.assign(state.productVariants[key], data)
  // } else {
  //   state.productVariants[key] = data
  // }
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

function getCampaign ({ commit, state }, { campaignSlug, productSlug = '' }) {
  if (state.campaignList[campaignSlug] && state.campaignList[campaignSlug].campaignData) {
    return ({
      success: true,
      campaign: state.campaignList[campaignSlug].campaignData,
      images: state.campaignList[campaignSlug].images,
      variants: state.campaignList[campaignSlug].variants ?? []
    })
  }

  const campaignApi = `${API_LIST.API_GET_SINGLE_PRODUCT}/${encodeURIComponent(campaignSlug)}?${qs.stringify(
    {
      product: productSlug
    }
  )}`
  return this.$httpDefault(METHOD.get, campaignApi).then((result) => {
    if (result && result.success) {
      const data = {
        campaignData: result.data,
        images: result.data?.images,
        variants: result.data?.variants
      }

      commit('UPDATE_CAMPAIGN_LIST', {
        campaignSlug,
        data
      })

      if (result.data?.variants.length > 0) {
        const productId = result.data?.current_product_id
        try {
          commit('UPDATE_PRODUCT_VARIANT_STATE', {
            key: productId,
            data: result.data?.variants
          })
        } catch (e) {
        }
      }

      return {
        success: true,
        campaign: result.data,
        images: result.data?.images,
        variants: result.data?.variants
      }
    } else if (result) {
      return result
    } else {
      return result
    }
  })
}

function getPromotion ({ commit, state }, { campaignSlug, campaignIds }) {
  if (campaignSlug && state.campaignList[campaignSlug] && state.campaignList[campaignSlug].promotions) {
    return {
      success: true,
      data: state.campaignList[campaignSlug].promotions
    }
  }
  const promotionApi = `${API_LIST.API_PROMOTION}?campaign_ids[]=${campaignIds.toString()}`
  return this.$httpDefault(METHOD.get, promotionApi).then((result) => {
    if (result && result.success && campaignSlug) {
      commit('UPDATE_CAMPAIGN_LIST', {
        campaignSlug,
        data: {
          promotions: result.data
        }
      })
    }
    return result
  })
}

function getBundleDiscount ({ commit, state }, { campaignSlug, campaignIds, viewPlace, productIds, isRefetch = false, bundleIds = [] }) {
  try {
    if (!isRefetch && campaignSlug && state.campaignList[campaignSlug] && state.campaignList[campaignSlug].bundleDiscount) {
      return {
        success: true,
        data: state.campaignList[campaignSlug].bundleDiscount
      }
    }
    let bundleDiscountApi = `${API_LIST.API_BUNDLE_DISCOUNT}?campaign_ids[]=${campaignIds.toString()}&position=${viewPlace}`
    if (productIds.length > 0) {
      bundleDiscountApi = `${bundleDiscountApi}&product_ids[]=${productIds.toString()}`
    }
    if (bundleIds.length > 0) {
      productIds = Array.from(new Set(productIds))
      bundleDiscountApi = `${bundleDiscountApi}&bundle_ids[]=${bundleIds.toString()}`
    }
    console.log('bundle discount api : ', bundleDiscountApi)

    return this.$httpDefault(METHOD.get, bundleDiscountApi).then((result) => {
      if (result && result.success && campaignSlug) {
        result.data = this.$correctTestPriceCampaignAtBundleDiscount(result.data)
        commit('UPDATE_CAMPAIGN_LIST', {
          campaignSlug,
          data: {
            bundleDiscount: result.data
          }
        })
      }
      return result
    })
  } catch (e) {
    console.error('getBundleDiscount : ', productIds)
  }
}

// eslint-disable-next-line camelcase
function getProductStats ({ commit, state }, { campaignSlug, campaign_id, params }) {
  if (campaignSlug && state.campaignList[campaignSlug] && state.campaignList[campaignSlug].productStats) {
    return {
      success: true,
      data: state.campaignList[campaignSlug].productStats
    }
  }
  const productStatsApi = `${API_LIST.API_PRODUCTS_STATS}?${qs.stringify(params)}`
  return this.$httpStats(METHOD.post, productStatsApi, { campaign_id }).then((result) => {
    if (result && result.success && campaignSlug) {
      commit('UPDATE_CAMPAIGN_LIST', {
        campaignSlug,
        data: {
          productStats: result.data
        }
      })
    }
    return result
  })
}

// eslint-disable-next-line camelcase
function getProductCustom ({ commit, state }, { campaignSlug }) {
  if (campaignSlug && state.campaignList[campaignSlug] && state.campaignList[campaignSlug].customData) {
    return {
      success: true,
      data: state.campaignList[campaignSlug].customData
    }
  }
  // eslint-disable-next-line camelcase
  const customApi = `${API_LIST.API_GET_SINGLE_PRODUCT}/${campaignSlug}/custom-designs`
  return this.$httpDefault(METHOD.get, customApi).then((result) => {
    if (result && result.success && campaignSlug) {
      commit('UPDATE_CAMPAIGN_LIST', {
        campaignSlug,
        data: {
          customData: result.data
        }
      })
    }
    return result
  })
}

function openCampaignModal ({ commit, dispatch }, slug) {
  return dispatch('getCampaign', { campaignSlug: slug }).then((result) => {
    if (!result.success) {
      return this.$toast.error(this.$i18n.t('Missing Info'))
    }
    commit('UPDATE_CURRENT_MODAL_CAMPAIGN_SLUG', slug)
    commit('UPDATE_SHOW_MODAL', true)
    return result
  }).catch(() => {
    this.$toast.error(this.$i18n.t('Missing Info'))
  })
}

async function getVariantsCampaignProduct ({ commit, state }, { campaignSlug, productId, productSlug, isProductPage = false }) {
  if (!productId || !campaignSlug) {
    return null
  }
  if (isProductPage) {
    commit('UPDATE_CURRENT_PRODUCT_SLUG', productSlug)
  }

  if (state.campaignList[campaignSlug] && state.campaignList[campaignSlug].variants && state.campaignList[campaignSlug].variants[productSlug]) {
    return state.campaignList[campaignSlug].variants[productSlug]
  }

  const productVariantsApi = `${API_LIST.API_GET_PRODUCT_VARIANTS}/${productId}`
  return await this.$httpStats(METHOD.get, productVariantsApi).then((result) => {
    if (result && result.success && campaignSlug) {
      commit('UPDATE_CAMPAIGN_FIELD_DATA', {
        campaignSlug,
        field: 'variants',
        key: productSlug,
        value: result.data
      })
    }

    return result.data
  })
}

async function getVariantsProduct ({ commit, state }, { productId, sellerId }) {
  if (!productId) {
    return null
  }

  // if (state.productVariants[productId]) {
  //   return state.productVariants[productId]
  // }

  const productVariantsInCache = lscache.get('product-variants') ?? null
  if (productVariantsInCache && productVariantsInCache[productId]) {
    return productVariantsInCache[productId]
  }

  let productVariantsApi = `${API_LIST.API_GET_PRODUCT_VARIANTS}/${productId}`
  const domain = window.location.hostname
  if (this.$config.appEnv !== 'production' || DOMAIN_MARKET_PLACE.includes(domain)) {
    productVariantsApi += `?seller_id=${sellerId}`
  }
  return await this.$httpStats(METHOD.get, productVariantsApi).then((result) => {
    if (result && result.success) {
      commit('UPDATE_PRODUCT_VARIANT_STATE', {
        key: productId,
        data: result.data
      })
    }

    return result.data
  })
}

export {
  state,
  getters,
  mutations,
  actions
}
