import lscache from 'lscache'
import { API_LIST, METHOD } from '~/helpers/variableConst'
const state = getDefaultState
const dateFormat = { month: 'short', day: 'numeric' }
const getters = {

}

const mutations = {
  RESET_DATA,
  UPDATE_ORDER_DATA,
  UPDATE_LOADING_CHECKOUT,
  USE_RECAPTCHA
}

const actions = {
  resetData,
  createOrder,
  createOrderWithRecaptcha,
  getOrderData,
  updateOrderData,
  getOrderThankYou,
  getOrderStatus,
  updateCustomerInfo,
  confirmAddress,
  updateCartKey,
  confirmEmail,
  deleteRequestCancelOrder,
  confirmCancelOrder
}

function getDefaultState () {
  return {
    isLoadingCheckout: false,
    order: {},
    gateways: [],
    paymentDomain: null,
    shippingMethods: [],
    needRecaptchaToken: false
  }
}

function USE_RECAPTCHA (state, data) {
  state.needRecaptchaToken = data
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
}

/* eslint-disable camelcase */
function UPDATE_ORDER_DATA (state, { order, payment_gateways, payment_domain, shipping_methods, separate_name }) {
  if (order) {
    state.order = { ...state.order, ...order }
  }

  if (payment_gateways) {
    state.gateways = payment_gateways
  }

  if (payment_domain) {
    state.paymentDomain = payment_domain
  }

  state.isSeparateName = separate_name || false

  const ONE_DAY = 3600 * 1000 * 24
  if (shipping_methods) {
    if (shipping_methods) {
      shipping_methods.map((shippingMethod) => {
        const shippingTime = shippingMethod.shipping_time ?? [3, 10]
        const printingTime = shippingMethod.printing_time ?? 3
        let description = ''
        const { date: dateLocale } = this.$i18n.localeProperties
        if (shippingMethod.name === 'standard') {
          const date = new Date(Date.now() + ONE_DAY * printingTime)
          description = this.$i18n.t('Printed before _day', { day: date.toLocaleDateString(dateLocale, dateFormat) })
          // description += "<br><span class='note'>* Due to the peak season, we do not guarantee delivery before Christmas and New Year.</span>"
        }
        if (shippingMethod.name === 'express') {
          description = this.$i18n.t('We print your order immediately (no cancellation allowed)')
        }
        const date1 = new Date(Date.now() + ONE_DAY * (shippingTime[0] + printingTime))
        const date2 = new Date(Date.now() + ONE_DAY * (shippingTime[1] + printingTime))
        description += '. ' + this.$i18n.t('Estimated delivery from _day1 to _day2.', { day1: date1.toLocaleDateString(dateLocale, dateFormat), day2: date2.toLocaleDateString(dateLocale, dateFormat) })
        shippingMethod.description = description
      })
    }
    state.shippingMethods = shipping_methods
  }
}
function UPDATE_LOADING_CHECKOUT (state, data) {
  state.isLoadingCheckout = data
}
/* eslint-enable camelcase */

async function createOrderWithRecaptcha ({ state, commit, dispatch }) {
  // prevent duplicated requests (called from multi places)
  if (state.isLoadingCheckout) {
    return
  }

  commit('USE_RECAPTCHA', true)
  await dispatch('createOrder')
}

async function createOrder ({ state, commit, dispatch, rootState }, data) {
  commit('UPDATE_LOADING_CHECKOUT', true)
  // Thông tin user lấy từ localStorage (thông tin shipping user đã nhập ở trang checkout)
  let userInfo = this.$lscache.get('userInfo')
  if (!userInfo || typeof userInfo !== 'object') {
    userInfo = {}
  }

  // Thông tin visit lấy từ localStorage (thông tin vị trí user truy cập vào trang web)
  // Nếu chưa có country thì detect thông tin vị trí user truy cập vào trang web từ API
  const visitInfo = this.$getVisitInfo()
  if (!visitInfo?.country) {
    visitInfo.country = await dispatch('detectCountry', null, { root: true }).catch(this.$log).countryCode
  }

  if (!visitInfo?.session_id && lscache.supported()) {
    visitInfo.session_id = lscache.get('senprints-session-id')
  }

  // Khi user chưa tạo đơn lần nào thì userInfo chưa được lưu ở localStorage.
  // Do đó khi tạo đơn lần đầu thì phải lấy thông tin country từ visitInfo
  // để lưu vào userInfo để backend có thể tạo đơn chính xác theo vị trí của user
  // đang truy cập.
  if (!userInfo?.country) {
    userInfo.country = visitInfo.country
  }

  delete visitInfo.dd

  const postData = {
    ...{
      email: userInfo.email,
      products: rootState.cart.products,
      country: ['MX', 'RU', 'UA'].includes(userInfo.country) ? 'US' : userInfo.country || 'US',
      token: rootState.cart.token,
      discount_code: rootState.cart.discount,
      currency_code: this.$lscache.get('currency')?.code ?? '',
      visit_info: visitInfo,
      correct_price: this.$lscache.get('correct_price') === true
    },
    ...data
  }

  try {
    if (state.needRecaptchaToken) {
      postData.recaptcha_token = await this.$recaptcha.getResponse()
    }
  } catch (e) {
    // noop, let's user continue without recaptcha
  }

  // const storeInfo = await dispatch('storeInfo/getStoreState', null, { root: true })
  // const createOrderAPI = (this.$config?.appRegion === 'us' || !this.$config?.appRegion) ? (storeInfo?.enable_distributed_checkout === 1 ? API_LIST.API_CREATE_DISTRIBUTED_CHECKOUT : API_LIST.API_CREATE_ORDER) : API_LIST.API_CREATE_DISTRIBUTED_CHECKOUT
  const createOrderAPI = API_LIST.API_CREATE_DISTRIBUTED_CHECKOUT

  return this.$httpWrite(METHOD.post, createOrderAPI, postData)
    .then((json) => {
      if (json && json.success) {
        commit('cart/UPDATE_CART_DATA', { token: json.data }, { root: true })
        this.$lscache.set('userInfo', userInfo)
        this.$lscache.set('visitInfo', visitInfo)
        this.$router.push(this.localePath({ path: `/checkout/${json.data}` }))
        commit('UPDATE_LOADING_CHECKOUT', false)
        commit('USE_RECAPTCHA', false)
      } else {
        if (json && json.message && json.message === 'need_verify') {
          this.$bus.$emit('confirm-recaptcha')
        } else if (json && !json.success && json.message.email) {
          this.$toast.error(json.message.email)
        } else {
          this.$toast.error(`Error: ${json.message && (json.message.content || json.message.error)}`)
        }

        commit('UPDATE_LOADING_CHECKOUT', false)
      }

      return json
    })
}

function getOrderData ({ commit, dispatch }, token) {
  // const storeInfo = await dispatch('storeInfo/getStoreState', null, { root: true })
  // const getOrderAPI = (this.$config?.appRegion === 'us' || !this.$config?.appRegion) ? (storeInfo?.enable_distributed_checkout === 1 ? API_LIST.API_GET_DISTRIBUTED_CHECKOUT : API_LIST.API_GET_ORDER) : API_LIST.API_GET_DISTRIBUTED_CHECKOUT
  const getOrderAPI = API_LIST.API_GET_DISTRIBUTED_CHECKOUT

  return this.$httpWrite(METHOD.get, `${getOrderAPI}/${token}`).then((result) => {
    if (result.success && result.data) {
      commit('UPDATE_ORDER_DATA', result.data)
    }
    return result
  })
}

function getOrderThankYou ({ commit, dispatch }, token) {
  const getOrderAPI = API_LIST.API_GET_ORDER
  return this.$httpWrite(METHOD.get, `${getOrderAPI}/${token}?ref=thank_you`)
}

function getOrderStatus ({ commit, dispatch }, token) {
  const getOrderAPI = API_LIST.API_GET_ORDER
  return this.$httpDefault(METHOD.get, `${getOrderAPI}/${token}/detail`)
}

function updateOrderData ({ commit, dispatch }, data) {
  // const storeInfo = await dispatch('storeInfo/getStoreState', null, { root: true })
  // const updateOrderAPI = (this.$config?.appRegion === 'us' || !this.$config?.appRegion) ? (storeInfo?.enable_distributed_checkout === 1 ? API_LIST.API_UPDATE_DISTRIBUTED_CHECKOUT : API_LIST.API_UPDATE_ORDER) : API_LIST.API_UPDATE_DISTRIBUTED_CHECKOUT
  const updateOrderAPI = API_LIST.API_UPDATE_DISTRIBUTED_CHECKOUT

  return this.$httpWrite(METHOD.post, `${updateOrderAPI}`, data).then((result) => {
    if (result.success && result.data) {
      if (result.data.order) {
        commit('UPDATE_ORDER_DATA', result.data)
      } else {
        commit('UPDATE_ORDER_DATA', { order: result.data })
      }
    }
    return result
  })
}

function updateCustomerInfo ({ commit }, data) {
  const userId = lscache.get('visitInfo')?.user_id ?? null
  return this.$httpWrite(METHOD.put, `${API_LIST.API_CUSTOMER_INFO}`, data, {
    'x-user-id': userId
  })
}

function confirmAddress ({ commit }, token) {
  return this.$httpWrite(METHOD.put, `${API_LIST.API_CONFIRM_ADDRESS}/${token}`)
}

function updateCartKey ({ commit }, orderKey) {
  return this.$httpWrite(METHOD.post, API_LIST.API_CART_KEY, orderKey)
}

function confirmEmail ({ commit }, email) {
  return this.$httpDefault(METHOD.post, API_LIST.API_EMAIL_VALIDATION, { email })
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

function confirmCancelOrder ({ commit }, token) {
  const userId = lscache.get('visitInfo')?.user_id ?? null
  return this.$httpDefault(METHOD.put, `${API_LIST.API_CONFIRM_CANCEL_ORDER}/${token}`, null, {
    'x-user-id': userId
  })
}

function deleteRequestCancelOrder ({ commit }, token) {
  const userId = lscache.get('visitInfo')?.user_id ?? null
  return this.$httpDefault(METHOD.del, `${API_LIST.API_CONFIRM_CANCEL_ORDER}/${token}`, null, {
    'x-user-id': userId
  })
}

export {
  state,
  getters,
  mutations,
  actions
}
