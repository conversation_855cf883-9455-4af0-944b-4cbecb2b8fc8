// import axios from 'axios'
// import { API_LIST, METHOD } from '~/helpers/variableConst'

const state = getDefaultState

const getters = {
  getStoreHeadTags
}

const mutations = {
  UPDATE_DATA,
  RESET_DATA
}

const actions = {
  resetData,
  getStoreState
  // getData
}

function getDefaultState () {
  return {
    id: null,
    seller_id: null,
    banner_url: '',
    banners: [],
    collections: [],
    company_id: null,
    currentDomain: '',
    discount_code: null,
    domain: '',
    foot_line: null,
    footerMenu: [],
    headerMenu: [],
    languages: [],
    logo_full_path: '',
    logo_url: '',
    name: '',
    primaryColor: '',
    promotion_title: null,
    socialsLink: {},
    store_type: '',
    show_payment_button: 0,
    collection_banners: [],
    enable_add_to_cart: true,
    head_tags: [],
    payment_gateway_type: '',
    enable_dynamic_base_cost: 0,
    order_summary_position: 'top',
    enable_distributed_checkout: 0,
    paypal_enable_card: 0,
  }
}

function UPDATE_DATA (state, data) {
  Object.assign(state, data)
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

function getStoreHeadTags (state) {
  return state.head_tags.reduce((returnObj, tag) => {
    if (['script_src', 'script', 'style'].includes(tag.tag)) {
      return returnObj // skip if tag matches type
    }

    if (!Object.hasOwnProperty.call(returnObj, tag.position)) {
      returnObj[tag.position] = []
    }

    returnObj[tag.position].push(tag)

    return returnObj
  }, ({}))
}

function getStoreState ({ state }) {
  return state
}

// function getData ({ commit }) {
//   return this.$httpDefault(METHOD.get, API_LIST.API_GET_STOREFRONT_INFO).then((result) => {
//     if (result.success) {
//       commit('UPDATE_DATA', result.data)
//       commit('changeSellerId', result.data.seller_id, { root: true })
//     }
//     return result
//   })
// }

export {
  state,
  getters,
  mutations,
  actions
}
