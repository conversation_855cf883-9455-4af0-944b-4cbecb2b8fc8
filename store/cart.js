// import axios from 'axios'
import _ from 'lodash'
import lscache from 'lscache'
import cloneDeep from 'lodash/cloneDeep'
import each from 'lodash/each'
import qs from 'qs'
import { API_LIST, METHOD } from '~/helpers/variableConst'
import { getCurrentTimeInUTC } from '~/helpers/function'
import { getFilterImages } from '~/helpers/campaign'

const state = getDefaultState

const getters = {
  getTotalQuantity
}

const mutations = {
  UPDATE_CART_DATA,
  ADD_CART_ITEM,
  UPDATE_CART_ITEM,
  DUPLICATE_CART_ITEM,
  UPDATE_CART_ITEM_BY_ID,
  REMOVE_CART_ITEM,
  UPDATE_RELATED_CART_DATA,
  RESET_DATA,
  UPDATE_FIELD_IN_ALL_CART_ITEM
}

const actions = {
  resetData,
  addProduct,
  addCartItem,
  addProductBundleDiscount,
  getCartDataFromStorage,
  getRelatedCart,
  postRelatedCart
}

function getDefaultState () {
  return {
    products: [],
    relatedCart: [],
    token: '',
    discount: '',
  }
}

function getTotalQuantity (state) {
  return state.products.reduce((previousValue, currentValue) => previousValue + currentValue.quantity, 0)
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
  lscache.set('cartData', state)
}

function UPDATE_CART_DATA (state, updateData) {
  Object.assign(state, updateData)
  lscache.set('cartData', state)
}

function ADD_CART_ITEM (state, cartItem) {
  state.products.push(cartItem)
  lscache.set('cartData', state)
}

function UPDATE_CART_ITEM (state, { index, data }) {
  const cartItem = state.products[index]
  if (cartItem) {
    Object.assign(cartItem, data)
  }
  lscache.set('cartData', state)
}

function UPDATE_CART_ITEM_BY_ID (state, { cartItemId, data }) {
  const cartItem = state.products.find(item => item.cartItemId === cartItemId)
  if (cartItem) {
    Object.assign(cartItem, data)
  }
  lscache.set('cartData', state)
}

function UPDATE_FIELD_IN_ALL_CART_ITEM (state, { field, data }) {
  state.products.forEach((element) => {
    if (Object.prototype.hasOwnProperty.call(element, field)) {
      // Update the field with the new data
      element[field] = data
    }
  })
  lscache.set('cartData', state)
}

function DUPLICATE_CART_ITEM (state, index) {
  const newCartItem = cloneDeep(state.products[index])
  const cartItemId = `${Date.now()}_${state.products.length}`
  newCartItem.cartItemId = cartItemId
  state.products.splice((index + 1), 0, newCartItem)
  lscache.set('cartData', state)
}

function REMOVE_CART_ITEM (state, index) {
  state.products.splice(index, 1)
  lscache.set('cartData', state)
}

function UPDATE_RELATED_CART_DATA (state, data) {
  state.relatedCart = data || []
}

function getCartDataFromStorage ({ commit }) {
  const cartData = lscache.get('cartData')
  // correct old data
  if (cartData && cartData.products && cartData.products.length) {
    cartData.products.map((item) => {
      if (item.custom_options && typeof item.custom_options === 'string') {
        item.custom_options = JSON.parse(item.custom_options)
      }
    })
  }
  commit('UPDATE_CART_DATA', cartData)
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

function addProduct ({ dispatch, state }, { campaign, product, options, optionList, variant, quantity, filterImages, filterDesigns, isCheckQuantity, designThumb, customDesign, pbCustomInfo, pbPrintUrl, customerCustomOptions, extraCustomFee }) {
  if (!(campaign || product || options || quantity)) {
    return {
      success: false,
      message: this.$i18n.t('Missing data')
    }
  }

  const cartItemId = `${Date.now()}_${state.products.length}`
  dispatch('addCartItem', { campaign, product, options, variant, quantity, filterImages, isCheckQuantity, filterDesigns, designThumb, customDesign, cartItemId, pbCustomInfo, pbPrintUrl, customerCustomOptions, extraCustomFee })

  return {
    success: true,
    cartItemId,
    index: state.products.length - 1
  }
}

function addCartItem ({ state, commit }, { campaign, product, options, variant, quantity, filterImages, isCheckQuantity, filterDesigns, designThumb, customDesign, cartItemId, pbCustomInfo, pbPrintUrl, customerCustomOptions, extraCustomFee = 0, campaignBundleId, promotion, productBundleId = null }) {
  const params = new URLSearchParams()
  const newOptions = {}

  if (options) {
    Object.keys(options).map((item) => {
      params.set(item, options[item])
      if (item !== 'product') {
        newOptions[item] = options[item].replace(/-/g, ' ')
      }
    })
  }

  let itemInCart = state.products.find(item => this.$shallowEqual(item.options, newOptions) && (item.product_id === product.id && item.campaign_id === product.campaign_id))
  let thumbUrl = designThumb || product.thumb_url || (filterImages && filterImages[0] && filterImages[0].file_url)
  if (campaign.system_type === 'custom') {
    const validImages = getFilterImages(campaign, product, options.color)
    thumbUrl = validImages[0].file_url
  }
  if (options && options.color) {
    const color = this.$colorVal(options.color.replaceAll('-', ' '))
    if (color) {
      thumbUrl = thumbUrl.replace(/co_rgb:.{6}/, `co_rgb:${color && color.replaceAll('#', '')}`)
      thumbUrl = thumbUrl.replace(/&c=[0-9a-fA-F]{6}/, `&c=${color && color.replaceAll('#', '')}`)
    }
  }
  const isExistItemCustomOption = product.full_printed === 5 || campaign.personalized === 3 ? itemInCart && customerCustomOptions && customerCustomOptions.length && itemInCart.customer_custom_options && _.isEqual(itemInCart.customer_custom_options, customerCustomOptions) : true
  if (itemInCart && isExistItemCustomOption && !((filterDesigns && filterDesigns.length) || (customerCustomOptions && customerCustomOptions.length))) {
    commit('UPDATE_CART_ITEM_BY_ID', {
      cartItemId: itemInCart.cartItemId,
      data: {
        quantity: itemInCart.quantity + quantity,
        isCheckQuantity
      }
    })
  } else {
    itemInCart = {
      cartItemId,
      campaign_id: campaign.id,
      campaign_title: campaign.name,
      campaign_slug: campaign.slug,
      product_id: product.id,
      product_name: product.name,
      full_printed: product.full_printed,
      thumb_url: thumbUrl,
      currency_code: product.currency_code,
      options: newOptions,
      price: product.price + extraCustomFee,
      quantity,
      isCheckQuantity,
      extra_custom_fee: extraCustomFee,
      campaignBundleId,
      promotion,
      seller_id: campaign.seller_id,
      via: campaign?.seller?.via,
      productBundleId
    }
    if (variant && variant.price) {
      itemInCart.variantPrice = variant.price + extraCustomFee
    }

    if (campaign && campaign.personalized) {
      if (campaign.personalized === 1 && filterDesigns && filterDesigns.length) {
        itemInCart.designs = {}
        const customOptions = {}
        filterDesigns && filterDesigns.forEach((item) => {
          const designJson = item.canvas.exportJson()
          itemInCart.designs[item.designData.print_space] = JSON.stringify(item.canvas.exportJson())
          designJson.objects.forEach((object) => {
            if (object.type === 'i-text') {
              customOptions[object.name] = object.text
              params.set(object.name && object.name.replaceAll(' ', '-'), object.text)
            }
            if (object.type === 'image' && object.isCustom) {
              customOptions.customImage = item.currentFileUploadUrl
              params.set('custom-image', item.currentFileUploadUrl)
            }
          })
          if (item.customDesign) {
            customOptions.design = item.customDesign
          }
        })
        itemInCart.custom_options = customOptions
      } else if (campaign.personalized === 2 && filterDesigns && filterDesigns.length) {
        const design = filterDesigns && filterDesigns[0]
        // start: remove base64 image from custom_options
        const customInfo = design.customInfo
        each(customInfo, (b, bKey) => {
          each(b, (c, cKey) => {
            if (c.base64 !== undefined) {
              c.base64 = ''
              b[cKey] = c
            }
          })
          customInfo[bKey] = b
        })
        // end: remove base64 image from custom_options
        itemInCart.pbCustomInfo = customInfo
        itemInCart.pbPrintUrl = design.printUrl
      } else if (campaign.personalized === 3 && customerCustomOptions && customerCustomOptions.length) {
        itemInCart.customer_custom_options = customerCustomOptions
      }
    }
    if (campaign && ((campaign.personalized === 0 && product && product.custom_options) || campaign.system_type === 'custom' || campaign.system_type === 'mockup') && customerCustomOptions && customerCustomOptions.length) {
      itemInCart.customer_custom_options = customerCustomOptions
    }
    itemInCart.product_url = `/${campaign.slug}/?${params}`
    commit('ADD_CART_ITEM', itemInCart)
  }
  const cQuantity = Number(quantity) || 1
  const pricePerItem = (itemInCart.variantPrice || itemInCart.price) + extraCustomFee
  const totalPrice = this.$formatPriceNoUnit(pricePerItem * cQuantity, product.currency_code, 'USD', true)
  try {
    const klaviyoData = buildKlaviyoData({ products: state.products, itemInCart })
    this.$tracking.trackEvent({
      event: 'add_to_cart',
      options: {
        content_ids: [campaign.id],
        content_name: campaign.name,
        content_category: product.name,
        content_value: [totalPrice],
        content_quantity: [cQuantity],
        content_type: 'product',
        num_items: cQuantity,
        currency: 'USD',
        value: totalPrice,
        klaviyoData: klaviyoData || {}
      },
      campaignId: campaign.id,
      campaignSlug: campaign.slug,
    })
  } catch (e) {
    // console.log(e)
  }
  lscache.set('cartData', state)
}

function addProductBundleDiscount ({ commit, state, dispatch }, { bundleDiscount, campaignBundleId, productBundleId }) {
  try {
    const productList = bundleDiscount.products.filter((item, index) => item.isSelected && index < 3)
    const indexList = productList.map((item, index) => index + state.products.length)
    const cartItemIdList = productList.map((item, index) => `${Date.now()}_${index + state.products.length}`)
    productList.forEach((product, index) => {
      dispatch('addCartItem', {
        campaign: {
          id: product.campaign_id,
          name: product.campaign_name || (product.slug && product.slug.replaceAll('-', ' ')),
          slug: product.slug,
          personalized: product.personalized,
          seller_id: product?.seller_id
        },
        product,
        options: product.currentOptions,
        full_printed: product.full_printed,
        variant: product.currentVariant,
        quantity: 1,
        isCheckQuantity: true,
        filterDesigns: product.filterDesigns,
        designThumb: product.designThumb,
        cartItemId: cartItemIdList[index],
        customerCustomOptions: product.customerCustomOptions,
        extraCustomFee: product.extraCustomFee,
        campaignBundleId,
        promotion: bundleDiscount.promotion,
        productBundleId
      })
    })
    commit('UPDATE_CART_DATA', { discount: bundleDiscount.promotion.discount_code })
    return {
      success: true,
      indexList,
      cartItemIdList
    }
  } catch (error) {
    return {
      success: false,
      message: error.message
    }
  }
}

function getRelatedCart ({ commit }, listProductIdCart) {
  const upsellApi = `${API_LIST.API_GET_UPSELL}?${qs.stringify(listProductIdCart)}`
  return this.$httpDefault(METHOD.get, upsellApi).then((result) => {
    if (result && result.success) {
      commit('UPDATE_RELATED_CART_DATA', result.data)
    } else {
      commit('UPDATE_RELATED_CART_DATA', false)
    }
    return result
  })
}

function postRelatedCart ({ commit }, listProductIdCart) {
  return this.$httpDefault(METHOD.post, API_LIST.API_GET_UPSELL, listProductIdCart).then((result) => {
    if (result && result.success) {
      commit('UPDATE_RELATED_CART_DATA', result.data)
    } else {
      commit('UPDATE_RELATED_CART_DATA', false)
    }
    return result
  })
}

function buildKlaviyoData ({ products, itemInCart }) {
  try {
    const domain = window.location.hostname || ''
    const port = window.location.port ? `:${window.location.port}` : ''
    const baseUrl = `https://${domain}${port}`
    const cartUrl = `${baseUrl}/cart`
    const addedItemData = {
      AddedItemProductName: itemInCart.campaign_title || '',
      AddedItemProductID: itemInCart.product_id || 0,
      AddedItemCategories: [itemInCart.product_name || ''],
      AddedItemURL: baseUrl + itemInCart.product_url,
      AddedItemPrice: itemInCart.price || 0,
      AddedItemQuantity: itemInCart.quantity || 0,
      ImageURL: itemInCart.thumb_url || '',
      CheckoutURL: cartUrl,
      $value: (itemInCart.price || 0) * (itemInCart.quantity || 0),
    }
    const addedItemsData = products.map((item) => {
      const itemQuantity = item.quantity || 0
      const itemPrice = item.variantPrice || 0
      const rowTotal = itemQuantity * itemPrice
      const imageUrl = item.thumb_url || ''
      return {
        ProductID: item.product_id || 0,
        ProductName: item.campaign_title || '',
        Quantity: itemQuantity,
        ItemPrice: itemPrice,
        RowTotal: rowTotal,
        ProductURL: baseUrl + item.product_url,
        ImageURL: imageUrl,
        ProductCategories: [item.product_name || '']
      }
    })
    return {
      addedItemData,
      addedItemsData,
      time: getCurrentTimeInUTC()
    }
  } catch (e) {
    // console.log(e)
    return null
  }
}

export {
  state,
  getters,
  mutations,
  actions
}
