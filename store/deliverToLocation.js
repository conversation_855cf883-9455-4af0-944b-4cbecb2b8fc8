import lscache from 'lscache'
import axios from 'axios'

const state = getDefaultState

function getDefaultState () {
  return {
    location: ''
  }
}

const getters = {
  getDeliverToLocation
}

function getDeliverToLocation (state) {
  return state.location
}

const mutations = {
  SET_LOCATION,
  RESET_DATA
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
}

function SET_LOCATION (state, loc) {
  state.location = loc
}

const actions = {
  initDeliverTo,
  ip2location,
  resetData
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

export {
  state,
  getters,
  mutations,
  actions
}

function ip2location ({ commit }, ip) {
  return new Promise((resolve, reject) => {
    try {
      axios.get(`https://ip.cloudimgs.net/json/${ip}`).then((res) => {
        resolve(res.data)
      })
    } catch (e) {
      reject(e)
    }
  })
}

async function initDeliverTo ({ dispatch, commit, rootGetters }) {
  commit('RESET_DATA')
  const visitInfo = lscache.get('visitInfo')
  const countryDisabledCheckout = rootGetters['generalInfo/getDisabledCountryCheckout']

  if (!visitInfo?.clientIp) {
    setTimeout(() => {
      dispatch('initDeliverTo')
    }, 500)
    return
  }
  if (countryDisabledCheckout.includes(visitInfo.country)) {
    return
  }

  try {
    const res = await dispatch('ip2location', visitInfo.clientIp)
    const tempLocation = (res.countryCode === 'US') ? `${res.city}, ${res.region}` : `${res.regionName}, ${res.country}`
    if (tempLocation.includes('undef')) {
      throw new Error('undef')
    }
    commit('SET_LOCATION', tempLocation)
  } catch (e) {
    const userCountry = visitInfo.country || ''
    commit('SET_LOCATION', (new Intl.DisplayNames(['en'], { type: 'region' })).of(userCountry))
  }
}
