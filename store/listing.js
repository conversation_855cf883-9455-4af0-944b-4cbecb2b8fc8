// import axios from 'axios'
import { API_LIST, METHOD } from '~/helpers/variableConst'

const state = getDefaultState

const getters = {
}

const mutations = {
  UPDATE_CAMPAIGN_BY_TYPE,
  UPDATE_CAMPAIGN_BY_URL,
  UPDATE_FILTER_BY_URL,
  UPDATE_PRODUCTS_SIMILAR,
  RESET_DATA
}

const actions = {
  resetData,
  getCampaignByType,
  getCampaignByUrl,
  getHomePageCampaigns,
  getProductsSimilar
}

function getDefaultState () {
  return {
    campaignByType: {
      best_seller: [],
      new_arrivals: [],
      featured: [],
      other: []
    },
    campaignByUrl: {},
    filterByUrl: {},
    productsSimilar: {}
  }
}

function UPDATE_CAMPAIGN_BY_TYPE (state, { type, data }) {
  state.campaignByType[type] = data || []
}

function UPDATE_CAMPAIGN_BY_URL (state, { apiQuery, data }) {
  state.campaignByUrl[apiQuery] = data
}

function UPDATE_FILTER_BY_URL (state, { apiQueryFilter, data }) {
  state.filterByUrl[apiQueryFilter] = data
}

function UPDATE_PRODUCTS_SIMILAR (state, { campaignId, data }) {
  state.filterByUrl[campaignId] = data
}

function RESET_DATA (state) {
  Object.assign(state, getDefaultState())
}

function resetData ({ commit }) {
  commit('RESET_DATA')
}

function getProductsSimilar ({ commit, state }, campaignId) {
  if (state.productsSimilar[campaignId] && state.productsSimilar[campaignId].length) {
    return state.productsSimilar[campaignId]
  }
  return this.$httpDefault(METHOD.get, `${API_LIST.API_PRODUCTS_SIMILAR}?id=${campaignId}`).then((result) => {
    commit('UPDATE_PRODUCTS_SIMILAR', { campaignId, data: result.data })
    return result.data
  })
}

function getCampaignByType ({ commit, state }, type) {
  if (state.campaignByType[type] && state.campaignByType[type].length) {
    return state.campaignByType[type]
  }
  return this.$httpDefault(METHOD.get, `${API_LIST.API_GET_PRODUCT}?type=${type}`).then((result) => {
    commit('UPDATE_CAMPAIGN_BY_TYPE', { type, data: result.data })
    return result.data
  })
}

function getCampaignByUrl ({ commit, state, rootState }, { params, query, pageType }) {
  const apiQuery = getApiQuery(params, query, pageType, rootState)
  const apiQueryFilter = getApiQueryFilter(params, query, pageType, rootState)
  if (state.campaignByUrl[apiQuery]) {
    return {
      success: true,
      apiQuery,
      apiQueryFilter
    }
  }
  return Promise.all([getProductByUrl.call(this, { commit, state }, apiQuery), getFilterByUrl.call(this, { commit, state }, apiQueryFilter)]).then((result) => {
    if (result[0] && result[1]) {
      return {
        success: true,
        apiQuery,
        apiQueryFilter
      }
    }
    return ({
      success: false,
      statusCode: 404,
      message: 'Page Not Found'
    })
  })
}

function getProductByUrl ({ commit, state }, apiQuery) {
  return new Promise((resolve, reject) => {
    if (state.campaignByUrl[apiQuery]) {
      resolve(true)
    } else {
      this.$httpDefault(METHOD.get, `${API_LIST.API_GET_PRODUCT}?${apiQuery}`)
        .then((result) => {
          if (
            !result.data.length && !result.total &&
            result.from === null && result.to === null
          ) {
            return this.$httpDefault(METHOD.get, `${API_LIST.API_GET_PRODUCT}?${apiQuery}&time=${Date.now()}`)
          }
          return result
        })
        .then((result) => {
          if (result) {
            commit('UPDATE_CAMPAIGN_BY_URL', {
              apiQuery,
              data: {
                campaigns: result.data,
                perPage: result.per_page,
                lastPage: result.last_page,
                total: result.total,
                from: result.from,
                to: result.to,
                banner_url: result.banner_url
              }
            })
            resolve(true)
          } else {
            resolve(false)
          }
        })
    }
  })
}

function getFilterByUrl ({ commit, state }, apiQueryFilter) {
  return new Promise((resolve, reject) => {
    if (state.filterByUrl[apiQueryFilter]) {
      resolve(true)
    } else {
      this.$httpDefault(METHOD.get, `${API_LIST.API_GET_FILTERS}?${apiQueryFilter}`).then((result) => {
        if (result) {
          commit('UPDATE_FILTER_BY_URL', {
            apiQueryFilter,
            data: result
          })
          resolve(true)
        } else {
          resolve(false)
        }
      })
    }
  })
}

function getHomePageCampaigns ({ commit, state }) {
  if (state.campaignByType.best_seller.length &&
    state.campaignByType.new_arrivals.length &&
    state.campaignByType.featured.length
  ) {
    return state.campaignByType
  }
  return this.$httpDefault(METHOD.get, `${API_LIST.API_HOME}`).then(({ success, data }) => {
    if (success) {
      commit('UPDATE_CAMPAIGN_BY_TYPE', { type: 'best_seller', data: data.best_seller })
      commit('UPDATE_CAMPAIGN_BY_TYPE', { type: 'new_arrivals', data: data.new_arrivals })
      commit('UPDATE_CAMPAIGN_BY_TYPE', { type: 'featured', data: data.featured })
      commit('UPDATE_CAMPAIGN_BY_TYPE', { type: 'other', data: data.other })
      return data
    } else {
      return state.campaignByType
    }
  }).catch(() => {
    return state.campaignByType
  })
}

export {
  state,
  getters,
  mutations,
  actions
}

// helpers
function getApiQuery (params, query, pageType, state) {
  const param = new URLSearchParams()

  if (query.page) {
    param.set('page', query.page)
  }

  if (query.s) {
    param.set('s', query.s)
  }

  if (params.slug) {
    if (pageType === 'collectionSlug') {
      param.set('collection_slug', params.slug)
    } else if (pageType === 'tag') {
      const keyWord = params.slug.split('-')
      if (keyWord[1]) {
        const productTemPlate = state.generalInfo.templates.find((item) => {
          return flatWord(item.name).includes(flatWord(keyWord[1]))
        })
        if (productTemPlate) {
          param.set('product', productTemPlate.id)
        }
      }
      param.set('s', keyWord[0].replace(/_/g, '') || params.slug)
    } else if (pageType === 'artist') {
      param.set('nickname', params.slug)
    }
    if (query.sub_cat) {
      param.set('category_slug', query.sub_cat)
    } else if (pageType === 'category') {
      param.set('category_slug', params.slug)
    }
  }

  if (query.category) {
    param.set('category_slug', query.category)
  }

  if (query.collection) {
    param.set('collection_slug', query.collection)
  }

  if (query.color) {
    param.set('color', query.color.replace(/-/g, ' '))
  }

  if (query.price) {
    param.set('filter_price', query.price)
  }

  if (query.product) {
    param.set('product', query.product)
  }

  if (!query.sort) {
    if (pageType === 'collectionSlug') {
      param.set('sort', 'featured')
    } else if (pageType === 'search') {
      param.set('sort', 'relevant')
    } else {
      param.set('sort', 'popular')
    }
  } else {
    param.set('sort', query.sort)
  }

  return param.toString()
}

function getApiQueryFilter (params, query, pageType, state) {
  const param = new URLSearchParams()

  if (query.s) {
    param.set('s', query.s)
  }

  if (params.slug) {
    if (pageType === 'collectionSlug') {
      param.set('collection_slug', params.slug)
    } else if (pageType === 'tag') {
      const keyWord = params.slug.split('-')
      param.set('s', keyWord[0].replace(/_/g, ' ') || params.slug)
    }
  }

  if (pageType === 'category') {
    param.set('category_slug', params.slug)
  }

  if (query.collection) {
    param.set('collection_slug', query.collection)
  }

  return param.toString()
}

function flatWord (word = '') {
  // eslint-disable-next-line no-useless-escape
  return word.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~\W]/g, '').toLowerCase()
}
