import axios from 'axios'
import lscache from 'lscache'
import { API_LIST, METHOD } from '~/helpers/variableConst'

const countryCodeExpression = /loc=([\w]{2})/
const userIPExpression = /ip=([\w.:]+)/

const state = () => ({
  theme: 'sample',
  currency: {
    code: 'USD',
    id: 1,
    locale: 'en',
    name: 'US Dollar',
    rate: 1,
    settable: 1
  },
  userCountry: '',
  currentSellerId: 0,
  viewImagePath: '',
  faqList: false,
  csrfToken: false,
  isLoadingUploadImage: false,
  isSearching: false,
  is_fallback_currency: false,
})

const mutations = {
  changeCurrency,
  changeUserCountry,
  toggle,
  changeSellerId,
  changeViewImagePath,
  updateFaqList,
  updateCSRF,
  updateLoadingUploadImage,
  updateHeaderSearchState,
  changeIsFallbackCurrency
}

const getters = {
  getHeaderSearchingState,
}

const actions = {
  nuxtServerInit,
  detectCountry,
  getCurrencyFromStorage,
  getPage,
  sendContactForm,
  sendReportForm,
  getFaqList,
  geCSRFToken,
  subscribeEmail,
  setSearchingState,
  setIsFallbackCurrency
}

function changeCurrency (state, currency) {
  if (currency) {
    state.currency = currency
    lscache.set('currency', currency)
    const visitInfo = this.$getVisitInfo()
    visitInfo.currency_code = currency.code
    lscache.set('visitInfo', visitInfo)
  }
}

function updateFaqList (state, faq) {
  if (faq) {
    state.faqList = faq
  }
}

function updateCSRF (state, csrf) {
  if (csrf) {
    state.csrfToken = csrf
  }
}

function updateLoadingUploadImage (state, value) {
  state.isLoadingUploadImage = value
}

function changeViewImagePath (state, path) {
  state.viewImagePath = path
}

function changeUserCountry (state, userCountry) {
  if (userCountry) {
    state.userCountry = userCountry
  }
}

function toggle (state, menu) {
  menu.visible = !menu.visible
}

function nuxtServerInit ({ commit }, { error }) {
  return this.$httpDefault(METHOD.get, API_LIST.API_GET_STORE_INFO).then(async (result) => {
    if (result.success) {
      const { success, data } = await this.$axios.$get('/public/is-enable-live-chat')
      result.data.storeInfo.enable_crisp_support = result.data.storeInfo.enable_crisp_support && success && Boolean(data)

      commit('storeInfo/UPDATE_DATA', result.data.storeInfo, { root: true })
      commit('generalInfo/UPDATE_DATA', result.data.generalSettings, { root: true })
      commit('changeSellerId', result.data.storeInfo.seller_id, { root: true })
    } else {
      error({
        statusCode: 404,
        message: 'Page not found'
      })
    }
    return result
  })
}

function detectCountry () {
  return new Promise((resolve, reject) => {
    axios.get('https://senprints.net/cdn-cgi/trace').then((res) => {
      const txt = res.data

      const countryCode = countryCodeExpression.exec(txt)
      const ip = userIPExpression.exec(txt)

      if (countryCode === null || countryCode[1] === '' ||
        ip === null || ip[1] === '') {
        reject(new Error('IP/Country code detection failed'))
      }

      resolve({
        countryCode: countryCode[1],
        IP: ip[1]
      })
    })
  })
}

function getCurrencyFromStorage ({ commit }) {
  const currency = lscache.get('currency')
  commit('changeCurrency', currency || {
    code: 'USD',
    id: 1,
    locale: 'en',
    name: 'US Dollar',
    rate: 1,
    settable: 1
  })
}

function getPage (context, slug) {
  return this.$httpDefault(METHOD.get, `${API_LIST.API_GET_PAGE}/${slug}`)
}

function sendContactForm (context, data) {
  return this.$httpWrite(METHOD.post, API_LIST.API_CONTACT_FORM, data)
}

function sendReportForm (context, data) {
  return this.$httpWrite(METHOD.post, API_LIST.API_REPORT_FORM, data)
}

function changeSellerId (state, sellerId) {
  state.currentSellerId = sellerId
}

function getFaqList ({ state, commit }) {
  if (state.faqList) {
    return {
      success: true,
      data: state.faqList
    }
  }

  return this.$httpDefault(METHOD.get, API_LIST.API_FAQ).then((result) => {
    if (result && result.success) {
      commit('updateFaqList', result.data)
    }
    return result
  })
}

function geCSRFToken ({ state, commit }) {
  if (state.csrfToken) {
    return {
      success: true,
      data: state.csrfToken
    }
  }

  const sessionID = lscache.get('senprints-session-id')
  return this.$httpWrite(METHOD.post, API_LIST.API_CSRF, null, {
    'x-session-id': sessionID
  }).then((result) => {
    if (result && result.success) {
      commit('updateCSRF', result.data && result.data.token)
    }
    return result
  })
}

function subscribeEmail ({ state }, email) {
  const sessionID = lscache.get('senprints-session-id')
  return this.$httpWrite(METHOD.post, API_LIST.API_GET_SUBSCRIBE, {
    email,
    token: state.csrfToken
  }, {
    'x-session-id': sessionID
  })
}

function updateHeaderSearchState (state, value) {
  state.isSearching = value
}

function changeIsFallbackCurrency (state, value) {
  state.is_fallback_currency = value
  lscache.set('is_fallback_currency', value)
}

function setSearchingState ({ commit }, value) {
  commit('updateHeaderSearchState', value)
}

function setIsFallbackCurrency ({ commit }, value) {
  commit('changeIsFallbackCurrency', value)
}

function getHeaderSearchingState (state) {
  return state.isSearching
}

export {
  getters,
  state,
  mutations,
  actions
}
