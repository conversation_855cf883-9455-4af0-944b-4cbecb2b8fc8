/*
DesignManager
- constructor( containerID ): init design canvas with display container
- loadDesign( params ): load design canvas with params
- getDesign( id ): get design canvas by id
- getActiveDesign(): get active design canvas
- setActiveDesign(id): set active design canvas by id
- onupdate( design ): callback function when a design canvs is updated
*/

/*
DesignCanvas
- constructor(params): init design canvas from params
- load(json): load design canvas from Json
- addDesign(designUrl): add a design image to design canvas
- reset(): reset design canvas, clear all design images
- getMinDPI(): min DPI of design canvas
- onupdate( design ): callback when design is updated
- exportCanvas(): export print area in a canvas to preview in mockup
- exportPNG(toPrint): export print area to PNG, true for printing, false for render mockup
- exportJson(): export design canvas to JSON
*/

/*
designParam = {
id : 'print1', //print space ID
printWidth : 2000,
printHeight : 2500,
displayWidth : 500 //display width & height of the design canvas
}
*/

import { fabric } from 'fabric'
import { capitalizeWords } from '~/helpers/string'
let opentype

export default (context, inject) => {
  class DesignManager {
    constructor (containerID) {
      this.container = document.getElementById(containerID)
      this.designs = {}
      fabric.Object.prototype.cornerColor = '#727cf5'
      this.applySameDesign = true
    }

    loadDesign (params) {
      if (this.designs[params.id]) {
        return this.designs[params.id]
      }
      const canvasEl = document.createElement('canvas')
      this.container.appendChild(canvasEl)

      params.canvasEl = canvasEl
      const design = new DesignCanvas(params)
      design.manager = this
      design.restricted = this.restricted

      if (!this.activeDesign) {
        this.activeDesign = design
      } else {
        design.hide()
      }

      this.designs[params.id] = design

      this.setActiveDesign()

      return design
    }

    getDesign (id) {
      if (this.designs[id]) { return this.designs[id] }
      return false
    }

    copyDesign (newDesign) {
      const designs = Object.values(this.designs)
      let design = designs.find(design => design.key === newDesign.key && design.hasDesign())
      let promise
      if (design) {
        promise = newDesign.load(design.exportJson())
      } else {
        design = designs.find(design => design.printSpace === newDesign.printSpace && design.hasDesign())
        if (design) {
          promise = newDesign.addDesign(design.designUrl)
        }
      }
      return promise
    }

    getActiveDesign () {
      return this.activeDesign
    }

    setActiveDesign (id) {
      if (!id) { id = this.activeDesignId }
      if (!id) { return false }

      if (this.designs[id]) {
        if (this.activeDesign !== this.designs[id]) {
          this.activeDesign.hide()
          this.activeDesign = this.designs[id]
          this.activeDesign.show()
          this.activeDesign.renderAll()
          this.activeDesign._onupdate(true)
        }
        return this.activeDesign
      }

      this.activeDesignId = id
      return false
    }

    setRestricted (restricted = true) {
      this.restricted = (restricted === true)
      const designs = Object.values(this.designs)
      designs.map((design) => {
        design.setRestricted(restricted)
      })
    }

    _applySameDesign (design) {
      if (design === this.activeDesign && this.applySameDesign) {
        const designs = Object.values(this.designs)
        designs.map((design2) => {
          if (design2 !== design && design2.key === design.key && (
            design.locked || design.locked === design2.locked)) {
            design2.load(design.exportJson()).then(() => {
            })
          }
        })
      }
    }

    _designUpdated (design) {
      if (this.onupdate) {
        this.onupdate(design)
      }
      this._applySameDesign(design)
    }

    copyObject (design, obj) {
      if (!design || !obj) { return }
      if (obj.type === 'image' && obj.isCustom) {
        this._copyCustomImage(design, obj)
      } else if (obj.type === 'i-text') {
        this._copyCustomText(design, obj)
      }
    }

    _copyCustomImage (design, image) {
      const designs = Object.values(this.designs)
      designs.map((design2) => {
        if (image.id && design2 !== design && design2.key !== design.key &&
          ((design2.printSpace === design.printSpace) || (design.isMain && design2.isMain))) {
          const image2 = design2.getObjects().find(obj => obj.id === image.id)
          if (!image2) {
            design2.addCustomImage(image._element.src).then((image2) => {
              const adjust = design.getZoom() / design2.getZoom()
              image2.set({
                id: image.id,
                left: image.left * adjust,
                top: image.top * adjust,
                scaleX: image.scaleX * adjust,
                scaleY: image.scaleY * adjust
              })
            })
          }
        }
      })
    }

    _copyCustomText (design, objText) {
      const designs = Object.values(this.designs)
      designs.map((design2) => {
        if (objText.id && design2 !== design && design2.key !== design.key &&
          ((design2.printSpace === design.printSpace) || (design.isMain && design2.isMain))) {
          let objText2 = design2.getObjects().find(obj => obj.id === objText.id)
          if (objText2) {
            design2._saveOldPosition(objText2, true)
            objText2.set({
              text: objText.text,
              name: objText.name,
              fontFamily: objText.fontFamily,
              fontUrl: objText.fontUrl,
              fill: objText.fill,
              globalCompositeOperation: objText.globalCompositeOperation,
              dirty: true,
              convertText: objText.convertText
            })
            // design2._correctTextPosition(objText2)
          } else {
            // objText2 = fabric.util.object.clone(objText)
            const adjust = design.getZoom() / design2.getZoom()
            objText2 = new fabric.IText(objText.text, {
              id: objText.id,
              name: objText.name,
              fontSize: objText.fontSize,
              lockUniScaling: false,
              textWidth: objText.textWidth,
              textAlign: objText.textAlign,
              lineHeight: objText.lineHeight,
              editable: false,
              maxLength: objText.maxLength,
              fontFamily: objText.fontFamily,
              fontUrl: objText.fontUrl,
              fill: objText.fill,
              width: objText.width,
              height: objText.height,
              left: objText.left * adjust,
              top: objText.top * adjust,
              scaleX: objText.scaleX * adjust,
              scaleY: objText.scaleY * adjust,
              angle: objText.angle,
              globalCompositeOperation: objText.globalCompositeOperation,
              isCustom: true,
              convertText: objText.convertText
            })
            design2.add(objText2)
            design2.texts.push(objText2)
            design2._resetTextPosition(objText2)
          }
          // design2.setActiveObject(objText2)
          design2.discardActiveObject()
          design2._onupdate(true)
          // design2.renderAll()
        }
      })
    }

    _removeObject (design, obj) {
      const designs = Object.values(this.designs)
      designs.map((design2) => {
        if (obj.id && design2 !== design && design2.key !== design.key &&
          ((design2.printSpace === design.printSpace) || (design.isMain && design2.isMain))) {
          const obj2 = design2.getObjects().find(obj2 => obj2.id === obj.id)
          if (obj2) {
            const systemUpdate = true
            design2.remove(obj2, systemUpdate)
            design2._onupdate(true)
          }
        }
      })
    }
  }

  class DesignCanvas extends fabric.Canvas {
    // eslint-disable-next-line constructor-super
    constructor (params) {
      if (!params) {
        super()
      } else if (params.canvasEl) {
        super(params.canvasEl)
        this.canvasEl = params.canvasEl
      } else {
        super(params.id)
      }

      this.designs = []
      this.texts = []
      this.onUpdates = []
      this.restricted = true
      this.textLimit = 2
      this.DPI = params?.DPI || 300
      this.minDPI = params?.minDPI || 150
      this.unique = params?.unique || false

      if (!params) { return }

      this.printSpace = params.name
      this.key = params.name + '-' + params.printWidth + '-' + params.printHeight
      this.isMain = params.isMain

      const _this = this
      this.bgColor = ''

      this.printWidth = params.printWidth
      this.printHeight = params.printHeight
      const boundWidth = Math.max(this.printWidth, this.printHeight) * 1.4
      const zoom = params.displayWidth / boundWidth

      this.setWidth(params.displayWidth)
      this.setHeight(params.displayWidth)
      this.displayWidth = params.displayWidth
      // canvas.setBackgroundColor('#000000');
      this.setZoom(zoom)
      // this.canvasEl._thisElement.style.background = '#000000';
      this.selection = false
      this.on('object:moving', function (options) {
        if (options.target) {
          _this._correctPosition(options.target)
        }
      })
      this.on('object:scaling', function (options) {
        if (options.target) {
          _this._correctScale(options.target)
        }
      })
      this.on('mouse:up', function (options) {
        if (options.target) {
          _this._correctPosition(options.target)
        }
        if (_this.hasDesign()) {
          _this._onupdate()
        }
      })

      this.printRect = new fabric.Rect({
        name: 'printRect',
        width: params.printWidth,
        height: params.printHeight,
        fill: '',
        hasControls: false,
        selectable: false,
        hoverCursor: 'default',
        top: (boundWidth - params.printHeight) / 2,
        left: (boundWidth - params.printWidth) / 2
      })

      this.add(this.printRect)
      this._grayoutNonPrintArea()

      // add print space border
      this.strokeWidth = 2 / zoom
      this.strokeColor = '#4e5bf2'
      this.printRect.clone(function (rect) {
        rect.set({
          name: 'printStroke',
          stroke: _this.strokeColor,
          strokeWidth: _this.strokeWidth,
          top: rect.top - _this.strokeWidth,
          left: rect.left - _this.strokeWidth,
          width: rect.width,
          height: rect.height,
          hasControls: false,
          selectable: false,
          hoverCursor: 'default'
        })
        _this.add(rect)
        _this.printStroke = rect
      })

      // init view border
      this.viewStrokeColor = '#00FF00'
      if (params.viewWidth && params.viewHeight) {
        this.viewRect = new fabric.Rect({
          name: 'viewRect',
          stroke: this.viewStrokeColor,
          strokeWidth: this.strokeWidth,
          strokeDashArray: [100, 50],
          width: params.viewWidth,
          height: params.viewHeight,
          fill: '',
          hasControls: false,
          selectable: false,
          hoverCursor: 'default',
          top: (boundWidth - params.viewHeight) / 2 - this.strokeWidth,
          left: (boundWidth - params.viewWidth) / 2 - this.strokeWidth
        })

        this.add(this.viewRect)
      }

      this.renderAll()
      initCenteringGuidelines(this)
    }

    load (json, displayWidth = 0, correctData = false) {
      if (json) {
        if (typeof json === 'string') {
          this.json = JSON.parse(json)
        } else {
          this.json = json
        }
      }
      const _this = this

      return new Promise((resolve, reject) => {
        if (!_this.json || _this.loadingJson) {
          return resolve(false)
        }
        _this.loadingJson = true
        _this.loadFrontPromises = []
        if (typeof document !== 'undefined') { // load font client side
          _this.json.objects.map((obj) => {
            if (obj.type === 'i-text') {
              let fontUrl = obj.fontUrl
              if (!fontUrl && obj.fontFamily === 'Roboto') {
                fontUrl = '/fonts/Roboto.ttf'
              }
              if (fontUrl) {
                _this.loadFrontPromises.push(_this.loadFont(obj.fontFamily, fontUrl))
              }
            }
          })
        }

        const duplicateObjects = []
        Promise.all(_this.loadFrontPromises).then(() => {
          _this.clear()
          _this.designs = []
          _this.texts = []
          _this.designUrl = null
          _this.printRect = null
          fabric.util.clearFabricFontCache()
          const backgroundColor = _this.json.background
          _this.json.background = null
          _this.loadFromJSON(_this.json, function () {
            if (duplicateObjects.length > 0) {
              duplicateObjects.map((obj) => {
                _this.remove(obj)
              })
            }
            const loadFontOpentypes = []
            if (typeof document === 'undefined') { // load font server side
              _this.getObjects().map((object) => {
                if (object.type === 'i-text' && object.fontFile) { // opentype.load is async returning a promise
                  loadFontOpentypes.push(new Promise((resolve, reject) => {
                    opentype.load(object.fontFile)
                      .then((font) => {
                        // eslint-disable-next-line no-console
                        console.log('load font', object.fontFamily, object.fontFile)
                        const path = font.getPath(object.text, 0, 0, object.fontSize)
                        const fabricPath = new fabric.Path(path.toPathData(20), {
                          top: object.top,
                          left: object.left,
                          width: object.width,
                          height: object.height,
                          scaleX: object.scaleX,
                          scaleY: object.scaleY,
                          angle: object.angle,
                          fill: object.fill,
                          globalCompositeOperation: object.globalCompositeOperation
                        })
                        if (object.width > fabricPath.width) {
                          fabricPath.left += (object.width - fabricPath.width) * fabricPath.scaleX / 2
                        }
                        if (object.height > fabricPath.height) {
                          fabricPath.top += (object.height - fabricPath.height) * fabricPath.scaleY / 2
                        }
                        _this.add(fabricPath)
                        _this.remove(object)
                        resolve(fabricPath)
                      })
                      // eslint-disable-next-line no-console
                      .catch(err => console.log('Error fetching font:', err))
                  }))
                }
              })
            }
            Promise.all(loadFontOpentypes).then(() => {
              _this.setBackgroundColor(backgroundColor)
              _this.renderAll.bind(_this)
              _this._onupdate(true)
              resolve(_this)
              _this.loadingJson = false
            })
          },
          function (o, object) {
            if (object.name === 'printRect' && !_this.printRect) {
              // correct canvas size
              let width = object.width + object.left * 2
              let height = object.height + object.top * 2
              if (_this.displayWidth) {
                _this.setZoom(_this.displayWidth / width)
              } else if (displayWidth) {
                const zoom = displayWidth / object.width
                width = width * zoom
                height = height * zoom
                _this.setZoom(zoom)
                _this.setWidth(width)
                _this.setHeight(height)
              }

              _this.printRect = object
              _this.printWidth = object.width
              _this.printHeight = object.height
              _this.printRect.hasControls = false
              _this.printRect.selectable = false
            } else if (object.name === 'printStroke') {
              _this.printStroke = object
              _this.printStroke.hasControls = false
              _this.printStroke.selectable = false
            } else if (object.name === 'viewRect') {
              _this.viewRect = object
              _this.viewRect.hasControls = false
              _this.viewRect.selectable = false
            } else if (object.type === 'image') {
              // remove duplicate image
              if (_this.designUrl === object.src) {
                return duplicateObjects.push(object)
              }

              const img = object.getElement()
              const width = object.width * object.scaleX
              const height = object.height * object.scaleY

              object.set({
                width: img.naturalWidth,
                height: img.naturalHeight,
                scaleX: width / img.naturalWidth,
                scaleY: height / img.naturalHeight
              })

              if (correctData) {
                let adjustedScale = object.adjustedScale || 1
                const scale = object.scaleX

                if (scale * adjustedScale > _this.DPI / _this.minDPI) {
                  adjustedScale = 1 / scale
                }
                object.adjustedScale = adjustedScale
              }

              _this.designs.push(object)
              _this.designUrl = object.src
            } else if (object.type === 'i-text') {
              _this.texts.push(object)
            }
          })
        })
      })
    }

    setDisplaySize (displayWidth) {
      const printRect = this.printRect
      if (!printRect) { return }
      const width = printRect.width + printRect.left * 2
      this.setZoom(displayWidth / width)
      this.setWidth(displayWidth)
      this.setHeight(displayWidth)
    }

    lockEdit () {
      this.isLockEdit = true
      this.selection = false
      this.defaultCursor = 'default'
      this.strokeColor = '#4e5bf2'
      this.printStroke.set({ stroke: this.strokeColor })
      this.getObjects().map((obj) => {
        if (!(obj.type === 'image' && obj.isCustom)) {
          obj.set('selectable', false)
          obj.defaultCursor = 'default'
        }
      })
      this.on('mouse:over', function (event) {
        if (event.target != null && !(event.target.type === 'image' && event.target.isCustom)) {
          event.target.hoverCursor = 'default'
        }
      })
      this._grayoutNonPrintArea()
    }

    setRestricted (restricted = true) {
      this.restricted = restricted
      if (this.hasDesign()) {
        this.designs.forEach(function (design) {
          design.lockRotation = restricted
        })
      }
    }

    cloneActiveObject () {
      const activeObject = this.getActiveObject()
      if (activeObject) {
        const clonedObj = fabric.util.object.clone(activeObject)
        this.add(clonedObj)
        this.setActiveObject(clonedObj)
      }
    }

    addDesign (designUrl, adjustedScale = 1) {
      const _this = this
      return new Promise((resolve, reject) => {
        if (_this.loadingJson) { return resolve(false) }
        _this.designUrl = designUrl
        // check and ignore duplicate
        if (this.hasDesign()) {
          this.designs.forEach(function (design) {
            if (design._element.src === designUrl) {
              return resolve(design)
            }
          })
        }

        // load new image
        this._loadImg(designUrl).then(function (design) {
          design.adjustedScale = adjustedScale
          _this._addDesign(design)
          _this.designs.push(design)
          _this._onupdate()
          resolve(design)
        })
      })
    }

    addCustomImage (imageUrl) {
      const _this = this
      return new Promise((resolve, reject) => {
        // load new image
        this._loadImg(imageUrl).then(function (image) {
          image.set({
            id: 'image_' + Date.now(),
            globalCompositeOperation: 'destination-over',
            isCustom: true
          })
          _this._fitImageToRect(image, _this.printRect)

          // remove old custom image
          const currentCustomImage = _this.getCustomImage()
          if (currentCustomImage) {
            _this.remove(currentCustomImage)
          }

          _this.add(image)
          _this.bringToFront(image)
          _this.setActiveObject(image)
          _this._onupdate()
          resolve(image)
        })
      })
    }

    changeCustomImage (imageUrl) {
      const _this = this
      return new Promise((resolve, reject) => {
        // load new image
        this._loadImg(imageUrl).then(function (image) {
          image.set({
            id: 'image_' + Date.now(),
            globalCompositeOperation: 'destination-over',
            isCustom: true
          })

          _this.oldImage = _this.oldImage || _this.getCustomImage()
          const oldImage = _this.oldImage
          if (oldImage) {
            let scale = oldImage.width * oldImage.scaleX / image.width
            if (image.height * scale < oldImage.height * oldImage.scaleY) {
              scale = oldImage.height * oldImage.scaleY / image.height
            }
            const alpha = oldImage.angle * Math.PI / 180
            const d = (oldImage.width * oldImage.scaleX - image.width * scale) / 2
            let top = oldImage.top + (d * Math.sin(alpha))
            let left = oldImage.left + (d * Math.cos(alpha))
            const d2 = (oldImage.height * oldImage.scaleY - image.height * scale) / 2
            if (Math.abs(d2) > Math.abs(d)) {
              top = oldImage.top + (d2 * Math.cos(alpha))
              left = oldImage.left - (d2 * Math.sin(alpha))
            }

            const clipPath = new fabric.Rect({
              width: oldImage.width * oldImage.scaleX,
              height: oldImage.height * oldImage.scaleY,
              angle: oldImage.angle,
              top: oldImage.top,
              left: oldImage.left,
              absolutePositioned: true
            })

            image.set({
              scaleX: scale,
              scaleY: scale,
              top,
              left,
              angle: oldImage.angle,
              oldImage
            })
            image.clipPath = clipPath
          }

          const currentImage = _this.getCustomImage()
          if (currentImage) {
            _this.remove(currentImage)
          }

          _this.add(image)
          _this.bringToFront(image)
          _this.setActiveObject(image)
          _this._onupdate()
          resolve(image)
        })
      })
    }

    getCustomImage () {
      return this.getObjects().find(obj => obj.type === 'image' && obj.isCustom)
    }

    getRequiredSize (image) {
      return {
        width: parseInt(image.width * image.scaleX),
        height: parseInt(image.height * image.scaleY)
      }
    }

    hasCustomText () {
      return this.texts.length > 0
    }

    addText (message = 'Hello', name, fontFamily = 'Roboto', fontUrl = null) {
      if (this.texts.length >= this.textLimit || this.loadingJson) { return }
      if (!name) {
        name = 'text_' + this.getObjects().length
      }
      const newText = new fabric.IText(message, {
        id: 'text_' + Date.now(),
        name,
        fontSize: 100,
        lockUniScaling: false,
        lineHeight: 1,
        textWidth: 'limited',
        textAlign: 'center',
        editable: false,
        maxLength: 16,
        fontFamily,
        isCustom: true
      })
      if (fontUrl) { newText.set({ fontUrl }) }

      // scale and set font size
      newText.scaleX = this.printRect.width * 0.8 / newText.width
      newText.scaleY = newText.scaleX
      newText.left = this.width / this.getZoom() / 2 - newText.width * newText.scaleX / 2
      newText.top = this.height / this.getZoom() / 2 - newText.height * newText.scaleY / 2

      this.add(newText)
      this.setActiveObject(newText)
      this.texts.push(newText)
      this.renderAll()
      this._onupdate()
      // if (this.manager) {
      //   this.manager._copyCustomText(this, newText)
      // }
      return newText
    }

    _resetTextPosition (objText) {
      // objText.angle = 0
      if (objText.width * objText.scaleX > this.printRect.width) {
        objText.scaleX = this.printRect.width / objText.width
        objText.scaleY = objText.scaleX
      }
      this._correctPosition(objText)
    }

    async loadFont (fontFamily, fontUrl) {
      if (!fontFamily || !fontUrl || typeof document === 'undefined') { return false }
      const font = new FontFace(fontFamily, 'url(' + fontUrl + ')')
      try {
        // wait for font to be loaded
        await font.load()
        // add font to document
        document.fonts.add(font)
      } catch (e) {
        // eslint-disable-next-line no-console
        console.log(e)
      }
    }

    canAddText () {
      return this.texts.length < this.textLimit
    }

    updateText (obj, message) {
      if (!obj || obj.type !== 'i-text') { return }
      this._saveOldPosition(obj)
      obj.text = this.convertText(obj, message)
      this._correctTextPosition(obj)
      this.renderAll()
      this._onupdate()
      // if (this.manager) {
      //   this.manager._copyCustomText(this, obj)
      // }
    }

    convertText (obj, message) {
      if (!obj || obj.type !== 'i-text' || !message) { return '' }
      let convertedText = message
      if (obj.convertText) {
        switch (obj.convertText) {
          case 'capitalize':
            convertedText = capitalizeWords(message)
            break
          case 'uppercase':
            convertedText = message.toUpperCase()
            break
          case 'lowercase':
            convertedText = message.toLowerCase()
            break
          default:
            break
        }
      }
      return convertedText
    }

    updateTextName (obj, name) {
      if (!obj || obj.type !== 'i-text') { return }
      obj.set('name', name)
      // if (this.manager) {
      //   this.manager._copyCustomText(this, obj)
      // }
    }

    _saveOldPosition (obj, reset = false) {
      if (reset) {
        obj.oldWidth = null
        obj.oldScaleX = null
        obj.oldLeft = null
        obj.oldTop = null
        obj.centerX = null
      }
      const scaledWidth = obj.width * obj.scaleX
      obj.oldWidth = obj.oldWidth || scaledWidth
      obj.oldScaleX = obj.oldScaleX || obj.scaleX
      obj.oldLeft = obj.oldLeft || obj.left
      obj.oldTop = obj.oldTop || obj.top
      obj.centerX = obj.centerX || (obj.left + scaledWidth / 2)
    }

    _correctTextPosition (obj) {
      this.renderAll()

      let textWidth = this.isLockEdit ? obj.textWidth : 'flexible'
      if (!textWidth && obj.fixWidth) {
        textWidth = 'limited'
      }

      if (textWidth === 'fixed') {
        obj.scaleX = obj.oldWidth / obj.width
      } else if (textWidth === 'limited') {
        if (obj.width * obj.oldScaleX > obj.oldWidth) {
          obj.scaleX = obj.oldWidth / obj.width
        } else {
          obj.scaleX = obj.oldScaleX
        }
      }

      if (obj.width * obj.scaleX > this.printWidth) {
        obj.scaleX = this.printWidth / obj.width
      }

      if (obj.angle === 0) {
        if (obj.textAlign === 'center') {
          obj.left = obj.centerX - obj.width * obj.scaleX / 2
        } else if (obj.textAlign === 'right') {
          obj.left = obj.oldLeft + obj.oldWidth - (obj.width * obj.scaleX)
        }
      } else {
        let d = 0
        if (obj.textAlign === 'center') {
          d = (obj.oldWidth - obj.width * obj.scaleX) / 2
        } else if (obj.textAlign === 'right') {
          d = (obj.oldWidth - obj.width * obj.scaleX)
        }
        const alpha = obj.angle * Math.PI / 180
        obj.left = obj.oldLeft + (d * Math.cos(alpha))
        obj.top = obj.oldTop + (d * Math.sin(alpha))
      }
    }

    updateFont (obj, fontFamily, fontUrl = null) {
      if (!obj || obj.type !== 'i-text') { return }
      this._saveOldPosition(obj)
      obj.set('fontFamily', fontFamily)
      obj.set('fontUrl', fontUrl)
      fabric.util.clearFabricFontCache()
      this._correctTextPosition(obj)
      this.renderAll()
      this._onupdate()
      // if (this.manager) {
      //   this.manager._copyCustomText(this, obj)
      // }
    }

    setColor (obj, color) {
      if (!obj || obj.type !== 'i-text') { return }
      obj.fill = color
      this.renderAll()
      this._onupdate()
    }

    getObjectByName (name) {
      return this.getObjects().find(obj => obj.name === name)
    }

    getAllObjectsByName (name) {
      return this.getObjects().filter(obj => obj.name === name)
    }

    changeDesignByCharacter (character) {
      if (character && typeof character === 'string') {
        character = character.toLowerCase()
        if (this.designUrl) {
          return this.designUrl.includes('custom') ? this.replaceDesign(this.designUrl.replace(/\w+(.\w+$)/g, `${character}$1`)) : this.replaceDesign(this.designUrl.replace(/\w+(.\w+$)/g, `custom/${character}$1`))
        }
      }
    }

    replaceDesign (designUrl, adjustedScale = 1) {
      const _this = this
      return new Promise((resolve, reject) => {
        if (_this.designs.length === 0 || _this.loadingJson) { return resolve(false) }
        _this.designUrl = designUrl
        const lastDesign = _this.designs[_this.designs.length - 1]

        if (lastDesign._element.src === designUrl) {
          return resolve(lastDesign)
        }

        fabric.Image.fromURL(designUrl, function (newImg) {
          const img = lastDesign._element
          // replace image with the same dimension
          const adjustedScale = newImg.width / img.width
          if (Math.abs(img.width / img.height - newImg.width / newImg.height) < 0.005) {
            img.crossOrigin = 'Anonymous'
            img.src = designUrl
            img.onload = () => {
              const width = lastDesign.width * lastDesign.scaleX
              const height = lastDesign.height * lastDesign.scaleY
              lastDesign.set({
                width: img.naturalWidth,
                height: img.naturalHeight,
                scaleX: width / img.naturalWidth,
                scaleY: height / img.naturalHeight,
                adjustedScale
              })
              _this.renderAll()
              _this._onupdate()
              resolve(lastDesign)
            }
          } else {
            // newImg.adjustedScale = adjustedScale
            _this._addDesign(newImg)
            _this._copyDesignConfig(newImg, lastDesign)
            _this.remove(lastDesign)
            _this.designs.splice(_this.designs.length - 1, 1)
            _this.designs.push(newImg)
            _this._onupdate()
            resolve(newImg)
          }
        }, { crossOrigin: 'Anonymous' })
      })
    }

    _copyDesignConfig (newImg, img) {
      const width = Math.max(this.printRect.width, img.getScaledWidth())
      const height = Math.max(this.printRect.height, img.getScaledHeight())

      let scale = width / newImg.width
      if (newImg.height * scale > height) {
        scale = height / newImg.height
      }

      newImg.scaleX = newImg.scaleY = scale

      const minTop = Math.min(img.top, this.printRect.top)
      newImg.top = Math.max(img.top + (img.getScaledHeight() - newImg.getScaledHeight()) / 2, minTop)
      newImg.left = img.left + (img.getScaledWidth() - newImg.getScaledWidth()) / 2
    }

    _addDesign (design) {
      if (this.restricted) {
        design.lockRotation = true
      }
      // design.set({
      //   globalCompositeOperation: 'destination-over'
      // })
      this._fitImageToRect(design, this.printRect)
      this.add(design)
      this.setActiveObject(design)

      // move texts to front
      const objs = this.getObjects()
      objs.map((obj) => {
        if (obj.type === 'i-text' || (obj.type === 'image' && obj.isCustom)) {
          this.bringToFront(obj)
        }
      })

      this.renderAll()
    }

    setBackgroundColor (color) {
      super.setBackgroundColor(color)
      this.bgColor = color
      this.renderAll()
    }

    reset () {
      // const _this = this
      const objs = this.getObjects()
      objs.map((obj) => {
        if (obj.type === 'image' || obj.type === 'i-text') {
          this.remove(obj)
        }
      })
      this.designs = []
      this.texts = []
      this.designUrl = null
      this.renderAll()
      this._onupdate()
    }

    removeDesign () {
      const _this = this
      this.designs.map((design) => {
        _this.remove(design)
      })
      this.designs = []
      this.designUrl = null
      // this.texts.map((text) => {
      //   _this.remove(text)
      // })
      // this.texts = []
      this.renderAll()
      this._onupdate()
    }

    remove (obj, systemUpdate = false) {
      if (!obj) { return }
      super.remove(obj)
      if (obj.type === 'image') {
        const index = this.designs.indexOf(obj)
        if (index > -1) {
          this.designs.splice(index, 1)
        }
      }
      if (obj.type === 'i-text') {
        const index = this.texts.indexOf(obj)
        if (index > -1) {
          this.texts.splice(index, 1)
        }
      }
      if (obj.type === 'i-text' || obj.isCustom) {
        if (this.manager && !systemUpdate) {
          this.manager._removeObject(this, obj)
        }
      }
      this._onupdate()
    }

    removeActiveObject () {
      const obj = this.getActiveObject()
      this.remove(obj)
    }

    hasDesign () {
      return this.designs.length > 0 || this.texts.length > 0
    }

    hasArtwork () {
      return this.designs.length > 0
    }

    _correctPosition (obj) {
      const pr = this.printRect
      if (pr && obj !== pr && this.restricted && obj.angle === 0) {
        const width = obj.getScaledWidth()
        const height = obj.getScaledHeight()
        if (obj.top + height > pr.top + pr.height) {
          obj.top = pr.top + pr.height - height
        }
        if (obj.top < pr.top) {
          obj.top = pr.top
        }
        if (obj.left + width > pr.left + pr.width) {
          obj.left = pr.left + pr.width - width
        }
        if (obj.left < pr.left) {
          obj.left = pr.left
        }
      }
      if (obj.type === 'i-text') {
        this._saveOldPosition(obj, true)
      }
    }

    _correctScale (obj) {
      const rect = this.printRect
      if (!this.restricted || !rect) { return }
      const adjustedScale = obj.adjustedScale || 1
      let scale = obj.scaleX
      if (obj.width * scale > rect.width) {
        scale = rect.width / obj.width
        if (obj.type === 'i-text') {
          obj.scaleX = scale
        }
      }
      if (obj.height * scale > rect.height) {
        scale = rect.height / obj.height
        if (obj.type === 'i-text') {
          obj.scaleY = scale
        }
      }
      if (obj.type === 'image' && scale * adjustedScale > this.DPI / this.minDPI) {
        scale = this.DPI / this.minDPI / adjustedScale
      }
      if (obj.type === 'image') {
        obj.scaleX = obj.scaleY = scale
      }
      if (obj.type === 'i-text') {
        this._saveOldPosition(obj, true)
      }
    }

    align (val, obj) {
      const canvas = this
      if (!obj) {
        obj = canvas.getActiveObject()
      }

      if (!obj) {
        return
      }

      const printRect = this.printRect

      let left, top

      if (!obj) { return }

      switch (val) {
        case 'left':
          if (obj.angle <= 90) {
            left = obj.aCoords.tl.x - obj.aCoords.bl.x
          }
          if (obj.angle > 90 && obj.angle <= 180) {
            left = obj.aCoords.tl.x - obj.aCoords.br.x
          }
          if (obj.angle > 180 && obj.angle <= 270) {
            left = obj.aCoords.tl.x - obj.aCoords.tr.x
          }
          if (obj.angle > 270) {
            left = 0
          }
          obj.set({
            left: printRect.left + left
          })
          break
        case 'right':
          if (obj.angle <= 90) {
            left = obj.aCoords.tl.x - obj.aCoords.tr.x
          }
          if (obj.angle > 90 && obj.angle <= 180) {
            left = 0
          }
          if (obj.angle > 180 && obj.angle <= 270) {
            left = obj.aCoords.tl.x - obj.aCoords.bl.x
          }
          if (obj.angle > 270) {
            left = obj.aCoords.tl.x - obj.aCoords.br.x
          }
          obj.set({
            left: printRect.left + printRect.width + left
          })
          break
        case 'top':
          if (obj.angle <= 90) {
            top = 0
          }
          if (obj.angle > 90 && obj.angle <= 180) {
            top = obj.aCoords.tl.y - obj.aCoords.bl.y
          }
          if (obj.angle > 180 && obj.angle <= 270) {
            top = obj.aCoords.tl.y - obj.aCoords.br.y
          }
          if (obj.angle > 270) {
            top = obj.aCoords.tl.y - obj.aCoords.tr.y
          }
          obj.set({
            top: printRect.top + top
          })
          break
        case 'bottom':
          if (obj.angle <= 90) {
            top = obj.aCoords.tl.y - obj.aCoords.br.y
          }
          if (obj.angle > 90 && obj.angle <= 180) {
            top = obj.aCoords.tl.y - obj.aCoords.tr.y
          }
          if (obj.angle > 180 && obj.angle <= 270) {
            top = 0
          }
          if (obj.angle > 270) {
            top = obj.aCoords.tl.y - obj.aCoords.bl.y
          }
          obj.set({
            top: printRect.top + printRect.height + top
          })
          break
        case 'center':
          obj.viewportCenterH()
          break
        case 'vcenter':
          obj.viewportCenterV()
          break
        case 'reset':
          this._fitImageToRect(obj, printRect)
          break
      }
      canvas.renderAll()
      this._onupdate()
    }

    getMinDPI () {
      const designs = this.designs
      let minDPI = this.DPI

      let index
      for (index = 0; index < designs.length; ++index) {
        const adjustedScale = designs[index].adjustedScale || 1
        const scale = designs[index].scaleX * adjustedScale
        const dpi = parseInt(this.DPI / scale)
        if (dpi < minDPI) { minDPI = dpi }
      }

      return minDPI
    }

    isGoodDPI () {
      return this.getMinDPI() >= (this.DPI + this.minDPI) / 2
    }

    isNormalDPI () {
      const dpi = this.getMinDPI()
      return dpi >= this.minDPI && dpi < (this.DPI + this.minDPI) / 2
    }

    isLowDPI () {
      return this.getMinDPI() < this.minDPI
    }

    checkDPI () {
      return this.isGoodDPI() ? 'good' : (this.isLowDPI() ? 'low' : 'normal')
    }

    _loadImg (fileUrl) {
      if (!fileUrl || typeof fabric === 'undefined') { return }
      return new Promise((resolve, reject) => {
        fabric.Image.fromURL(fileUrl,
          function (img) {
            resolve(img)
          }, { crossOrigin: 'Anonymous' })
      })
    }

    exportCanvas (printSize = 1500) {
      // fix design quality for mug
      if (this.printWidth / this.printHeight > 1.5) {
        printSize = printSize * 1.5
      }
      const oldzoom = this.getZoom()
      const oldSize = this.getWidth()
      const boundWidth = Math.max(this.printWidth, this.printHeight) * 1.4
      if (printSize === 0) { printSize = boundWidth }
      const zoom = printSize / boundWidth
      this.setWidth(printSize)
      this.setHeight(printSize)
      this.setZoom(zoom)

      const width = this.printWidth * zoom
      const height = this.printHeight * zoom

      if (!this.printCanvas) {
        if (typeof document === 'undefined') {
          this.printCanvas = new fabric.StaticCanvas(null, { width, height })
        } else {
          this.printCanvas = document.createElement('canvas')
        }
      }
      this.printCanvas.width = width
      this.printCanvas.height = height

      const ctx = this.printCanvas.getContext('2d')

      const printRect = this.printRect
      const left = printRect.left * zoom
      const top = printRect.top * zoom

      this.printStroke.set({ stroke: '' })
      this.viewRect?.set({ stroke: '' })
      this.setBackgroundColor(null)
      ctx.clearRect(0, 0, width, height)
      ctx.drawImage(this.toCanvasElement(), left, top, width, height, 0, 0, width, height)
      this.printStroke.set({ stroke: this.strokeColor })
      this.viewRect?.set({ stroke: this.viewStrokeColor })
      this.setBackgroundColor(this.bgColor)
      this.setZoom(oldzoom)
      this.setWidth(oldSize)
      this.setHeight(oldSize)

      return this.printCanvas
    }

    exportPrint () {
      const printRect = this.printRect
      const left = printRect.left
      const top = printRect.top
      const objects = this.getObjects()
      const unprints = ['printRect', 'printStroke', 'viewRect']
      objects.map((obj) => {
        if (unprints.includes(obj.name)) {
          this.remove(obj)
        } else {
          obj.top = obj.top - top
          obj.left = obj.left - left
        }
        if (obj.type === 'image' && !obj.isCustom) {
          obj.set({
            globalCompositeOperation: 'source-over'
          })
        }
      })
      // this.setBackgroundColor(null)
      this.setZoom(1)
      this.setWidth(printRect.width)
      this.setHeight(printRect.height)
      this.renderAll()
      return this
    }

    exportPNG (toPrint = true) {
      const zoom = this.getZoom()
      let multiplier = 1 / zoom
      if (!toPrint) {
        multiplier = 1920 / Math.max(this.printWidth, this.printHeight) / zoom
      }

      const printRect = this.printRect
      this.printStroke.set({ stroke: '' })
      this.viewRect?.set({ stroke: '' })
      this.setBackgroundColor(null)
      const dataURL = this.toDataURL({
        format: 'png',
        multiplier,
        left: printRect.left * zoom,
        top: printRect.top * zoom,
        width: printRect.width * zoom,
        height: printRect.height * zoom
      })
      this.printStroke.set({ stroke: this.strokeColor })
      this.viewRect?.set({ stroke: this.viewStrokeColor })
      this.setBackgroundColor(this.bgColor)
      return dataURL
    }

    exportJson () {
      return this.toJSON(['id', 'name', 'fontUrl', 'DPI', 'adjustedScale', 'maxLength', 'textWidth', 'textAlign', 'isCustom'])
    }

    backup (json) {
      if (json) {
        this.json = json
      } else {
        this.json = this.toJSON(['name'])
      }
    }

    show () {
      if (this.canvasEl) {
        this.canvasEl.parentElement.style.display = 'block'
      }
    }

    hide () {
      if (this.canvasEl) {
        this.canvasEl.parentElement.style.display = 'none'
      }
    }

    _fitImageToRect (img, rect, fit = true) {
      const adjustedScale = img.adjustedScale || 1
      let scale = rect.width / img.width
      if (img.height * scale > rect.height) {
        scale = rect.height / img.height
      }
      if (scale * adjustedScale > this.DPI / this.minDPI) {
        scale = this.DPI / this.minDPI / adjustedScale
      }
      img.scaleX = img.scaleY = scale
      img.angle = 0
      img.top = rect.top + (rect.height - img.getScaledHeight()) / 2
      img.left = rect.left + (rect.width - img.getScaledWidth()) / 2
    }

    _grayoutNonPrintArea () {
      const _this = this
      // grayout non-print area
      this.on('after:render', function () {
        const ctx = _this.contextContainer
        const printRect = _this.printRect
        if (printRect) {
          // set the fill color of the overlay
          ctx.fillStyle = _this._hexToRgbA(this.bgColor)
          const bound = printRect.getBoundingRect()
          const left = bound.left - 2
          const top = bound.top - 2
          const width = bound.width + 2
          const height = bound.height + 2

          ctx.beginPath()
          // draw rectangle to the left of the selection
          ctx.rect(0, 0, left, _this.height)
          // draw rectangle to the right of the selection
          ctx.rect(left + width, 0, _this.width - left - width, _this.height)
          // draw rectangle above the selection
          ctx.rect(left, 0, width, top)
          // draw rectangle below the selection
          ctx.rect(left, top + height, width, _this.height - top - height)
          ctx.fill()
        }
      })
    }

    _hexToRgbA (hex, opacity = '0.5') {
      let c
      if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
        c = hex.substring(1).split('')
        if (c.length === 3) {
          c = [c[0], c[0], c[1], c[1], c[2], c[2]]
        }
        c = '0x' + c.join('')
        return 'rgba(' + [(c >> 16) & 255, (c >> 8) & 255, c & 255].join(',') + ',' + opacity + ')'
      }
      return 'rgba(255, 255, 255, ' + opacity + ')'
    }

    addUpdateCallback (callback) {
      if (typeof callback === 'function') {
        this.onUpdates.push(callback)
      }
    }

    _onupdate (system = false) {
      const design = this
      if (this.onUpdates.length) {
        this.onUpdates.forEach(function (callback) {
          callback(design)
        })
      }
      if (this.onupdate) {
        this.onupdate(this)
      }
      if (this.manager && !system) {
        this.manager._designUpdated(this)
      }
    }
  }

  inject('DesignManager', DesignManager)
  inject('DesignCanvas', DesignCanvas)
}

function initCenteringGuidelines (canvas) {
  const zoom = canvas.getZoom()
  const canvasWidth = canvas.getWidth() / zoom
  const canvasHeight = canvas.getHeight() / zoom
  const canvasWidthCenter = canvasWidth / 2
  const canvasHeightCenter = canvasHeight / 2
  const canvasWidthCenterMap = { }
  const canvasHeightCenterMap = { }
  const centerLineMargin = 100
  const centerLineColor = '#4e5bf2'
  const centerLineWidth = 1
  const ctx = canvas.contextContainer
  let viewportTransform

  for (let i = canvasWidthCenter - centerLineMargin, len = canvasWidthCenter + centerLineMargin; i <= len; i++) {
    canvasWidthCenterMap[Math.round(i)] = true
  }
  for (let i = canvasHeightCenter - centerLineMargin, len = canvasHeightCenter + centerLineMargin; i <= len; i++) {
    canvasHeightCenterMap[Math.round(i)] = true
  }

  function showVerticalCenterLine () {
    showCenterLine(canvasWidthCenter + 0.5, 0, canvasWidthCenter + 0.5, canvasHeight)
  }

  function showHorizontalCenterLine () {
    showCenterLine(0, canvasHeightCenter + 0.5, canvasWidth, canvasHeightCenter + 0.5)
  }

  function showCenterLine (x1, y1, x2, y2) {
    ctx.save()
    ctx.strokeStyle = centerLineColor
    ctx.lineWidth = centerLineWidth
    ctx.beginPath()
    ctx.moveTo(x1 * viewportTransform[0], y1 * viewportTransform[3])
    ctx.lineTo(x2 * viewportTransform[0], y2 * viewportTransform[3])
    ctx.stroke()
    ctx.restore()
  }

  let isInVerticalCenter
  let isInHorizontalCenter

  canvas.on('mouse:down', function () {
    viewportTransform = canvas.viewportTransform
  })

  canvas.on('object:moving', function (e) {
    const object = e.target
    const objectCenter = object.getCenterPoint()
    const transform = canvas._currentTransform

    if (!transform) { return }

    isInVerticalCenter = Math.round(objectCenter.x) in canvasWidthCenterMap
    isInHorizontalCenter = Math.round(objectCenter.y) in canvasHeightCenterMap

    if (isInHorizontalCenter || isInVerticalCenter) {
      object.setPositionByOrigin(new fabric.Point((isInVerticalCenter ? canvasWidthCenter : objectCenter.x), (isInHorizontalCenter ? canvasHeightCenter : objectCenter.y)), 'center', 'center')
    }
  })

  canvas.on('before:render', function () {
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
  })

  canvas.on('after:render', function () {
    if (isInVerticalCenter) {
      showVerticalCenterLine()
    }
    if (isInHorizontalCenter) {
      showHorizontalCenterLine()
    }
  })

  canvas.on('mouse:up', function () {
    // clear these values, to stop drawing guidelines once mouse is up
    isInVerticalCenter = isInHorizontalCenter = null
    canvas.renderAll()
  })
}
