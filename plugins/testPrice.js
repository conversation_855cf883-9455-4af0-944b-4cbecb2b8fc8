import lscache from 'lscache'

export default ({ store }, inject) => {
  if (lscache.get('correct_price') === null) {
    const value = !!(Math.floor(Math.random() * 100) % 2)
    const expire = 60 * 24 * 7 // Minutes
    lscache.set('correct_price', value, expire)
  }

  const correctTestPriceCampaignAtListing = (campaign) => {
    if (lscache.get('correct_price') === true && campaign.r_price && campaign.seller_id === store.state.storeInfo.seller_id) {
      campaign.price = campaign.r_price
      campaign.old_price = campaign.r_old_price
    }

    return campaign
  }

  const correctTestPriceCampaignAtCampaignDetail = (campaign) => {
    if (lscache.get('correct_price') === true && campaign && campaign.seller_id === store.state.storeInfo.seller_id) {
      campaign.products = mapPriceWithoutCheckSellerId(campaign.products)
      campaign.variants = mapPriceWithoutCheckSellerId(campaign.variants)
    }

    return campaign
  }

  const correctTestPriceCampaignAtBundleDiscount = (campaign) => {
    if (lscache.get('correct_price') === true && campaign) {
      const sellerId = store.state.storeInfo.seller_id

      if (campaign.products) {
        campaign.products = campaign.products.map((p) => {
          if (p.r_price && p.seller_id === sellerId) {
            p.price = p.r_price
            p.old_price = p.r_old_price
          }

          return p
        })
      }

      if (campaign.variants) {
        campaign.variants = mapPriceWithoutCheckSellerId(campaign.variants)
      }
    }

    return campaign
  }

  function mapPriceWithoutCheckSellerId (es) {
    return es.map((e) => {
      if (e.r_price) {
        e.price = e.r_price
        e.old_price = e.r_old_price
      }

      return e
    })
  }

  inject('correctTestPriceCampaignAtListing', correctTestPriceCampaignAtListing)
  inject('correctTestPriceCampaignAtCampaignDetail', correctTestPriceCampaignAtCampaignDetail)
  inject('correctTestPriceCampaignAtBundleDiscount', correctTestPriceCampaignAtBundleDiscount)
}
