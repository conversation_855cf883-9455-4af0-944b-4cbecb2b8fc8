import { fabric } from 'fabric'

export class MockupRender {
  constructor (mockup) {
    if (!mockup || !mockup.background) {
      return false
    }
    this.mockup = mockup
  }

  async render () {
    const mockup = this.mockup

    if (!mockup || !mockup.background) {
      return
    }

    let canvas

    if (mockup.canvasEl) {
      this.canvasEl = mockup.canvasEl
      canvas = new fabric.Canvas(mockup.canvasEl)
    }

    if (!canvas) {
      canvas = new fabric.Canvas(mockup.canvasID)
    // canvas = new fabric.StaticCanvas();
    }

    this.canvas = canvas
    const color = mockup.color

    // load background
    if (!mockup.background) { return }
    const actualWidth = mockup.background.width; const actualHeight = mockup.background.height

    const scaleRatio = mockup.maxSize / Math.max(actualWidth, actualHeight)
    const width = actualWidth * scaleRatio
    const height = actualHeight * scaleRatio

    this.width = width
    this.height = height

    canvas.setWidth(width)
    canvas.setHeight(height)

    const brightness = this._getBrightness(color)

    this.filters = [new fabric.Image.filters.Brightness({ brightness: Math.min(-0.5 + brightness / 100, 0) }),
      new fabric.Image.filters.Grayscale()]

    // load color layer
    const colorRects = []
    if (mockup.colorImg) {
      const colorRect = new fabric.Rect({
        width,
        height,
        fill: color,
        opacity: 1,
        globalCompositeOperation: 'source-atop'
      })
      colorRects.push(colorRect)
      mockup.colorImg.scaleToWidth(width)
      this.colorGroup = new fabric.Group([mockup.colorImg, colorRect])
      canvas.add(this.colorGroup)
    }

    if (mockup.overlay) {
      mockup.overlay.scaleToWidth(width)
      mockup.overlay.set({ opacity: 1 - Math.min(2 * brightness / 100, 1), globalCompositeOperation: 'overlay' })
      canvas.add(mockup.overlay)
    }

    // render and load design
    if (mockup.glb) {
      const width3d = width * 1.5 // load bigger 3d renderer for better quality
      const glbCanvas = await this._loadObj(width3d)
      if (glbCanvas) {
        this.design = new fabric.Image(glbCanvas)
      }
    } else if (mockup.design3DImg) {
      this.design = mockup.design3DImg
    }

    if (!this.design) {
    // init empty design
      this.design = new fabric.Rect({
        width: 0,
        height: 0,
        opacity: 1
      })
    } else {
      this.design.scaleToWidth(width)
    }

    // load crop layer
    let designGroup
    if (mockup.cropImg) {
      mockup.cropImg.scaleToWidth(width)
      this.design.set({ globalCompositeOperation: 'source-in' })
      designGroup = new fabric.Group([mockup.cropImg, this.design])
    } else {
      designGroup = new fabric.Group([this.design])
    }

    if (mockup.cropImg && typeof document === 'undefined') {
    // fix crop design server side
      const designCanvas = new fabric.StaticCanvas(null, { width, height })
      this.designStaticCanvas = designCanvas
      designCanvas.add(designGroup)
      designCanvas.renderAll()
      const designCanvasImg = new fabric.Image(designCanvas.lowerCanvasEl)
      canvas.add(designCanvasImg)
    } else {
      canvas.add(designGroup)
    }

    this.designGroup = designGroup
    this.colorRects = colorRects

    // load shadow layer
    if (mockup.shadowImg) {
      mockup.shadowImg.scaleToWidth(width)
      // mockup.shadowImg.filters = this.filters;
      // mockup.shadowImg.applyFilters()
      // const colorMode = (brightness < 50) ? 'lighten' : 'mutiply'
      // mockup.shadowImg.set({ opacity: 0.5, globalCompositeOperation: colorMode })
      canvas.add(mockup.shadowImg)
    }

    // set background
    mockup.background.scaleToWidth(width)
    mockup.background.set({ globalCompositeOperation: 'destination-over' })
    canvas.add(mockup.background)

    // disable selection
    canvas.selection = false
    canvas.forEachObject(function (o) {
      o.selectable = false
      o.hoverCursor = 'default'
    })

    canvas.renderAll()
  }

  _clone (obj) {
    return new Promise((resolve, reject) => {
      obj.clone(function (clonedObj) {
        resolve(clonedObj)
      })
    })
  }

  _loadObj (width = 600, background = false) {
    return false
  }

  render3D () {

  }

  changeColor (color = 'white') {
    const mockup = this.mockup
    const brightness = this._getBrightness(color)
    // const colorMode = (brightness < 50) ? 'lighten' : 'mutiply'

    // update product color
    if (this.colorRects) {
      this.colorRects.forEach(function (colorRect) {
        colorRect.set({ fill: color })
      })

      // if (mockup.shadowImg) {
      //   mockup.shadowImg.filters[0]['brightness'] = Math.min(- 0.5 + brightness / 100, 0)
      //   mockup.shadowImg.applyFilters()
      //   mockup.shadowImg.set({  globalCompositeOperation: colorMode })
      // }

      if (mockup.overlay) {
        mockup.overlay.set({ opacity: 1 - Math.min(2 * brightness / 100, 1) })
      }

      this.canvas.renderAll()
    }
  }

  showBackground (show = false) {
    const mode = show ? 'multiply' : 'destination-over'
    this.mockup.background.set({ globalCompositeOperation: mode })
    this.canvas.renderAll()
  }

  _changeCanvasDesign (newDesign) {
    if (newDesign) {
      newDesign.scaleToWidth(this.width)
      newDesign.set({
      // width: this.width,
      // height: this.height,
        top: -this.designGroup.height / 2,
        left: -this.designGroup.width / 2
      })
    } else {
      newDesign = new fabric.Rect({
        width: 1,
        height: 1,
        opacity: 0
      })
    }

    if (this.mockup.cropImg) {
      newDesign.set({ globalCompositeOperation: 'source-in' })
    }

    this.designGroup.remove(this.design)
    this.designGroup.add(newDesign)

    this.design = newDesign

    if (this.designStaticCanvas) {
      this.designStaticCanvas.renderAll()
    }
    this.canvas.renderAll()
  }

  // return design image with correct size
  _correctDesignSize (designImg, printWidth, printHeight) {
    const width = this.width; const height = printHeight * width / printWidth
    let canvas = this.correctCanvas
    if (!canvas) {
      canvas = new fabric.Canvas()
      this.correctCanvas = canvas
      canvas.setWidth(width)
      canvas.setHeight(height)
    }

    designImg.scaleToWidth(width)
    if (designImg.getScaledHeight() > height) {
      designImg.scaleToHeight(height)
    }

    designImg.set({
      left: (width - designImg.getScaledWidth()) / 2,
      top: (height - designImg.getScaledHeight()) / 2
    })

    if (this.designImg) {
      canvas.remove(this.designImg)
    }
    canvas.add(designImg)
    this.designImg = designImg
    canvas.renderAll()

    return canvas.lowerCanvasEl
  }

  mockupPNG () {
    return this.canvas.toDataURL('image/png')
  }

  _loadImg (fileUrl) {
    if (!fileUrl || typeof fabric === 'undefined') { return }
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(fileUrl,
        function (img) {
          resolve(img)
        }, { crossOrigin: 'Anonymous' })
    })
  }

  _correctColor (color) {
    color = this._colourNameToHex(color)
    if (this._getBrightness(color) < 20) {
      color = this._getNewBrightnessColor(color, 20)
    }
    return color
  }

  _getBrightness (color) {
    color = this._colourNameToHex(color)
    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)
    return parseInt((0.299 * r + 0.587 * g + 0.114 * b) / 255 * 100)
  }

  _getNewBrightnessColor (rgbcode, brightness) {
    const _super = this
    const r = parseInt(rgbcode.slice(1, 3), 16)
    const g = parseInt(rgbcode.slice(3, 5), 16)
    const b = parseInt(rgbcode.slice(5, 7), 16)
    const HSL = _super._rgbToHsl(r, g, b)

    HSL[2] = brightness / 100

    const RGB = _super._hslToRgb(HSL[0], HSL[1], HSL[2])
    rgbcode = '#' + _super._convertToTwoDigitHexCodeFromDecimal(RGB[0]) + _super._convertToTwoDigitHexCodeFromDecimal(RGB[1]) + _super._convertToTwoDigitHexCodeFromDecimal(RGB[2])

    return rgbcode
  }

  _rgbToHsl (r, g, b) {
    r /= 255
    g /= 255
    b /= 255
    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h; let s; const l = (max + min) / 2

    if (max === min) {
      h = s = 0 // achromatic
    } else {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0)
          break
        case g:
          h = (b - r) / d + 2
          break
        case b:
          h = (r - g) / d + 4
          break
      }
      h /= 6
    }

    return [h, s, l]
  }

  _hslToRgb (h, s, l) {
    let r, g, b

    if (s === 0) {
      r = g = b = l // achromatic
    } else {
    // eslint-disable-next-line no-inner-declarations
      function hue2rgb (p, q, t) {
        if (t < 0) { t += 1 }
        if (t > 1) { t -= 1 }
        if (t < 1 / 6) { return p + (q - p) * 6 * t }
        if (t < 1 / 2) { return q }
        if (t < 2 / 3) { return p + (q - p) * (2 / 3 - t) * 6 }
        return p
      }

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s
      const p = 2 * l - q
      r = hue2rgb(p, q, h + 1 / 3)
      g = hue2rgb(p, q, h)
      b = hue2rgb(p, q, h - 1 / 3)
    }

    return [r * 255, g * 255, b * 255]
  }

  _convertToTwoDigitHexCodeFromDecimal (decimal) {
    let code = Math.round(decimal).toString(16)
    if (typeof code.length !== 'undefined') {
      (code.length > 1) || (code = '0' + code)
      return code
    }
  }

  _colourNameToHex (colour) {
    if (typeof colour === 'undefined') { return '#000000' }

    const colours = {
      aliceblue: '#f0f8ff',
      antiquewhite: '#faebd7',
      aqua: '#00ffff',
      aquamarine: '#7fffd4',
      azure: '#f0ffff',
      beige: '#f5f5dc',
      bisque: '#ffe4c4',
      black: '#000000',
      blanchedalmond: '#ffebcd',
      blue: '#0000ff',
      blueviolet: '#8a2be2',
      brown: '#a52a2a',
      burlywood: '#deb887',
      cadetblue: '#5f9ea0',
      chartreuse: '#7fff00',
      chocolate: '#d2691e',
      coral: '#ff7f50',
      cornflowerblue: '#6495ed',
      cornsilk: '#fff8dc',
      crimson: '#dc143c',
      cyan: '#00ffff',
      darkblue: '#00008b',
      darkcyan: '#008b8b',
      darkgoldenrod: '#b8860b',
      darkgray: '#a9a9a9',
      darkgreen: '#006400',
      darkkhaki: '#bdb76b',
      darkmagenta: '#8b008b',
      darkolivegreen: '#556b2f',
      darkorange: '#ff8c00',
      darkorchid: '#9932cc',
      darkred: '#8b0000',
      darksalmon: '#e9967a',
      darkseagreen: '#8fbc8f',
      darkslateblue: '#483d8b',
      darkslategray: '#2f4f4f',
      darkturquoise: '#00ced1',
      darkviolet: '#9400d3',
      deeppink: '#ff1493',
      deepskyblue: '#00bfff',
      dimgray: '#696969',
      dodgerblue: '#1e90ff',
      firebrick: '#b22222',
      floralwhite: '#fffaf0',
      forestgreen: '#228b22',
      fuchsia: '#ff00ff',
      gainsboro: '#dcdcdc',
      ghostwhite: '#f8f8ff',
      gold: '#ffd700',
      goldenrod: '#daa520',
      gray: '#808080',
      green: '#008000',
      greenyellow: '#adff2f',
      honeydew: '#f0fff0',
      hotpink: '#ff69b4',
      indianred: '#cd5c5c',
      indigo: '#4b0082',
      ivory: '#fffff0',
      khaki: '#f0e68c',
      lavender: '#e6e6fa',
      lavenderblush: '#fff0f5',
      lawngreen: '#7cfc00',
      lemonchiffon: '#fffacd',
      lightblue: '#add8e6',
      lightcoral: '#f08080',
      lightcyan: '#e0ffff',
      lightgoldenrodyellow: '#fafad2',
      lightgrey: '#d3d3d3',
      lightgreen: '#90ee90',
      lightpink: '#ffb6c1',
      lightsalmon: '#ffa07a',
      lightseagreen: '#20b2aa',
      lightskyblue: '#87cefa',
      lightslategray: '#778899',
      lightsteelblue: '#b0c4de',
      lightyellow: '#ffffe0',
      lime: '#00ff00',
      limegreen: '#32cd32',
      linen: '#faf0e6',
      magenta: '#ff00ff',
      maroon: '#800000',
      mediumaquamarine: '#66cdaa',
      mediumblue: '#0000cd',
      mediumorchid: '#ba55d3',
      mediumpurple: '#9370d8',
      mediumseagreen: '#3cb371',
      mediumslateblue: '#7b68ee',
      mediumspringgreen: '#00fa9a',
      mediumturquoise: '#48d1cc',
      mediumvioletred: '#c71585',
      midnightblue: '#191970',
      mintcream: '#f5fffa',
      mistyrose: '#ffe4e1',
      moccasin: '#ffe4b5',
      navajowhite: '#ffdead',
      navy: '#000080',
      oldlace: '#fdf5e6',
      olive: '#808000',
      olivedrab: '#6b8e23',
      orange: '#ffa500',
      orangered: '#ff4500',
      orchid: '#da70d6',
      palegoldenrod: '#eee8aa',
      palegreen: '#98fb98',
      paleturquoise: '#afeeee',
      palevioletred: '#d87093',
      papayawhip: '#ffefd5',
      peachpuff: '#ffdab9',
      peru: '#cd853f',
      pink: '#ffc0cb',
      plum: '#dda0dd',
      powderblue: '#b0e0e6',
      purple: '#800080',
      rebeccapurple: '#663399',
      red: '#ff0000',
      rosybrown: '#bc8f8f',
      royalblue: '#4169e1',
      saddlebrown: '#8b4513',
      salmon: '#fa8072',
      sandybrown: '#f4a460',
      seagreen: '#2e8b57',
      seashell: '#fff5ee',
      sienna: '#a0522d',
      silver: '#c0c0c0',
      skyblue: '#87ceeb',
      slateblue: '#6a5acd',
      slategray: '#708090',
      snow: '#fffafa',
      springgreen: '#00ff7f',
      steelblue: '#4682b4',
      tan: '#d2b48c',
      teal: '#008080',
      thistle: '#d8bfd8',
      tomato: '#ff6347',
      turquoise: '#40e0d0',
      violet: '#ee82ee',
      wheat: '#f5deb3',
      white: '#ffffff',
      whitesmoke: '#f5f5f5',
      yellow: '#ffff00',
      yellowgreen: '#9acd32'
    }

    if (typeof colours[colour.toLowerCase()] !== 'undefined') { return colours[colour.toLowerCase()] }

    return colour
  }
}

export class MockupLoader {
  async loadMockup (params) {
    const res = await Promise.all([
      this._loadImg(params.background, params.maxSize),
      this._loadImg(params.colorImg, params.maxSize),
      this._loadImg(params.cropImg, params.maxSize),
      this._loadImg(params.shadowImg, params.maxSize),
      this._loadObj(params.glb),
      this._loadTexture(params.background),
      this._loadImg(params.designImg, params.maxSize),
      this._loadImg(params.design3DImg, params.maxSize),
      this._loadImg(params.overlay, params.maxSize)
    ])
    return {
      id: params.id,
      name: this._name(params.name),
      canvasID: params.canvasID,
      canvasEl: params.canvasEl,
      maxSize: params.maxSize || 2000,
      printWidth: params.printWidth || 1000,
      printHeight: params.printHeight || 1000,
      background: res[0],
      colorImg: res[1],
      cropImg: res[2],
      shadowImg: res[3],
      glb: res[4],
      texture: res[5],
      designImg: res[6],
      design3DImg: res[7],
      overlay: res[8],
      color: params.color || '#FFFFFF'
    }
  }

  _loadImg (fileUrl, size = 1600) {
    if (typeof fileUrl === 'undefined' || typeof fabric === 'undefined') { return }
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(fileUrl,
        function (img) {
          resolve(img)
        }, { crossOrigin: 'Anonymous' })
    })
  }

  _loadObj (fileUrl) {
  // implement in child class
    return false
  }

  _loadTexture (fileUrl) {
  // implement in child class
    return false
  }

  _name (str) {
    if (!str) { return 'print' }
    return name(str)
  }
}

export default (context, inject) => {
  inject('MockupRender', MockupRender)
  inject('MockupLoader', MockupLoader)
}
/* eslint-disable */
function namecycle (x, k) {
var a = x[0], b = x[1], c = x[2], d = x[3]

a = ff(a, b, c, d, k[0], 7, -680876936)
d = ff(d, a, b, c, k[1], 12, -389564586)
c = ff(c, d, a, b, k[2], 17,  606105819)
b = ff(b, c, d, a, k[3], 22, -1044525330)
a = ff(a, b, c, d, k[4], 7, -176418897)
d = ff(d, a, b, c, k[5], 12,  1200080426)
c = ff(c, d, a, b, k[6], 17, -1473231341)
b = ff(b, c, d, a, k[7], 22, -45705983)
a = ff(a, b, c, d, k[8], 7,  1770035416)
d = ff(d, a, b, c, k[9], 12, -1958414417)
c = ff(c, d, a, b, k[10], 17, -42063)
b = ff(b, c, d, a, k[11], 22, -1990404162)
a = ff(a, b, c, d, k[12], 7,  1804603682)
d = ff(d, a, b, c, k[13], 12, -40341101)
c = ff(c, d, a, b, k[14], 17, -1502002290)
b = ff(b, c, d, a, k[15], 22,  1236535329)

a = gg(a, b, c, d, k[1], 5, -165796510)
d = gg(d, a, b, c, k[6], 9, -1069501632)
c = gg(c, d, a, b, k[11], 14,  643717713)
b = gg(b, c, d, a, k[0], 20, -373897302)
a = gg(a, b, c, d, k[5], 5, -701558691)
d = gg(d, a, b, c, k[10], 9,  38016083)
c = gg(c, d, a, b, k[15], 14, -660478335)
b = gg(b, c, d, a, k[4], 20, -405537848)
a = gg(a, b, c, d, k[9], 5,  568446438)
d = gg(d, a, b, c, k[14], 9, -1019803690)
c = gg(c, d, a, b, k[3], 14, -187363961)
b = gg(b, c, d, a, k[8], 20,  1163531501)
a = gg(a, b, c, d, k[13], 5, -1444681467)
d = gg(d, a, b, c, k[2], 9, -51403784)
c = gg(c, d, a, b, k[7], 14,  1735328473)
b = gg(b, c, d, a, k[12], 20, -1926607734)

a = hh(a, b, c, d, k[5], 4, -378558)
d = hh(d, a, b, c, k[8], 11, -2022574463)
c = hh(c, d, a, b, k[11], 16,  1839030562)
b = hh(b, c, d, a, k[14], 23, -35309556)
a = hh(a, b, c, d, k[1], 4, -1530992060)
d = hh(d, a, b, c, k[4], 11,  1272893353)
c = hh(c, d, a, b, k[7], 16, -155497632)
b = hh(b, c, d, a, k[10], 23, -1094730640)
a = hh(a, b, c, d, k[13], 4,  681279174)
d = hh(d, a, b, c, k[0], 11, -358537222)
c = hh(c, d, a, b, k[3], 16, -722521979)
b = hh(b, c, d, a, k[6], 23,  76029189)
a = hh(a, b, c, d, k[9], 4, -640364487)
d = hh(d, a, b, c, k[12], 11, -421815835)
c = hh(c, d, a, b, k[15], 16,  530742520)
b = hh(b, c, d, a, k[2], 23, -995338651)

a = ii(a, b, c, d, k[0], 6, -198630844)
d = ii(d, a, b, c, k[7], 10,  1126891415)
c = ii(c, d, a, b, k[14], 15, -1416354905)
b = ii(b, c, d, a, k[5], 21, -57434055)
a = ii(a, b, c, d, k[12], 6,  1700485571)
d = ii(d, a, b, c, k[3], 10, -1894986606)
c = ii(c, d, a, b, k[10], 15, -1051523)
b = ii(b, c, d, a, k[1], 21, -2054922799)
a = ii(a, b, c, d, k[8], 6,  1873313359)
d = ii(d, a, b, c, k[15], 10, -30611744)
c = ii(c, d, a, b, k[6], 15, -1560198380)
b = ii(b, c, d, a, k[13], 21,  1309151649)
a = ii(a, b, c, d, k[4], 6, -145523070)
d = ii(d, a, b, c, k[11], 10, -1120210379)
c = ii(c, d, a, b, k[2], 15,  718787259)
b = ii(b, c, d, a, k[9], 21, -343485551)

x[0] = add32(a, x[0])
x[1] = add32(b, x[1])
x[2] = add32(c, x[2])
x[3] = add32(d, x[3])

}

function cmn(q, a, b, x, s, t) {
a = add32(add32(a, q), add32(x, t))
return add32((a << s) | (a >>> (32 - s)), b)
}

function ff(a, b, c, d, x, s, t) {
return cmn((b & c) | ((~b) & d), a, b, x, s, t)
}

function gg(a, b, c, d, x, s, t) {
return cmn((b & d) | (c & (~d)), a, b, x, s, t)
}

function hh(a, b, c, d, x, s, t) {
return cmn(b ^ c ^ d, a, b, x, s, t)
}

function ii(a, b, c, d, x, s, t) {
return cmn(c ^ (b | (~d)), a, b, x, s, t)
}

function name1(s) {
var txt = ''
var n = s.length,
state = [1732584193, -271733879, -1732584194, 271733878], i
for (i=64; i<=s.length; i+=64) {
namecycle(state, nameblk(s.substring(i-64, i)))
}
s = s.substring(i-64)
var tail = [0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0]
for (i=0; i<s.length; i++)
tail[i>>2] |= s.charCodeAt(i) << ((i%4) << 3)
tail[i>>2] |= 0x80 << ((i%4) << 3)
if (i > 55) {
namecycle(state, tail)
for (i=0; i<16; i++) tail[i] = 0
}
tail[14] = n*8
namecycle(state, tail)
return state
}

/* there needs to be support for Unicode here,
 * unless we pretend that we can redefine the MD-5
 * algorithm for multi-byte characters (perhaps
 * by adding every four 16-bit characters and
 * shortening the sum to 32 bits). Otherwise
 * I suggest performing MD-5 as if every character
 * was two bytes--e.g., 0040 0025 = @%--but then
 * how will an ordinary MD-5 sum be matched?
 * There is no way to standardize text to something
 * like UTF-8 before transformation; speed cost is
 * utterly prohibitive. The JavaScript standard
 * itself needs to look at this: it should start
 * providing access to strings as preformed UTF-8
 * 8-bit unsigned value arrays.
 */
function nameblk(s) { /* I figured global was faster.   */
var nameblks = [], i; /* Andy King said do it this way. */
for (i=0; i<64; i+=4) {
nameblks[i>>2] = s.charCodeAt(i)
+ (s.charCodeAt(i+1) << 8)
+ (s.charCodeAt(i+2) << 16)
+ (s.charCodeAt(i+3) << 24)
}
return nameblks
}

var hex_chr = '0123456789abcdef'.split('')

function rhex(n)
{
var s='', j=0
for(; j<4; j++)
s += hex_chr[(n >> (j * 8 + 4)) & 0x0F]
+ hex_chr[(n >> (j * 8)) & 0x0F]
return s
}

function hex(x) {
for (var i=0; i<x.length; i++)
x[i] = rhex(x[i])
return x.join('')
}

function name(s) {
return hex(name1(s))
}

/* this function is much faster,
so if possible we use it. Some IEs
are the only ones I know of that
need the idiotic second function,
generated by an if clause.  */

function add32(a, b) {
return (a + b) & 0xFFFFFFFF
}

if (name('hello') != '5d41402abc4b2a76b9719d911017c592') {
function add32(x, y) {
var lsw = (x & 0xFFFF) + (y & 0xFFFF),
msw = (x >> 16) + (y >> 16) + (lsw >> 16)
return (msw << 16) | (lsw & 0xFFFF)
}
}
