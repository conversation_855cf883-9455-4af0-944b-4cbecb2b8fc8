import lscache from 'lscache'
import union from 'lodash/union'
let noCache

export default ({ store, $axios, $toast, $config: { baseUrl, hasWriteApi, xDomain }, req, res, query }, inject) => {
  const getQuery = (key) => {
    if (process.browser) {
      const url = new URL(window.top.location.href)

      return url.searchParams.get(key)
    }

    return query[key]
  }

  const getHost = () => {
    if (process.browser) {
      return window.top.location.hostname
    }

    return req.headers.host
  }

  let host
  let protocol = 'https:'
  let port = ''
  if (process.browser) {
    host = window.top.location.hostname
    protocol = window.top.location.protocol
    if (window.top.location.port !== '') {
      port = ':' + window.top.location.port
    }
  } else {
    host = req.headers.host
  }
  if (host.includes(':')) {
    host = host.split(':')[0]
  }

  $axios.defaults.headers.common['X-DOMAIN'] = xDomain ?? host
  const spsid = query.spsid || getCookie('spsid', req?.headers.cookie)
  if (spsid) {
    $axios.defaults.headers.common.spsid = spsid
  }

  // add flush cache header
  if (query.fcache) {
    $axios.defaults.headers.common.fcache = query.fcache
  }

  if (typeof noCache === 'undefined') {
    noCache = lscache.get('no-cache') || false
  }

  if (query.nocache) {
    noCache = true
    lscache.set('no-cache', noCache, 60) // set no cache for 60 min
  }

  if (noCache) {
    $axios.defaults.headers.common['No-Cache'] = 'true'
  }

  function masterUrl (url) {
    if (hasWriteApi && process.browser && !url.startsWith('http')) {
      url = `${protocol}//${host}${port}/api2${url}`
    } else {
      const writeBaseUrl = process.env.PROXY_API_URL2 || process.env.PROXY_API_URL || 'https://apis.dev.senprints.net'
      url = `${writeBaseUrl}${url}`
    }
    return url
  }

  function httpWrite (method, url, data, headers) {
    return httpDefault(method, masterUrl(url), data, headers)
  }

  let cacheTags = []
  function httpDefault (method, url, data, headers) {
    if (process.browser && !$axios.defaults.headers.common['X-Client-IP'] && store.state.clientIp) {
      $axios.defaults.headers.common['X-Client-IP'] = store.state.clientIp
    }
    if ((process.browser && getHost().includes('localhost')) || getQuery('debug')) {
      console.groupCollapsed('%c' + method, 'color:green', url)
      console.log(data)
      console.groupEnd()
    }
    return new Promise((resolve, reject) => {
      $axios({ method, url, data, headers }).then((result) => {
        if ((process.browser && getHost().includes('localhost')) || getQuery('debug')) {
          console.groupCollapsed('%cresponse', 'color:blue', url)
          console.log(result.data)
          console.groupEnd()
        }
        if (result.headers['cache-tags']) {
          cacheTags = union(cacheTags, result.headers['cache-tags'].split(','))
        }
        if (res && cacheTags.length) {
          res.setHeader('Cache-Tags', cacheTags.toString())
        }
        if (res && result.headers['cache-expire-time']) {
          res.setHeader('Cache-Expire-Time', result.headers['cache-expire-time'])
        }

        resolve(result.data)
      }).catch((error) => {
        if ((process.browser && getHost().includes('localhost')) || getQuery('debug')) {
          console.groupCollapsed('%cresponse-error', 'color:red', url)
          console.log(error)
          console.groupEnd()
          $toast.error(error.message)
        }
        reject(error)
      })
    })
  }

  function httpStats (method, url, data) {
    url = `${protocol}//${host}${port}/apistats${url}`
    // console.log('stats api url', url)
    return httpDefault(method, url, data)
  }

  function getCookie (cookieName, stringCookie) {
    if (!stringCookie) { return null }
    const strCookie = new RegExp('' + cookieName + '[^;]+').exec(stringCookie)
    return strCookie ? strCookie.toString().replace(/^[^=]+./, '') : null
  }

  inject('httpDefault', httpDefault)
  inject('httpStats', httpStats)
  inject('httpWrite', httpWrite)
  inject('masterUrl', masterUrl)
}
