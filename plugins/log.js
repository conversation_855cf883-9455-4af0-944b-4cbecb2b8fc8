export default ({ req, query }, inject) => {
  const getQuery = (key) => {
    if (process.browser) {
      const url = new URL(window.top.location.href)

      return url.searchParams.get(key)
    }

    return query[key]
  }

  const getHost = () => {
    if (process.browser) {
      return window.top.location.hostname
    }

    return req.headers.host
  }

  const log = (data, type = null) => {
    // disable on production
    if (!getHost().includes('localhost') && !getQuery('debug')) {
      return
    }

    if (type && typeof console[type] === 'function') {
      console[type](data)
    } else {
      console.log(data)
    }
  }

  inject('log', log)
}
