import { v4 as uuidv4 } from 'uuid'
import lscache from 'lscache'
import UAParser from 'ua-parser-js'
import { load as FingerPrintLoad } from '@fingerprintjs/fingerprintjs'
import { sha256, getQueriesString, parseCfInfo, isBot } from '~/helpers/function'
import { TRACKING_EVENT_LIST, TRACKING_PLATFORMS } from '~/helpers/variableConst'

export default ({ $gtm, app, store, route, router, $axios, $i18n, $config: { googleAnalytics, gtm, appEnv, trackingEndpointUrl, baseImageUrlVN, newTrackingEndpointUrl } }, inject) => {
  const tracking = {
    initTracking,
    trackEvent,
    retrackEvent,
    newCustomTracking,
    customTracking
  }
  let trackingData = {}
  let trackingInfo = {}
  let eventQueueList = []
  const API_ENDPOINT = trackingEndpointUrl
  const NEW_API_ENDPOINT = newTrackingEndpointUrl
  const sessionExpireTime = 24 * 60

  const appId = 'customer'
  let sessionId = null
  let trackingInit = false
  let visitInfo
  let userInfo
  let client = {}
  // const googleAnalytics = null

  // Init Tracking
  async function initTracking () {
    if (trackingInit) {
      return
    }
    initSessionId()
    await initClient()

    trackingData = getTrackingData()
    trackingInfo = getTrackingInfo()
    userInfo = lscache.get('userInfo') || {}

    // createSession()

    if (trackingData.facebook && window.fbq) {
      setFacebookPixel(trackingData.facebook)
    }

    if (trackingData.gtm) {
      $gtm.init(trackingData.gtm)
    }

    trackingInit = true
    retrackEvent()
    return true
  }

  function getTrackingData () {
    const trackingData = {
      facebook: ''
    }
    // campaign
    try {
      if (route.name.startsWith('campaign___')) {
        const currentCampaign = store.state.campaign.campaignList[store.state.campaign.currentCampaignSlug]?.campaignData
        let trackingCodeCampaign = currentCampaign?.tracking_code
        if (trackingCodeCampaign) {
          if (typeof trackingCodeCampaign !== 'object') {
            trackingCodeCampaign = JSON.parse(trackingCodeCampaign)
          }
          if (trackingCodeCampaign.facebook_pixel) {
            trackingData.facebook = trackingCodeCampaign.facebook_pixel
          }
        }
      }
    } catch (e) {
      //
    }

    // store
    try {
      let trackingCodeStore = store.state.storeInfo.tracking_code
      if (trackingCodeStore) {
        if (typeof trackingCodeStore !== 'object') {
          trackingCodeStore = JSON.parse(trackingCodeStore)
        }
        if (trackingCodeStore.facebook_pixel) {
          trackingData.facebook = `${trackingData.facebook},${trackingCodeStore.facebook_pixel}`
        }

        if (trackingCodeStore.google_analytics && !trackingData.google) {
          trackingData.google = trackingCodeStore.google_analytics
        }

        if (trackingCodeStore.google_tag && !trackingData.gtm) {
          trackingData.gtm = trackingCodeStore.google_tag
        }
      }
    } catch (e) {
      //
    }

    // user
    let trackingCodeUser = store.state.storeInfo.trackingCode
    if (trackingCodeUser) {
      try {
        if (typeof trackingCodeUser !== 'object') {
          trackingCodeUser = JSON.parse(trackingCodeUser)
        }
        if (trackingCodeUser.facebook_pixel) {
          trackingData.facebook = `${trackingData.facebook},${trackingCodeUser.facebook_pixel}`
        }
        if (trackingCodeUser.google_analytics && !trackingData.google) {
          trackingData.google = trackingCodeUser.google_analytics
        }

        if (trackingCodeUser.google_tag_manager && !trackingData.gtm) {
          trackingData.gtm = trackingCodeUser.google_tag_manager
        }
      } catch (e) {
        // noop
      }
    }

    if (googleAnalytics && !trackingData.google) {
      trackingData.google = googleAnalytics
    }

    if (gtm.id && !trackingData.gtm) {
      trackingData.gtm = gtm.id
    }

    return trackingData
  }

  function getTrackingInfo () {
    const info = {}
    const visitInfo = getVisitInfo()
    if (visitInfo && visitInfo.seller_id) {
      info.sellerId = visitInfo.seller_id
    } else if (store.state.currentSellerId) {
      info.sellerId = store.state.currentSellerId
    } else if (store.state.storeInfo.seller_id) {
      info.sellerId = store.state.storeInfo.seller_id
    }

    if (store.state.storeInfo.id) {
      info.storeId = store.state.storeInfo.id
    }
    return info
  }

  function setFacebookPixel (pixel) {
    if (pixel.includes(',')) {
      // convert to array
      // "a, b" => "a,b" => ["a", "b"]
      pixel = pixel.replaceAll(' ', '').split(',').filter(item => item)
    }
    // check if we have multi-pixels
    const customerData = {
      em: (userInfo.email || '').toLowerCase(),
      fn: (userInfo.name || '').toLowerCase(),
      ph: (userInfo.phone || '').toLowerCase(),
      ct: (userInfo.city || '').toLowerCase(),
      st: (userInfo.state || '').toLowerCase(),
      zp: (userInfo.zipcode || '').toLowerCase(),
      country: (userInfo.country || '').toLowerCase()
    }
    if (Array.isArray(pixel)) {
      pixel.forEach((px) => {
        if (px) {
          window.fbq('init', px, customerData)
        }
      })
    } else if (pixel) {
      window.fbq('init', pixel, customerData)
    }
  }

  /* eslint-disable camelcase */
  function attachMoreClientInfo () {
    const browserInfo = new UAParser().getResult()
    client = {
      ...client,
      url: window.top.location.href,
      queries: getQueriesString(),
      browser: browserInfo.browser,
      device: browserInfo.device,
      os: browserInfo.os,
      useragent: browserInfo.ua,
      seller_id: store.state.currentSellerId
    }

    if (client.device.type === undefined) {
      client.device.type = 'desktop'
    }

    // update visit info
    if (!visitInfo) {
      visitInfo = lscache.get('visitInfo') || {}
    }

    if (!visitInfo.visitTime) {
      visitInfo.visitTime = new Date().getTime()
    }

    if (client.ip) {
      visitInfo.clientIp = client.ip
    }

    if (client.city) {
      visitInfo.city = client.city
    }

    if (client.region) {
      visitInfo.region = client.region
    }

    if (client.latitude) {
      visitInfo.latitude = client.latitude
    }

    if (client.longitude) {
      visitInfo.longitude = client.longitude
    }

    let currency = lscache.get('currency')
    if (!currency?.code) {
      currency = {}
    }

    const isFallbackCurrency = lscache.get('is_fallback_currency')
    if (isFallbackCurrency) {
      store.dispatch('setIsFallbackCurrency', true)
    }

    if (!currency.code) {
      const userCountry = store.state.generalInfo.countries.find(item => item.code === client.country)
      // priority: store default currency -> user country default currency -> (USD + mark as fallback)
      if (store.state.storeInfo.default_currency) {
        currency.code = store.state.storeInfo.default_currency
      } else if (userCountry?.default_currency_code) {
        currency.code = userCountry?.default_currency_code
        const acceptedCode = store.state.generalInfo.currencies.map(item => item.code)
        if (!acceptedCode.includes(currency.code)) {
          store.dispatch('setIsFallbackCurrency', true)
        }
      } else {
        currency.code = 'USD'
        store.dispatch('setIsFallbackCurrency', true)
      }

      const currentCurrency = store.state.generalInfo.currencies.find(item => item.code === currency.code)
      store.commit('changeCurrency', currentCurrency)
    } else {
      store.dispatch('getCurrencyFromStorage')
    }

    const { utm_source, source, utm_campaign, utm_medium, seid, fbclid, gclid, user_id, msclkid } = route.query
    let { spsid } = route.query
    if (utm_source || source) {
      visitInfo.ad_source = utm_source || source
    }
    if (utm_campaign || msclkid) {
      visitInfo.ad_campaign = utm_campaign || msclkid
    }
    if (utm_medium) {
      visitInfo.ad_medium = utm_medium
    }
    if (fbclid) {
      visitInfo.ad_id = fbclid
      visitInfo.ad_campaign = visitInfo.ad_campaign || 'facebook_ad'
    }
    if (gclid) {
      visitInfo.ad_id = gclid
      visitInfo.ad_campaign = visitInfo.ad_campaign || 'google_ad'
    }
    if (seid) {
      visitInfo.ad_id = seid
    }

    // set custom store seller_id
    if (store.state.storeInfo && store.state.storeInfo.id !== 1) {
      visitInfo.seller_id = store.state.storeInfo.seller_id
    } else if (spsid) {
      // overwrite current seller id if visit come from an affiliate
      if (Array.isArray(spsid)) {
        spsid = spsid[spsid.length - 1]
      }
      visitInfo.seller_id = spsid
      visitInfo.spsid = spsid
      app.$cookies.set('spsid', spsid, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7
      })
    }
    // add affiliate seller id
    if (visitInfo.spsid) {
      $axios.defaults.headers.common.spsid = visitInfo.spsid
    }
    if (client.seller_id !== visitInfo.seller_id) {
      client.seller_id = visitInfo.seller_id
    }

    store.state.clientIp = client.ip
    visitInfo.country = client.country
    visitInfo.currency_code = currency.code || 'USD'
    visitInfo.device = !visitInfo.dd ? (client.device && client.device.type) : 'desktop'
    visitInfo.device_detail = `${client.os.name}/${client.browser.name}`
    visitInfo.user_id = user_id ? parseInt(user_id) : undefined

    lscache.set('visitInfo', visitInfo)
    store.commit('changeUserCountry', client.country)
    return visitInfo
  }

  function updateAdsSource ({ utm_source, source, utm_campaign, utm_medium, seid, msclkid }) {
    const visitInfo = lscache.get('visitInfo') || {}
    if (utm_source || source) {
      visitInfo.ad_source = utm_source || source
    }
    if (utm_campaign || msclkid) {
      visitInfo.ad_campaign = utm_campaign || msclkid
    }
    if (utm_medium) {
      visitInfo.ad_medium = utm_medium
    }
    if (seid) {
      visitInfo.ad_id = seid
    }
    lscache.set('visitInfo', visitInfo)
  }

  /* eslint-enable camelcase */

  function getVisitInfo () {
    if (!visitInfo) {
      visitInfo = lscache.get('visitInfo') || attachMoreClientInfo()
    }
    return visitInfo
  }

  /**
   * @param url
   * @returns {Promise<Response>}
   */
  async function fetchApi (url) {
    return await fetch(url)
  }

  /**
     * Get info of current user by using CloudFlare
     *
     * @return {Promise<void>}
     */
  async function initClient () {
    // use fetch() because axios having a CORS problem
    const data = {}
    let response = await fetchApi('https://speed.cloudflare.com/meta')
    if (!response.ok || response.status !== 200) {
      response = await fetchApi(`${document?.location?.origin || ''}/cdn-cgi/trace`)
      if (!response.ok || response.status !== 200) {
        response = await fetchApi('https://senprints.com/cdn-cgi/trace')
      }
      response = await response.text()
      // check if we have a valid response
      if (!response || !response.includes('visit_scheme')) {
        return attachMoreClientInfo()
      }
      const lines = response.split('\n').filter(res => res !== '' && res !== null)
      const total = lines.length - 1
      for (let i = 0; i <= total; i++) {
        const [name, value] = lines[i].split('=')
        data[name] = value
        delete lines[i]
      }
      client = parseCfInfo(data)
    } else {
      const data = await response.json()
      data.ip = data?.clientIp
      client = parseCfInfo(data)
    }
    attachMoreClientInfo()
  }

  /**
     * General an unique UUID for current customer
     */
  function initSessionId () {
    // todo: check supported() and
    //  add a fallback method (cookie)
    let id = lscache.get('senprints-session-id')

    if (!id) {
      id = uuidv4()
      lscache.set('senprints-session-id', id, sessionExpireTime) // one day
      createSession(id)
    }

    sessionId = id
    return sessionId
  }

  // function getCurrentPath () {
  //   const loc = window.top.location

  //   // https://stackoverflow.com/a/16376491
  //   return `${loc.pathname}${loc.search}`
  // }

  async function getFingerPrint (data) {
    const fpPromise = FingerPrintLoad()
    // Get the visitor identifier when you need it.
    const fp = await fpPromise
    const result = await fp.get()
    // This is the visitor identifier:
    const id = result.visitorId
    if (!isBot()) {
      await fetch(NEW_API_ENDPOINT, {
        method: 'PUT', // or 'PUT'
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'fingerprint',
          id,
          ...client,
          ...data
        })
      })
    }
    return id
  }

  function updateVisitInfo (sessionId, fingerprintId) {
    const visitInfo = getVisitInfo()
    visitInfo.session_id = sessionId
    visitInfo.device_id = fingerprintId
    lscache.set('visitInfo', visitInfo)
  }

  async function createSession (id = null) {
    if (id == null) {
      id = initSessionId()
    } else {
      lscache.remove('session-created')
    }
    if (lscache.get('session-created')) {
      return
    }

    const location = null
    //    if (!location) {
    //      try {
    //        location = await getLocation()
    //      } catch (e) { }
    //    }

    const fingerprintId = await getFingerPrint({ location })
    updateVisitInfo(id, fingerprintId)
    if (!isBot()) {
      let response = await fetch(NEW_API_ENDPOINT, {
        method: 'PUT', // or 'PUT'
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'session',
          id,
          fingerprintId,
          location,
          referrer: document.referrer,
          ...client
        })
      })
      response = await response.json()
      if (response.success) {
        lscache.set('session-created', true, sessionExpireTime)
      }
    }
  }

  // Track Event
  function trackEvent (trackData, retrack = false, platform = TRACKING_PLATFORMS.ALL) {
    const { event, options, campaignId, campaignSlug } = trackData
    if (isBot()) {
      return
    }
    if (typeof window.SP_VISIT_ACTIVITY === 'undefined') {
      window.SP_VISIT_ACTIVITY = 0
    }
    const url = window.location.href
    if ((event !== 'page_view' && event !== 'view_content') || url.includes('/checkout/') || url.includes('/order/')) {
      window.SP_VISIT_ACTIVITY += 2
    }
    if (!trackingInit || window.SP_VISIT_ACTIVITY < 2) {
      eventQueueList.push(trackData)
      return
    }
    const eventName = TRACKING_EVENT_LIST[event]
    if (platform === TRACKING_PLATFORMS.KLAVIYO) {
      klaviyoTracking({ event: eventName.klaviyo, options, campaignId, trackingInfo, imgUrl: app.$imgUrl, context: app })
      return
    }
    if (platform === TRACKING_PLATFORMS.FACEBOOK) {
      facebookConversionTracking(eventName.facebook, options)
      return
    }
    if (platform === TRACKING_PLATFORMS.PINTEREST) {
      pinterestTracking({ event: eventName.pintrk, options, campaignId })
      return
    }
    if (platform === TRACKING_PLATFORMS.TIKTOK) {
      tiktokTracking({ event: eventName.ttq, options, campaignId })
      return
    }
    if (platform === TRACKING_PLATFORMS.GOOGLE) {
      gtagTracking({ event: eventName.gtag, options, campaignId, trackingInfo })
      return
    }
    if (platform === TRACKING_PLATFORMS.SNAP) {
      snapTracking({ event: eventName.snap, options, campaignId, trackingInfo })
      return
    }
    if (platform === TRACKING_PLATFORMS.QUORA) {
      quoraTracking({ event: eventName.quora, options, campaignId, trackingInfo })
      return
    }
    if (platform === TRACKING_PLATFORMS.BING) {
      bingTracking({ event: eventName.bing, options, campaignId, trackingInfo })
      return
    }
    if (platform === TRACKING_PLATFORMS.REDDIT) {
      redditTracking({ event: eventName.reddit, options })
      return
    }
    if (platform === TRACKING_PLATFORMS.CUSTOM) {
      customTracking({ event: eventName.custom, options, campaignId: campaignId || options?.content_ids })
      return
    }
    if (platform === TRACKING_PLATFORMS.NEW_CUSTOM) {
      newCustomTracking(event, null, null, { campaignId, campaignSlug, ...options })
      return
    }
    if (platform === TRACKING_PLATFORMS.ALL) {
      customTracking({ event: eventName.custom, options, campaignId: campaignId || options?.content_ids })
      newCustomTracking(event, null, null, { campaignId, campaignSlug, ...options })
      facebookConversionTracking(eventName.facebook, options)
      pinterestTracking({ event: eventName.pintrk, options, campaignId })
      tiktokTracking({ event: eventName.ttq, options, campaignId })
      gtagTracking({ event: eventName.gtag, options, campaignId, trackingInfo })
      snapTracking({ event: eventName.snap, options, campaignId, trackingInfo })
      quoraTracking({ event: eventName.quora, options, campaignId, trackingInfo })
      bingTracking({ event: eventName.bing, options, campaignId, trackingInfo })
      klaviyoTracking({ event: eventName.klaviyo, options, campaignId, trackingInfo, imgUrl: app.$imgUrl, context: app })
      redditTracking({ event: eventName.reddit, options })
    }
  }

  function retrackEvent () { // for checking bot
    if (window.SP_VISIT_ACTIVITY < 2) {
      return
    }
    if (eventQueueList.length > 0) {
      eventQueueList.forEach(function (data) {
        trackEvent(data, true)
      })
      eventQueueList = []
    }
  }

  function customTracking ({ event, options, campaignId, action }) {
    if (!event) {
      return
    }
    fetch(API_ENDPOINT, {
      method: 'PUT', // or 'PUT'
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        appId,
        client,
        events: {
          ...trackingInfo,
          pixelsData: options,
          campaignId,
          track: event.toLowerCase(),
          action
        },
        sessionId
      })
    })
  }

  function newCustomTracking (event, action, elementName, data) {
    if (!event) {
      return
    }
    const sessionId = initSessionId()
    event = action || event
    fetch(NEW_API_ENDPOINT, {
      method: 'PUT', // or 'PUT'
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'event',
        sessionId,
        event,
        elementName,
        url: window.top.location.href,
        ...data
      })
    })
  }

  async function facebookConversionTracking (event, options) {
    if (!event) {
      return
    }
    // facebook tracking
    const eventID = `${store.state.storeInfo.name.replaceAll(' ', '_')}.${event}.${Date.now()}`
    if (window.fbq) {
      window.fbq('track', event, options, { eventID })
    }
    const currentTime = Math.round(Date.now() / 1000)
    const facebookConversionsData = [{
      event_id: eventID,
      event_name: event,
      event_time: currentTime,
      custom_data: options,
      user_data: {
        em: await sha256(visitInfo.email ? visitInfo.email.toLowerCase() : ''),
        ct: await sha256(visitInfo.city ? visitInfo.city.toLowerCase() : ''),
        fbc: `fb.1.${currentTime}.${route.query.fbclid || visitInfo.ad_id || ''}`,
        fbp: `fb.1.${currentTime}.${visitInfo.device_id}`,
        country: await sha256(visitInfo.country ? visitInfo.country.toLowerCase() : ''),
        client_ip_address: client.ip,
        client_user_agent: client.useragent
      },
      event_source_url: client.url,
      action_source: 'website'
    }]
    sendFacebookConversions(facebookConversionsData)
  }

  function sendFacebookConversions (data) {
    const API_VERSION = store.state.storeInfo?.tracking_code?.facebook_pixel_version || 'v13.0'
    let pixelIds = store.state.storeInfo?.tracking_code?.facebook_pixel || ''
    pixelIds = pixelIds.split(',')
    let tokens = store.state.storeInfo?.tracking_code?.facebook_conversion_token || ''
    tokens = tokens.split(',')
    pixelIds.forEach((PIXEL_ID, index) => {
      const TOKEN = tokens[index]
      if (API_VERSION && PIXEL_ID && TOKEN) {
        const trackingUrl = `https://graph.facebook.com/${API_VERSION}/${PIXEL_ID}/events?access_token=${TOKEN}`
        $axios.post(trackingUrl, { data })
      }
    })
  }

  // add methods to Nuxt context
  inject('isBot', isBot)
  inject('getVisitInfo', getVisitInfo)
  inject('updateAdsSource', updateAdsSource)
  inject('tracking', tracking)
}

function pinterestTracking ({ event, options, campaignId }) {
  if (!event || !window.pintrk) {
    return
  }
  let data = {}
  switch (event) {
    case 'PageVisit':
      data = {
        product_id: campaignId
      }
      break
    case 'Contact':
      data = options
      break
    case 'AddToCart':
      data = {
        product_id: options.content_ids,
        value: options.value,
        order_quantity: options.num_items,
        currency: options.currency,
        content_name: options.content_name
      }
      break
    case 'Checkout':
    case 'Purchase':
      data = {
        order_id: options.order_token,
        product_id: options.content_ids,
        value: options.value,
        order_quantity: options.num_items,
        currency: 'USD',
        line_items: options.content_ids.map((item, index) => {
          return {
            product_id: options.content_ids[index],
            product_name: options.content_name.split(',')[index],
            product_category: options.content_category.split(',')[index]
          }
        })
      }
      break
  }
  window.pintrk('track', event, data)
}

function tiktokTracking ({ event, options, campaignId }) {
  if (!event || !window.ttq) {
    return
  }
  let data = {}
  switch (event) {
    case 'page_view':
      window.ttq.page()
      return
    case 'ViewContent':
      data = {
        campaignId
      }
      break
    case 'Contact':
      data = options
      break
    case 'AddToCart':
    case 'InitiateCheckout':
    case 'CompletePayment':
      data = {
        content_id: options.content_ids,
        content_type: 'product',
        content_name: options.content_name,
        quantity: options.num_items,
        value: options.value,
        currency: options.currency
      }
      break
  }
  window.ttq.track(event, data)
}

function gtagTracking ({ event, options, campaignId, trackingInfo }) {
  if (!event || !window.gtag) {
    return
  }
  let data = {}
  switch (event) {
    case 'page_view':
      data = {
        event_category: 'Ecommerce'
      }
      return
    case 'view_item':
    case 'add_to_cart':
    case 'begin_checkout':
    case 'purchase':
      data = {
        event_category: 'Ecommerce',
        currency: options.currency,
        value: options.value,
        items: options.content_ids.map((item, index) => {
          return {
            item_id: options.content_ids[index],
            item_name: options.content_name?.split(',')?.[index],
            item_category: options.content_category?.split(',')?.[index],
            price: options.content_value[index],
            currency: options.currency,
            quantity: options.content_quantity[index]
          }
        })
      }

      if (options.order_token) {
        data.order_token = options.order_token
      }
      break
    case 'view_item_list':
    case 'select_item':
      data = {
        event_category: 'Ecommerce',
        items: options.items
      }
      break
  }

  if (trackingInfo && trackingInfo.storeId && trackingInfo.storeId === 1) {
    data.seller_id = trackingInfo.sellerId || 1
  }
  window.gtag('event', event, data)
}

function snapTracking ({ event, options, campaignId, trackingInfo }) {
  if (!event || !window.snaptr) {
    return
  }
  let data = {}
  switch (event) {
    case 'VIEW_CONTENT':
    case 'ADD_CART':
    case 'START_CHECKOUT':
    case 'PURCHASE':
      data = {
        currency: options.currency,
        value: options.value,
        item_id: options.content_ids,
        item_name: options.content_name?.split(','),
        item_category: options.content_category,
        price: options.content_value,
        quantity: options.content_quantity
      }

      if (options.order_token) {
        data.order_token = options.order_token
      }
      break
  }
  window.snaptr('track', event, data)
}

function quoraTracking ({ event, options, campaignId, trackingInfo }) {
  if (!event || !window.qp) {
    return
  }
  let data = {}
  switch (event) {
    case 'ViewContent':
      data = {
        campaignId
      }
      break
    case 'Contact':
      data = options
      break
    case 'AddToCart':
    case 'InitiateCheckout':
    case 'Purchase':
      data = {
        currency: options.currency,
        value: options.value,
        item_id: options.content_ids,
        item_name: options.content_name?.split(','),
        item_category: options.content_category,
        price: options.content_value,
        quantity: options.content_quantity
      }
      break
  }
  window.qp('track', event, data)
}

function bingTracking ({ event, options, campaignId, trackingInfo }) {
  if (!event || typeof window.uetq === 'undefined') {
    return
  }
  window.uetq = window.uetq || []
  let data = {}
  switch (event) {
    case 'page_view':
      data = {
        ecomm_pagetype: correctEcommPageType(event)
      }
      event = ''
      break
    case 'view_item':
    case 'view_content':
      data = {
        ecomm_pagetype: correctEcommPageType(event),
        ecomm_prodid: [campaignId]
      }
      event = ''
      break
    case 'contact':
      data = {
        ...options,
        ecomm_pagetype: correctEcommPageType(event)
      }
      break
    case 'add_to_cart':
    case 'begin_checkout':
    case 'purchase':
      data = {
        currency: options?.currency,
        ecomm_pagetype: correctEcommPageType(event),
        ecomm_totalvalue: options?.value,
        ecomm_prodid: options.content_ids,
        items: options.content_ids.map((item, index) => {
          return {
            id: options.content_ids[index],
            price: options.content_value[index],
            quantity: options.content_quantity[index]
          }
        })
      }
      if (options.order_token) {
        data.order_token = options.order_token
      }
      break
  }
  window.uetq.push('event', event, data)
}

function klaviyoTracking ({ event, options, campaignId, trackingInfo, imgUrl, context }) {
  if (!event || typeof window.klaviyo === 'undefined') {
    return
  }
  const domain = window.location.hostname || ''
  const port = window.location.port ? `:${window.location.port}` : ''
  const baseUrl = `https://${domain}${port}`
  window.klaviyo = window.klaviyo || []
  const userInfo = lscache.get('userInfo') || {}
  if (options?.klaviyoData?.email) {
    const email = options.klaviyoData.email || ''
    const name = options.klaviyoData.name || ''
    if (email && email.length > 0) {
      userInfo.email = email
    }
    if (name && name.length > 0) {
      userInfo.name = name
    }
    lscache.set('userInfo', userInfo)
  }
  if (userInfo && userInfo.email && userInfo.email.length > 0) {
    // if only email is provided, identify with email
    if (userInfo.name && userInfo.name.length > 0) {
      const { firstName, lastName, middleName } = parseFullName(userInfo.name)
      window.klaviyo.push(['identify', { email: userInfo.email, first_name: firstName, last_name: lastName, middle_name: middleName, name: userInfo.name }])
    } else {
      window.klaviyo.push(['identify', { email: userInfo.email }])
    }
  }
  const storeInfo = context.store.state.storeInfo || {}
  const storeName = storeInfo.name || ''
  let data = {
    store_domain: domain,
    store_name: storeName,
  }
  switch (event) {
    case 'Active on Site': {
      data = {
        ...data,
        time: new Date().toISOString()
      }
      break
    }
    case 'Viewed Collection': {
      if (!options?.klaviyoData) {
        return
      }
      const collectionData = options.klaviyoData || {}
      data = {
        ...data,
        ...collectionData
      }
      break
    }
    case 'Submitted Search': {
      if (!options?.klaviyoData) {
        return
      }
      const searchData = options.klaviyoData || {}
      data = {
        ...data,
        ...searchData
      }
      break
    }
    case 'Viewed Product': {
      let imageUrl = ''
      if (options?.klaviyoData?.image_url) {
        imageUrl = imgUrl(options?.klaviyoData?.image_url, 'medium')
      }
      const slug = options?.klaviyoData?.slug || ''
      const productUrl = slug ? `${baseUrl}/${slug}` : ''
      data = {
        ...data,
        ProductName: options?.content_name ?? '',
        ProductID: options?.content_ids?.[0] ?? 0,
        Categories: [options?.content_category] ?? [],
        URL: productUrl,
        Price: options?.content_value?.[0] ?? 0,
        ImageURL: imageUrl,
      }
      break
    }
    case 'Added to Cart': {
      const klaviyoData = options.klaviyoData || {}
      if (!klaviyoData) {
        return
      }
      const addedItemData = klaviyoData.addedItemData || {}
      const addedItemsData = klaviyoData.addedItemsData || []
      if (!addedItemData || !addedItemsData) {
        return
      }
      addedItemData.ImageURL = addedItemData.ImageURL ? imgUrl(addedItemData.ImageURL, 'medium') : ''
      for (const itemData of addedItemsData) {
        itemData.ImageURL = imgUrl(itemData.ImageURL, 'medium')
      }
      data = {
        ...data,
        ...addedItemData,
        Items: addedItemsData,
        time: klaviyoData.time || ''
      }
      break
    }
    case 'Started Checkout': {
      // Only proceed if we have user email
      if (!userInfo?.email) {
        return
      }

      const klaviyoData = options.klaviyoData || {}
      if (!klaviyoData) {
        return
      }
      const orderId = options.order_token || ''
      const accessToken = klaviyoData.accessToken || ''
      if (!accessToken) {
        return
      }
      const checkoutUrl = baseUrl + '/checkout/' + accessToken
      const itemNames = klaviyoData.itemNames || []
      if (!itemNames || itemNames.length === 0) {
        return
      }
      const totalPrice = options.value || 0
      const categories = klaviyoData.categories || []
      const items = klaviyoData.items || []
      if (!items || items.length === 0) {
        return
      }
      for (const item of items) {
        item.ImageURL = imgUrl(item.ImageURL, 'medium')
        item.ProductURL = baseUrl + item.ProductURL
      }
      data = {
        ...data,
        time: klaviyoData.time || '',
        $event_id: orderId,
        $value: totalPrice,
        ItemsNames: itemNames,
        CheckoutURL: checkoutUrl,
        Categories: categories,
        Items: items
      }
      break
    }
    default:
      break
  }
  window.klaviyo.push(['track', event, data])
}

/**
 * Parse full name into first name, last name, and middle name
 * @param {string} name
 * @returns {Object} { firstName, lastName, middleName }
 * @throws {Error} If error occurs
 */
function parseFullName (name) {
  try {
    if (!name) {
      return {
        firstName: '',
        lastName: '',
        middleName: ''
      }
    }
    name = name.replace(/\s+/g, ' ').trim()
    if (name.includes(' ')) {
      const nameParts = name.split(' ')
      if (nameParts.length === 2) {
        return {
          firstName: nameParts[0],
          lastName: nameParts[1]
        }
      }
      if (nameParts.length === 1) {
        return {
          firstName: nameParts[0],
          lastName: ''
        }
      }
      if (nameParts.length > 2) {
        const middleName = nameParts.slice(1, -1).join(' ')
        return {
          firstName: nameParts[0],
          middleName,
          lastName: nameParts.slice(-1)[0]
        }
      }
      return {
        firstName: name,
        lastName: ''
      }
    }
    return {
      firstName: name,
      lastName: '',
      middleName: ''
    }
  } catch (error) {
    console.error('Error parsing full name', error)
    return {
      firstName: '',
      lastName: '',
      middleName: ''
    }
  }
}

function correctEcommPageType (event) {
  switch (event) {
    case 'page_view':
      return 'home'
    case 'view_item':
    case 'view_content':
      return 'product'
    case 'add_to_cart':
      return 'cart'
    case 'purchase':
      return 'purchase'
    default:
      return 'other'
  }
}

function redditTracking ({ event, data }) {
  if (!event || !window.rdt) {
    return
  }
  let rdtData = {}
  switch (event) {
    case 'Contact':
      event = 'Custom'
      rdtData = {
        customEventName: 'Contact',
        ...data,
      }
      break
    case 'InitiateCheckout':
      event = 'Custom'
      rdtData = {
        customEventName: 'InitiateCheckout',
        ...data,
      }
      break
    case 'AddToCart':
    case 'Purchase':
      rdtData = {
        ...data,
        /**
         * force value to match
         * - "order.total_amount" at InitiateCheckout / Purchase
         * - "totalPrice" at Cart
        */
        content_value: data?.value
      }
      break
  }
  if (rdtData.klaviyoData) {
    delete rdtData.klaviyoData
  }
  window.rdt('track', event, rdtData)
}
