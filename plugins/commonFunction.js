/* eslint-disable no-unused-vars */
import lscache from 'lscache'
import { CURRENCY, PRICING_MODE, USD_CODE, VND_CODE } from '~/helpers/variableConst'

export default ({ store, $config, route, req }, inject) => {
  const convertPrice = (value, productCurrencyCode, convertCode) => {
    // convert other currency to default(USD)
    if (!productCurrencyCode) {
      productCurrencyCode = USD_CODE
    } else if (productCurrencyCode !== USD_CODE) {
      const productRate = findCurrencyByCode(productCurrencyCode).rate
      value = parseFloat(value) / productRate
    }

    // display store currency
    let rate = store.state.currency.rate
    let currency = store.state.currency

    if (convertCode) {
      currency = findCurrencyByCode(convertCode)
    } else if (process.browser) {
      currency = lscache.get('currency') || CURRENCY
    }

    rate = currency.rate

    value = parseFloat(value) * rate
    if (convertCode === VND_CODE) {
      value = Math.round(value / 1000) * 1000
    }
    return value
  }

  const formatPriceNoUnit = (value, productCurrencyCode, convertCode, isRaw = false, fix = 2) => {
    // convert other currency to default(USD)
    if (!productCurrencyCode) {
      productCurrencyCode = USD_CODE
    } else if (productCurrencyCode !== USD_CODE) {
      const productRate = findCurrencyByCode(productCurrencyCode).rate
      value = parseFloat(value) / productRate
    }

    // display store currency
    let rate = store.state.currency.rate
    let currency = store.state.currency

    if (convertCode) {
      currency = findCurrencyByCode(convertCode)
    } else if (process.browser) {
      currency = lscache.get('currency') || CURRENCY
    }

    rate = currency.rate

    value = parseFloat(value) * rate
    return formatCurrency(value, fix, isRaw)
  }

  const formatCurrency = (number, fix = 2, isRaw = false) => {
    if (isRaw) {
      return number ? Number(parseFloat(number).toFixed(fix)) : 0
    }
    return number ? Number(parseFloat(number).toFixed(fix)).toLocaleString() : 0
  }

  const formatPrice = (value, productCurrencyCode, convertCode, useInfoFromState = false) => {
    if (typeof value === 'string') {
      value = value.replace(',', '.')
    }
    let code = store.state.currency.code
    let rate = store.state.currency.rate
    if (productCurrencyCode && lscache.get('currency') && productCurrencyCode === lscache.get('currency').code) {
      return toLocalePrice(value, lscache.get('currency'))
    }
    // convert other currency to default(USD)
    if (!productCurrencyCode) {
      productCurrencyCode = USD_CODE
    } else if (productCurrencyCode !== USD_CODE) {
      const productRate = findCurrencyByCode(productCurrencyCode).rate
      value = parseFloat(value) / productRate
    }

    // display store currency
    let currency = store.state.currency
    if (convertCode) {
      currency = findCurrencyByCode(convertCode)
    } else if (process.browser) {
      if (!useInfoFromState) {
        currency = lscache.get('currency') || CURRENCY
      }
    }

    code = currency.code
    rate = currency.rate

    value = parseFloat(value) * rate

    if (code === VND_CODE) {
      value = Math.round(value / 1000) * 1000
    }
    return toLocalePrice(value, currency)
  }

  const toLocalePrice = (value, currency) => {
    if (!currency) {
      currency = lscache.get('currency') || CURRENCY
    }
    let string = value.toLocaleString(currency?.locale, {
      style: 'currency',
      currency: currency?.code
    }).replace(/\s/, '') // remove space in string
    const arrCurrenciesMustHavePrefix = [
      'ARS',
      'AUD',
      'BBD',
      'BSD',
      'CAD',
      'COP',
      'DOP',
      'GYD',
      'HKD',
      'JMD',
      'LRD',
      'MXN',
      'NAD',
      'NIO',
      'NZD',
      'TTD',
      'TWD',
      'XCD'
    ]
    if (arrCurrenciesMustHavePrefix.includes(currency?.code)) {
      string = currency?.code + ' ' + string
    }

    // Check for USD fallback currency case
    const isFallbackCurrency = store?.state?.is_fallback_currency
    if (currency?.code === 'USD' && isFallbackCurrency) {
      string = string.replace('$', 'US$')
    }

    return string
  }

  const formatPriceByRate = (value, rate, code, isGetOnlyNumber = false) => {
    value = parseFloat(value) * (rate || CURRENCY?.rate || 1)

    if (code === VND_CODE) {
      value = Math.round(value / 1000) * 1000
    }

    if (isGetOnlyNumber) {
      return value
    }

    const currency = findCurrencyByCode(code)
    return toLocalePrice(value, currency)
  }

  const findCurrencyByCode = (code) => {
    return store.state.generalInfo.currencies.find(each => each.code === code) ?? CURRENCY // set default if not found
  }

  const shallowEqual = (object1, object2) => {
    const keys1 = Object.keys(object1)
    const keys2 = Object.keys(object2)

    if (keys1.length !== keys2.length) {
      return false
    }

    for (const key of keys1) {
      if (object1[key] !== object2[key]) {
        return false
      }
    }

    return true
  }

  const totalPrice = (list, disableBundle = false) => {
    let total = 0
    if (list) {
      list.forEach((item) => {
        if (item.out_of_stock) {
          return
        }

        const rate = store.state.generalInfo.currencies.find(each => each.code === item.currency_code).rate
        let price = ((item.variantPrice || item.price) + (item?.extra_custom_fee ?? 0)) / rate * item.quantity

        if (!disableBundle && item.campaignBundleId && list.find(product => item.campaignBundleId === product.campaign_id)) {
          price = price * (100 - item.promotion?.discount_percentage) / 100
        }

        total += price
      })
    }

    return total
  }

  const totalBundleDiscount = (list) => {
    let total = 0
    if (list) {
      list.forEach((item) => {
        if (item.out_of_stock || !item.campaignBundleId) {
          return
        }
        const rate = store.state.generalInfo.currencies.find(each => each.code === item.currency_code).rate
        const dynamicBaseIndex = item?.dynamic_base_index ?? 0
        const price = ((item.variantPrice || item.price) + (item?.extra_custom_fee ?? 0) + dynamicBaseIndex) / rate * item.quantity
        let discountPrice = 0
        if (list.find(product => item.campaignBundleId === product.campaign_id)) {
          discountPrice = price * (item.promotion?.discount_percentage) / 100
        }

        total += discountPrice
      })
    }
    return total
  }

  const totalDynamicBaseCostIndex = (list) => {
    if (!store.state.storeInfo.enable_dynamic_base_cost) {
      return 0
    }
    let total = 0
    if (list) {
      list.forEach((item) => {
        if (!item.dynamic_base_index || !item.quantity || item.quantity === 0) {
          return
        }

        const price = formatPriceNoUnit(item.dynamic_base_index, item.currency_code ?? USD_CODE, USD_CODE, false, 10) * item.quantity
        total += price
      })
    }

    return total
  }

  const toSlug = (title) => {
    if (!title) {
      return ''
    }
    let slug = ''
    // Change to lower case
    const titleLower = title.toLowerCase()
    // Letter "e"
    slug = titleLower.replace(/e|é|è|ẽ|ẻ|ẹ|ê|ế|ề|ễ|ể|ệ/gi, 'e')
    // Letter "a"
    slug = slug.replace(/a|á|à|ã|ả|ạ|ă|ắ|ằ|ẵ|ẳ|ặ|â|ấ|ầ|ẫ|ẩ|ậ/gi, 'a')
    // Letter "o"
    slug = slug.replace(/o|ó|ò|õ|ỏ|ọ|ô|ố|ồ|ỗ|ổ|ộ|ơ|ớ|ờ|ỡ|ở|ợ/gi, 'o')
    // Letter "u"
    slug = slug.replace(/u|ú|ù|ũ|ủ|ụ|ư|ứ|ừ|ữ|ử|ự/gi, 'u')
    // Letter "d"
    slug = slug.replace(/đ/gi, 'd')
    // Trim the last whitespace
    slug = slug.replace(/\s*$/g, '')
    // Change whitespace to "-"
    slug = slug.replace(/\s+/g, '-')

    return slug
  }

  const toKey = (name) => {
    name = toSlug(name)
    name = name.replace(/[^a-zA-Z0-9]/g, '')
    return name
  }

  const loadedImgs = []

  const imgUrl = (path, type, format = '', color) => {
    if (!path) { return '' }
    // return static image
    if (path.startsWith('/images/')) { return path }
    if (path.startsWith('http')) {
      if (path.includes('cloudmockups.com')) {
        if (color) {
          path = path.replace(/(c=[\w\d])\w+/g, `c=${color.replace('#', '')}`)
        }
        switch (type) {
          case 'full': {
            break
          }
          case 'banner_hd': {
            path += '&expectedWidth=1920'
            break
          }
          case 'banner_mobile': {
            path += '&expectedWidth=1080'
            break
          }
          case 'share': {
            path += '&expectedWidth=1080'
            break
          }
          case 'collection_banner': {
            path += '&expectedWidth=600'
            break
          }
          case 'full_hd': {
            path += '&expectedWidth=1280'
            break
          }
          case 'list': {
            path += '&expectedWidth=600'
            break
          }
          case 'mobile': {
            path += '&expectedWidth=640'
            break
          }
          case 'list_mb': {
            path += '&expectedWidth=320'
            break
          }
          case 'logo': {
            path += '&expectedWidth=256'
            break
          }
          case 'icon': {
            path += '&expectedWidth=32'
            break
          }
          case 'icon-lg': {
            path += '&expectedWidth=64'
            break
          }
          case 'icon-xl': {
            path += '&expectedWidth=512'
            break
          }
          case 'avatar': {
            path += '&expectedWidth=160'
            break
          }
          case 'product-review-thumb': {
            path += '&expectedWidth=256'
            break
          }
          case 'product-review-image': {
            path += '&expectedWidth=720'
            break
          }
          case 'product-review-video': {
            path += '&expectedWidth=160'
            break
          }
          default: {
            path += '&expectedWidth=160'
          }
        }
      }
      return path
    }

    const key = type + '-' + path + '-' + color
    if (loadedImgs[key]) {
      return loadedImgs[key]
    }

    let imgUrl = $config.baseImageUrl
    if (store.state.storeInfo.is_proxy && $config.appEnv === 'production') {
      imgUrl = 'https://images.' + getHost()
    }
    switch (type) {
      case 'full': {
        imgUrl += '/rx/-'
        break
      }
      case 'banner_hd': {
        imgUrl += '/rx/1920x1080'
        break
      }
      case 'banner_mobile': {
        imgUrl += '/rx/1080x608'
        break
      }
      case 'share': {
        imgUrl += '/rx/1080x1080'
        break
      }
      case 'collection_banner': {
        imgUrl += '/rx/600x600,c_1'
        break
      }
      case 'full_hd': {
        // imgUrl += '/rx/1280x1600'
        imgUrl += '/rx/1000x1250'
        break
      }
      case 'list': {
        imgUrl += '/rx/600x750,c_2'
        break
      }
      case 'mobile': {
        imgUrl += '/rx/640x800,c_1'
        break
      }
      case 'list_mb': {
        imgUrl += '/rx/320x400,c_1'
        break
      }
      case 'logo': {
        imgUrl += '/rx/256x256'
        break
      }
      case 'icon': {
        imgUrl += '/rx/32x32'
        break
      }
      case 'icon-lg': {
        imgUrl += '/rx/64x64'
        break
      }
      case 'icon-xl': {
        imgUrl += '/rx/512x512'
        break
      }
      case 'avatar': {
        imgUrl += '/rx/160x160'
        break
      }
      case 'product-review-thumb': {
        imgUrl += '/rx/256x256,c_1'
        break
      }
      case 'product-review-image': {
        imgUrl += '/rx/720'
        break
      }
      case 'product-review-video': {
        imgUrl += '/rx/-'
        break
      }
      case 'thumb': {
        imgUrl += '/rx/160x200,c_2'
        break
      }
      default: {
        imgUrl += '/rx/160x200,c_1'
      }
    }

    const fm = supportWebp() && format === 'webp' ? ',q_90,ofmt_webp' : ''
    imgUrl += fm

    if (path.includes('cloudinary.com') || path.startsWith('/s')) {
      if (color) {
        path = path.replace(/co_rgb:.{6}/, `co_rgb:${color.replace('#', '')}`)
      }
      path = path.replace(/https:\/\/res\.cloudinary\.com\/\w+\/image\/upload/, '/s1')
      imgUrl += path
    } else {
      imgUrl += '/s2/' + path
    }

    return imgUrl
  }

  // const cdnUrl = (path, type, format = 'jpg', color) => {
  //   if (supportWebp() && format === 'webp') {
  //     path = path.replace('f_jpg', 'f_webp')
  //   }

  //   if (color) {
  //     path = path.replace(/co_rgb:.{6}/, `co_rgb:${color.replace('#', '')}`)
  //   }

  //   let width = 'w_1280'
  //   switch (type) {
  //     case 'full': {
  //       break
  //     }
  //     case 'full_hd': {
  //       width = 'w_1280'
  //       break
  //     }
  //     case 'list' : {
  //       width = 'w_300'
  //       break
  //     }
  //     case 'mobile' : {
  //       width = 'w_384'
  //       break
  //     }
  //     case 'list_mb' : {
  //       width = 'w_164'
  //       break
  //     }
  //     default: {
  //       width = 'w_80'
  //     }
  //   }

  //   path = path.replace('w_1280', width)

  //   return path
  // }

  // const imgix = (path, type, format = 'jpg') => {
  //   const baseImageUrl = $config.baseImageUrl
  //   const fm = supportWebp() ? 'fm=webp' : 'fm=' + format
  //   let imgUrl = ''
  //   switch (type) {
  //     case 'full': {
  //       imgUrl = `${baseImageUrl}/${path}?${fm}`
  //       break
  //     }
  //     case 'full_hd': {
  //       imgUrl = `${baseImageUrl}/${path}?w=1600&h=1600&${fm}`
  //       break
  //     }
  //     case 'list' : {
  //       imgUrl = `${baseImageUrl}/${path}?w=300&h=375&${fm}&fit=crop`
  //       break
  //     }
  //     case 'mobile' : {
  //       imgUrl = `${baseImageUrl}/${path}?w=384&h=480&${fm}&fit=crop`
  //       break
  //     }
  //     case 'list_mb' : {
  //       imgUrl = `${baseImageUrl}/${path}?w=164&h=205&${fm}&fit=crop`
  //       break
  //     }
  //     case 'logo' : {
  //       imgUrl = `${baseImageUrl}/${path}?w=256&h=256&${fm}`
  //       break
  //     }
  //     default: {
  //       imgUrl = `${baseImageUrl}/${path}?w=80&h=100&${fm}&fit=crop`
  //     }
  //   }
  //   return imgUrl
  // }

  let webpSupported

  const supportWebp = () => {
    try {
      if (typeof document === 'undefined') {
        return false
      }

      if (typeof webpSupported !== 'undefined') {
        return webpSupported
      }

      const elem = document.createElement('canvas')

      if (elem.getContext && elem.getContext('2d')) {
        // was able or not to get WebP representation
        webpSupported = elem.toDataURL('image/webp').indexOf('data:image/webp') === 0
      } else {
        // very old browser like IE 8, canvas not supported
        webpSupported = false
      }
      return webpSupported
    } catch (error) {
      return false
    }
  }

  const colorVal = (val) => {
    const colors = store.state.generalInfo.colors
    let color

    // todo: change to filter()
    colors.map((item) => {
      if (val === item.name) {
        color = item.hex_code
      }
    })

    return color
  }

  const preloadImg = async (path, type, color) => {
    const key = type + '-' + path
    let img, url
    // preload webp image if browser support webp
    if (supportWebp()) {
      url = imgUrl(path, type, 'webp', color)
      img = await imgReady(url, key)
    }
    if (!img) {
      url = imgUrl(path, type, null, color)
      await imgReady(url)
    }
    return url
  }

  const imgReady = (url, key) => {
    return new Promise((resolve) => {
      if (!(process && process.browser)) {
        resolve(null)
      }
      if (loadedImgs[key]) {
        return resolve(loadedImgs[key])
      }

      const img = new Image(80, 100)
      img.onload = function () {
        if (key) {
          loadedImgs[key] = url
        }
        resolve(url)
      }
      img.onerror = function () {
        resolve(null)
      }
      img.src = url
    })
  }

  const oldCopyMethod = (text) => {
    const el = document.createElement('textarea')
    el.value = text
    el.setAttribute('readonly', '')
    el.style.position = 'absolute'
    el.style.left = '-9999px'
    document.body.appendChild(el)
    el.select()
    document.execCommand('copy')
    document.body.removeChild(el)
  }

  const copyToClipBoard = (value) => {
    navigator.permissions.query({ name: 'clipboard-write' })
      .then((result) => {
        if (result.state === 'granted' || result.state === 'prompt') {
          navigator.clipboard.writeText(value).then(() => {
            /* Resolved - text copied to clipboard */
          }, () => {
            /* Rejected - clipboard failed */
            oldCopyMethod(value)
          })
          return
        }

        // fallback method
        oldCopyMethod(value)
      })
  }

  function createSEOMeta ({ title = '', description = '', image, keywords, price, currency = 'USD', SKU = '', name = '' }) {
    const storeInfo = store.state.storeInfo
    const host = getHost()
    const url = `https://${host}${route.fullPath || ''}`

    const meta = [
      { hid: 'description', name: 'description', content: description },
      { hid: 'title', name: 'title', content: title },
      // google
      { hid: 'apple-mobile-web-app-title', name: 'apple-mobile-web-app-title', content: title },

      // facebook
      { hid: 'og:title', property: 'og:title', content: title },
      { hid: 'og:description', property: 'og:description', content: description },
      { hid: 'og:url', property: 'og:url', content: url },
      { hid: 'og:type', property: 'og:type', content: 'website' },
      { hid: 'og:site_name', property: 'og:site_name', content: storeInfo.name || '' },

      // twitter
      { hid: 'twitter:card', name: 'twitter:card', content: 'summary_large_image' },
      { hid: 'twitter:site', name: 'twitter:site', content: storeInfo.name || '' },
      { hid: 'twitter:title', name: 'twitter:title', content: title },
      { hid: 'twitter:description', name: 'twitter:description', content: description },
      { hid: 'twitter:creator', name: 'twitter:creator', content: storeInfo.name },
      { hid: 'twitter:url', name: 'twitter:url', content: url }
    ]

    if (storeInfo.store_type === 'google_ads') {
      meta.push(
        { hid: 'description', property: 'description', content: description },
        { hid: 'title', name: 'title', content: title }
      )

      if (keywords) {
        meta.push(
          { hid: 'keywords', property: 'keywords', content: keywords }
        )
      }
    }

    if (image) {
      meta.push(
        { hid: 'og:image:alt', property: 'og:image:alt', content: description },
        { hid: 'og:image', property: 'og:image', content: image },
        { hid: 'twitter:image:src', name: 'twitter:image:src', content: image }
      )
    }

    if (price) {
      meta.push(
        { hid: 'product:price:amount', property: 'product:price:amount', content: price },
        { hid: 'product:brand', property: 'product:brand', content: storeInfo.name || '' },
        { hid: 'product:price:currency', property: 'product:price:currency', content: currency },
        { hid: 'product:id', property: 'product:id', content: SKU },
        { hid: 'product:availability', property: 'product:availability', content: 'in stock' },
        { hid: 'product:retailer_item_id', name: 'product:retailer_item_id', content: SKU },
        { hid: 'product:item_group_id', name: 'product:item_group_id', content: name },
        { hid: 'product:condition', name: 'product:condition', content: 'new' }
      )
    }

    if (storeInfo.tracking_code && storeInfo.tracking_code.google_merchant_verification) {
      meta.push({ hid: 'google-site-verification', name: 'google-site-verification', content: storeInfo.tracking_code.google_merchant_verification })
    }
    if (storeInfo.tracking_code && storeInfo.tracking_code.facebook_meta_tag) {
      meta.push({ hid: 'facebook-domain-verification', name: 'facebook-domain-verification', content: storeInfo.tracking_code.facebook_meta_tag })
    }
    if (storeInfo.tracking_code && storeInfo.tracking_code.pinterest_meta_tag) {
      meta.push({ hid: 'p:domain_verify', name: 'p:domain_verify', content: storeInfo.tracking_code.pinterest_meta_tag })
    }

    return meta
  }

  function getExistVariantList (optionsList) {
    if (typeof optionsList !== 'object') { optionsList = JSON.parse(optionsList) }
    const optionKeys = Object.keys(optionsList)
    let existVariantList = []
    optionKeys.forEach((item) => {
      const oldExistVariantList = [...existVariantList]
      existVariantList = []
      if (oldExistVariantList && oldExistVariantList.length) {
        oldExistVariantList.forEach((oldValue) => {
          optionsList[item].forEach((value) => {
            existVariantList.push(`${oldValue}-${value}`.replaceAll(' ', '_'))
          })
        })
      } else {
        existVariantList = optionsList[item]
      }
    })
    return existVariantList
  }

  function getImageFileFromDom (dom) {
    return new Promise((resolve, reject) => {
      dom.toBlob(function (blob) {
        resolve(blob)
      })
    })
  }

  function checkViewPort (el) {
    let top = el.offsetTop
    const height = el.offsetHeight

    while (el.offsetParent) {
      el = el.offsetParent
      top += el.offsetTop
    }
    return top >= window.pageYOffset && (top + height) <= (window.pageYOffset + window.innerHeight)
  }

  const getHost = () => {
    if (process.browser) {
      return window.top.location.hostname
    }

    return req.headers.host
  }

  function inputFileToBase64 (file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  };

  function removeEmoji (text) {
    return text.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '')
  }

  async function findAsync (arr, asyncCallback) {
    const promises = arr.map(asyncCallback)
    const results = await Promise.all(promises)
    const index = results.findIndex(result => result)
    return arr[index]
  }

  /*
  * Kiểm tra xem có hiển thị thông báo ship late hay không
  * Hiển thị từ ngày 9/12 đến hết 25/12 hàng năm
  */
  function isShowWarningShipLate () {
    const currentDate = new Date()
    const start = new Date(currentDate.getFullYear(), 11, 9)
    const end = new Date(currentDate.getFullYear(), 11, 26)

    start.setHours(0, 0, 0, 0)
    end.setHours(0, 0, 0, 0)

    return currentDate >= start && currentDate <= end
  }

  function getBaseCostOfTemplateDependOnUserCountry (countryCodeGetting, generalCountries, templateBaseCosts) {
    if (countryCodeGetting === null || generalCountries.length === 0 || templateBaseCosts.length === 0) {
      return null
    }

    let baseCostOfTemplate = templateBaseCosts.find((variant) => {
      return variant.location_code === countryCodeGetting
    })

    if (baseCostOfTemplate) {
      return baseCostOfTemplate
    }

    const generalCountryInfo = generalCountries.find((country) => {
      return country.code === countryCodeGetting
    })

    if (generalCountryInfo === undefined) {
      return null
    }
    baseCostOfTemplate = templateBaseCosts.find((variant) => {
      return variant.location_code === generalCountryInfo.region_code
    })

    if (baseCostOfTemplate) {
      return baseCostOfTemplate
    }

    baseCostOfTemplate = templateBaseCosts.find((variant) => {
      return variant.location_code === '*'
    })
    if (baseCostOfTemplate) {
      return baseCostOfTemplate
    }

    return null
  }

  function roundToHalf (number) {
    const response = Math.ceil(Math.abs(number) * 2) / 2
    if (number < 0) {
      return -1 * response
    }
    return response
  }

  function getDynamicBaseCostIndex ({ currentCampaign, currentOption, optionList, variants, variantsCurrency = USD_CODE, currentProduct = null }) {
    autoSelectVariant(currentProduct, currentOption)
    if (store.state.storeInfo && !store.state.storeInfo.enable_dynamic_base_cost) {
      return 0
    }
    let response = 0
    const campInfo = currentCampaign
    try {
      const userLocation = this.$userCountryForPricing()
      const systemLocations = store.state.generalInfo.countries
      const campaignMarketLocation = campInfo.market_location
      const pricingMode = campInfo.pricing_mode
      if (pricingMode && pricingMode !== PRICING_MODE.ADJUST_PRICE) {
        return 0
      }
      if (campInfo && variants) {
        // const variantKeyForCampLocation = Object.keys(optionList).map(option => optionList[option].length === 1 ? optionList[option][0] : currentOption[option]).toString().replace(/(-|\s)/g, '_').replace(/,/g, '-')
        const variantKeyForCampLocation = `${currentOption.color.toString().replace(/_/g, '').replace(/(-|\s)/g, '_').replace(/,/g, '-')}-${optionList.size.length > 0 ? optionList.size[0].toString().replace(/_/g, '').replace(/(-|\s)/g, '_').replace(/,/g, '-') : 's'}`
        const variantsSelectedForCampLocation = variants.filter((variant) => {
          return variant.variant_key === variantKeyForCampLocation
        })

        const variantKeyForUserLocation = variantKeyForCampLocation
        const variantsSelected = variants.filter((variant) => {
          return variant.variant_key === variantKeyForUserLocation
        })

        const baseCostOnUserLocation = this.$getBaseCostOfTemplateDependOnUserCountry(userLocation, systemLocations, variantsSelected)
        const baseCostOnCampLocation = this.$getBaseCostOfTemplateDependOnUserCountry(campaignMarketLocation, systemLocations, variantsSelectedForCampLocation)
        if (baseCostOnUserLocation === null || baseCostOnCampLocation === null || !baseCostOnUserLocation.base_cost || !baseCostOnCampLocation.base_cost) {
          return 0
        }
        response = this.$roundToHalf(baseCostOnUserLocation.base_cost - baseCostOnCampLocation.base_cost)
        response = roundToHalf(Number(formatPriceNoUnit(response, USD_CODE, variantsCurrency).toString().replace(/,/g, '.')))
      }
    } catch (e) {
    }

    return response
  }

  function getPreferredCountryText () {
    const userInfo = lscache.get('userInfo')
    const userCountry = userInfo?.country || ''
    if (userCountry !== '') {
      const countryName = (new Intl.DisplayNames(['en'], { type: 'region' })).of(userCountry)
      const userCity = userInfo?.city || ''
      if (userCity !== '') {
        return `${userCity}, ${countryName}`
      }
      return countryName
    }

    return null
  }

  function userCountryForPricing () {
    const userCountry = lscache.get('userInfo') && lscache.get('userInfo').country !== '' ? lscache.get('userInfo').country : store.state.userCountry
    return userCountry
  }

  function autoSelectVariant (currentProduct, currentOption) {
    if (!currentProduct) {
      return currentOption
    }

    if (currentOption.size && currentOption.size !== '') {
      return currentOption
    }

    const currentProductSize = JSON.parse(currentProduct.options).size
    if (currentProductSize?.length) {
      currentOption.size = currentProductSize[0]
    }
    return currentOption
  }

  function headTagFilter (objects, path) {
    if (!objects || objects.length === 0) {
      return []
    }

    let baseName = path || route.path
    const locale = route.path.split('/')[1]
    if (locale.length === 2) {
      baseName = baseName.replace(`/${locale}`, '')
    }

    return objects.filter((obj) => {
      if (!obj.path) {
        return true
      } else {
        const paths = obj.path.split(',').map(p => p.trim())
        return paths.some(path => path === baseName)
      }
    })
  }

  inject('supportWebp', supportWebp)
  inject('imgUrl', imgUrl)
  inject('shallowEqual', shallowEqual)
  inject('convertPrice', convertPrice)
  inject('formatPriceByRate', formatPriceByRate)
  inject('formatPrice', formatPrice)
  inject('formatPriceNoUnit', formatPriceNoUnit)
  inject('findCurrencyByCode', findCurrencyByCode)
  inject('totalPrice', totalPrice)
  inject('toLocalePrice', toLocalePrice)
  inject('colorVal', colorVal)
  inject('toSlug', toSlug)
  inject('toKey', toKey)
  inject('preloadImg', preloadImg)
  inject('copyToClipBoard', copyToClipBoard)
  inject('createSEOMeta', createSEOMeta)
  inject('getExistVariantList', getExistVariantList)
  inject('getImageFileFromDom', getImageFileFromDom)
  inject('checkViewPort', checkViewPort)
  inject('inputFileToBase64', inputFileToBase64)
  inject('removeEmoji', removeEmoji)
  inject('lscache', lscache)
  inject('findAsync', findAsync)
  inject('getHost', getHost)
  inject('isShowWarningShipLate', isShowWarningShipLate)
  inject('getBaseCostOfTemplateDependOnUserCountry', getBaseCostOfTemplateDependOnUserCountry)
  inject('roundToHalf', roundToHalf)
  inject('getDynamicBaseCostIndex', getDynamicBaseCostIndex)
  inject('totalDynamicBaseCostIndex', totalDynamicBaseCostIndex)
  inject('getPreferredCountryText', getPreferredCountryText)
  inject('userCountryForPricing', userCountryForPricing)
  inject('autoSelectVariant', autoSelectVariant)
  inject('totalBundleDiscount', totalBundleDiscount)
  inject('headTagFilter', headTagFilter)
}
