import lscache from 'lscache'

export default ({ app, store }) => {
  app.router.afterEach(() => {
    reloadCurrencySelected()
    reloadProductVariantsSize()
    clearExpiredProductVariants()
  })

  function clearExpiredProductVariants () {
    const productIds = Object.keys(lscache.get('product-variants') || {})
    const now = Date.now()
    productIds.forEach((productId) => {
      const timestamp = lscache.get(`${productId}-saved-at`)
      const removedProductVariants = lscache.get('product-variants')
      if (timestamp && (now - timestamp > 30 * 60 * 1000) && removedProductVariants[productId]) {
        delete removedProductVariants[productId]
        lscache.set('product-variants', removedProductVariants)
        lscache.remove(`${productId}-saved-at`)
      }
    })
  }

  function reloadCurrencySelected () {
    if (!lscache.get('currency')) { return }

    let currencySelected = lscache.get('currency')
    if (currencySelected === 'undefined' || currencySelected === undefined) {
      currencySelected = store.state.currency
    }

    const currency = store.state.generalInfo.currencies.find(item => item.code === currencySelected.code)
    lscache.set('currency', currency)
  }

  function reloadProductVariantsSize () {
    if (!lscache.get('product-variants')) { return }

    if (Object.keys(lscache.get('product-variants')).length >= 10) {
      lscache.set('product-variants', null)
    }
  }
}
