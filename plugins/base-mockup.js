import { fabric } from 'fabric'
let enableToughScroll = false

export default (context, inject) => {
  class BaseMockup extends fabric.Canvas {
    setMockup (imgUrl) {
      const _this = this
      return new Promise((resolve, reject) => {
        this._loadImg(imgUrl).then(function (mockup) {
          _this.setBackgroundImage(mockup)
          let zoom = _this.width / mockup.width
          if (mockup.height * zoom > _this.height) {
            zoom = _this.height / mockup.height
          }
          _this.setZoom(zoom)
          _this.renderAll()
          resolve(mockup)
        })
      })
    }

    setMockupOverlay (overlay = true) {
      this.mockupOverlay = overlay
    }

    addPrintSpace (width, height) {
      const oldPrintSpace = this.getObjectByName('printSpace')
      if (oldPrintSpace) {
        this.remove(oldPrintSpace)
      }

      const zoom = this.getZoom()
      const mockupWidth = this.width / zoom
      const mockupHeight = this.height / zoom
      const newWidth = mockupWidth * 0.5
      const newHeight = newWidth * height / width
      this.strokeWidth = 2 / zoom
      this.strokeColor = '#4e5bf2'
      this.printSpace = new fabric.Rect({
        name: 'printSpace',
        width: newWidth,
        height: newHeight,
        fill: '',
        top: (mockupHeight - newHeight) / 2,
        left: (mockupWidth - newWidth) / 2,
        stroke: this.strokeColor,
        strokeWidth: this.strokeWidth
      })
      this.printSpace.setControlsVisibility({
        mt: false,
        mb: false,
        ml: false,
        mr: false
      })

      this.add(this.printSpace)
      this.renderAll()
    }

    viewDesign (canvas) {
      if (!canvas) { return }

      const printSpace = this.getObjectByName('printSpace')
      const newDesign = new fabric.Image(canvas)
      if (this.locked) {
        newDesign.set('selectable', !this.locked)
        newDesign.defaultCursor = 'default'
      }
      if (printSpace) {
        printSpace.set({ stroke: '' })
        newDesign.scaleToWidth(printSpace.getScaledWidth())
        if (newDesign.height * newDesign.scaleY > printSpace.scaleY * printSpace.height) {
          newDesign.scaleToHeight(printSpace.getScaledHeight())
        }
        newDesign.set({
          name: 'design',
          top: printSpace.top + (printSpace.getScaledHeight() - newDesign.getScaledHeight()) / 2,
          left: printSpace.left + (printSpace.getScaledWidth() - newDesign.getScaledWidth()) / 2,
          angle: printSpace.angle
        })
      } else {
        newDesign.scaleToWidth(this.width)
        if (newDesign.getScaledHeight() > this.height) {
          newDesign.scaleToHeight(this.height)
        }
        newDesign.set({
          name: 'design',
          top: (this.height - newDesign.getScaledHeight()) / 2,
          left: (this.width - newDesign.getScaledWidth()) / 2
        })
      }

      if (this.mockupOverlay) {
        newDesign.set({
          globalCompositeOperation: 'destination-over'
        })
      }

      const oldDesign = this.getObjectByName('design')
      this.remove(oldDesign)
      this.add(newDesign)

      const mockupColor = this.getObjectByName('mockupColor')
      if (mockupColor) {
        this.bringToFront(mockupColor)
      }

      this.renderAll()
    }

    setMockupColor (hex) {
      let mockupColor = this.getObjectByName('mockupColor')
      if (!mockupColor) {
        mockupColor = new fabric.Rect({
          name: 'mockupColor',
          width: this.width / this.getZoom(),
          height: this.height / this.getZoom(),
          fill: hex,
          globalCompositeOperation: 'destination-over'
        })
        if (this.locked) {
          mockupColor.set('selectable', !this.locked)
          mockupColor.defaultCursor = 'default'
        }
        this.add(mockupColor)
      } else {
        mockupColor.set({ fill: hex })
      }
      this.renderAll()
    }

    getObjectByName (name) {
      return this.getObjects().find(obj => obj.name === name)
    }

    loadFromJSON (json) {
      const _this = this
      return new Promise((resolve, reject) => {
        super.loadFromJSON(json, function () {
          const mockup = _this.backgroundImage
          if (mockup) {
            const img = mockup.getElement()
            const width = mockup.width * mockup.scaleX
            const height = mockup.height * mockup.scaleY
            mockup.set({
              width: img.naturalWidth,
              height: img.naturalHeight,
              scaleX: width / img.naturalWidth,
              scaleY: height / img.naturalHeight
            })

            let zoom = _this.width / width
            if (height * zoom > _this.height) {
              zoom = _this.height / height
            }
            _this.setZoom(zoom)
          }
          if (_this.locked) {
            _this.lock()
          }
          _this.renderAll.bind(_this)
          resolve(this)
        })
      })
    }

    setDesignGuide (imgUrl) {
      if (this.designGuide) {
        this.remove(this.designGuide)
      }
      if (!imgUrl) {
        this.renderAll()
        return
      }

      const _this = this
      return new Promise((resolve, reject) => {
        this._loadImg(imgUrl).then(function (img) {
          _this.designGuide = img
          img.scaleToWidth(_this.width / _this.getZoom())
          _this.add(img)
          _this.sendToBack(img)
          _this.renderAll()
          resolve(img)
        })
      })
    }

    lock (locked = true) {
      this.locked = locked
      this.selection = !this.locked
      // this.allowTouchScrolling = locked
      this.defaultCursor = 'default'
      const printSpace = this.getObjectByName('printSpace')
      if (printSpace) {
        printSpace.set({ stroke: '' })
      }
      this.getObjects().map((obj) => {
        obj.set('selectable', !this.locked)
        obj.defaultCursor = 'default'
      })

      const _this = this
      this.on('mouse:over', function (event) {
        if (event.target != null) {
          event.target.hoverCursor = _this.defaultCursor
        }
      })
      // this.enableToughScroll()
    }

    enableToughScroll () {
      if (enableToughScroll) { return }
      enableToughScroll = true
      fabric.util.object.extend(fabric.Canvas.prototype, {
        _onTouchStart (e) { }
      })
    }

    toJSON () {
      this.setDesignGuide(null)
      return super.toJSON(['name', 'mockupOverlay'])
    }

    toBlob () {
      return new Promise((resolve, reject) => {
        this.getElement().toBlob(function (blob) {
          resolve(blob)
        })
      })
    }

    _loadImg (fileUrl) {
      if (!fileUrl || typeof fabric === 'undefined') { return }
      return new Promise((resolve, reject) => {
        fabric.Image.fromURL(fileUrl,
          function (img) {
            resolve(img)
          }, { crossOrigin: 'Anonymous' })
      })
    }
  }

  inject('BaseMockup', BaseMockup)
}
