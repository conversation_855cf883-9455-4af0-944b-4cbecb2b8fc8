import * as THREE from 'three'
import { Vector2 } from 'three'
import G<PERSON><PERSON>oader from 'three-gltf-loader'
import { fabric } from 'fabric'
import { MockupRender, MockupLoader } from './mockup.js'
global.THREE = THREE

// import * as THREE from './three.module.js'
// import { GLTFLoader } from './GLTFLoader.js'
// import { OrbitControls } from './OrbitControls.js'
// import { MockupRender, MockupLoader } from './mockup.js'

/*
MockupManager
- constructor( containerID ): init mockup manager with display container
- loadMockup( params ): load mockup render with params
- getMockup( id ): get mockup render by id
- getActiveMockup(): get active mockup render
- setActiveMockup(id): set active mockup render by id
*/

/*
MockupRender
- constructor( mockup ): init mockup render with mockup object return from MockupLoader
- render(): render mockup
- changeDesign( img_url ): change mockup design with img URL
- changeColor( color_hex ): change mockup color
- updateDesignCanvas( canvas ): update design canvas when seller update print design
- addShortcutCanvas( containerID, width ): create a shortcut canvas that display the mockup
- designPNG(): export 3D design PNG
- mockupPNG(): export mockup PNG
- viewMockup(): view mockup image in new window
*/

/*
MockupLoader
- loadMockup( params ): return mockup resource from params
mockupParams = {
  id : 'shirt1', //option to manage multiple mockups via MockupManager
  canvasID : 'mockupCanvas', //canvas ID to render mockup if load 1 mockup
  maxSize : 700, //max display size
  background : './shirt2/bg.jpg', //main mockup image
  colorImg : './shirt2/color.png', //option to change mockup color
  cropImg : './shirt2/crop.png', //option if need to crop design to fit mockup
  shadowImg : './shirt2/shadow.png', //option to add shadow layer on top of design
  glb : './shirt2/print.glb', //required to render 3D design
  designImg : designImg, //required if render 3D with glb
  design3Img : './png/design3D.png', //use to render mockup without THREE on server
  printWidth : 2000,
  printHeight : 2500,
  color : 'yellow'
}
*/

let globalRenderer

export class TMockupRender extends MockupRender {
  constructor (mockup) {
    super(mockup)

    if (!mockup || !mockup.background) {
      return
    }

    this.exported = false
    this.hasDesign = false
    this.mockup = mockup
    this.mockupType = mockup.mockupType

    if (mockup.glb) {
      const threeContainer = document.getElementById('three')

      if (typeof globalRenderer === 'undefined') {
        globalRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true, preserveDrawingBuffer: true }) // alpha: true
      }

      const renderer = this.renderer = globalRenderer
      // renderer.setPixelRatio( window.devicePixelRatio )
      // renderer.setSize( window.innerWidth, window.innerHeight )
      renderer.setClearColor(0xCCCCCC, 0)
      if (threeContainer) {
        threeContainer.appendChild(renderer.domElement)
      }

      const fov = 75
      const aspect = window.innerWidth / window.innerHeight
      const near = 0.1
      const far = 25000
      const camera = this.camera = new THREE.PerspectiveCamera(fov, aspect, near, far)
      camera.position.z = 1000
      camera.lookAt(0, 0, 0)

      // const controls = this.controls = new OrbitControls( camera, renderer.domElement )
      // controls.minDistance = 0.1
      // controls.maxDistance = 25000
      // controls.target.set(0,0,0)
      // controls.update()

      const scene = this.scene = new THREE.Scene()
      scene.background = new THREE.Color(0xCCCCCC)
      scene.add(new THREE.AmbientLight(0xFFFFFF, 1.0))
    }
  }

  _loadObj (width = 600, background = false) {
    const parent = this
    const mockup = this.mockup

    if (!mockup || !mockup.glb) {
      return
    }

    const camera = this.camera
    const scene = this.scene
    const renderer = this.renderer

    // load background
    const texture = this.bgTexture = mockup.texture
    const aspect = texture.image.width / texture.image.height
    const height = width / aspect

    // this._initCamera2(width,height)

    renderer.setSize(width, height)
    camera.aspect = width / height
    camera.updateProjectionMatrix()

    scene.background = background ? texture : null

    // load print layer
    const printScene = mockup.glb.scene
    printScene.traverse(function (child) {
      if (child.isMesh) {
        if (child.name === 'background') {
          parent.bgLayer = child
          parent._initOgCamera(child)
          // console.log(child)
          parent._fitCameraToBgImage(child)
        } else if (child.name === mockup.name) {
          parent.print = child
        } else {
          return false
          // parent.print = child
          // parent._initCamera(child)
          // parent._moveCameraToObject(child)
          // parent._fitCameraToObject(child, 1)
        }
      }
    })

    // remove background layer
    if (this.bgLayer) {
      printScene.remove(this.bgLayer)
    }

    scene.add(printScene)
    // scene.add( this.ocamera )

    // apply design
    if (this.print && mockup.designImg) {
      const designCanvas = this._correctDesignSize(mockup.designImg, mockup.printWidth, mockup.printHeight)
      const designTexture = new THREE.CanvasTexture(designCanvas)
      designTexture.flipY = false
      const material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
      this.print.material = this.material = material
      this.print.material.needsUpdate = true

      this.render3D()
      return this._glbCanvas()
    }

    return false
  }

  render3D () {
    if (this.renderer) {
      this.renderer.render(this.scene, this.ocamera)
    }
  }

  changeDesign (png = null) {
    const mockup = this.mockup
    return new Promise((resolve, reject) => {
      const parent = this
      const print = this.print
      parent.hasDesign = true
      // load custom design
      if (!png) {
        png = './png/' + (Math.floor(Math.random() * 5) + 1) + '.png'
      }

      parent._loadImg(png).then(function (img) {
        const designCanvas = parent._correctDesignSize(img, mockup.printWidth, mockup.printHeight)

        // png = './png/22.png'
        const designTexture = new THREE.CanvasTexture(designCanvas)
        designTexture.flipY = false

        if (!parent.material) {
          parent.print.material = parent.material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
        } else {
          print.material.map = designTexture
        }

        print.material.needsUpdate = true
        parent.render3D()

        const newDesign = new fabric.Image(parent._glbCanvas())
        parent._changeCanvasDesign(newDesign)

        resolve(parent)
      })
    })
  }

  updateDesignCanvas (canvas, hasDesign = true) {
    try {
      if (!this.print) { return }

      this.designCanvas = canvas
      const designTexture = new THREE.CanvasTexture(canvas)
      designTexture.flipY = false

      if (!this.material) {
        this.material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
        this.print.material = this.material
      } else {
        this.print.material.map = designTexture
      }

      this.print.material.needsUpdate = true
      this.render3D()

      const newDesign = new fabric.Image(this._glbCanvas())
      this._changeCanvasDesign(newDesign)

      if (this.shortcut) {
        this.shortcut.renderAll()
      }
      this.hasDesign = hasDesign
      this.exported = false
    } catch (e) {
    }
  }

  showWireframe (show) {
    const print = this.print
    if (!print) { return }
    if (show) {
      if (!this.wireframe) {
        // Setup our wireframe
        const wireframeGeometry = new THREE.WireframeGeometry(print.geometry)
        const wireframeMaterial = new THREE.LineBasicMaterial({ color: 0xFF0000 })
        this.wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial)
      }
      print.add(this.wireframe)
    } else {
      print.remove(this.wireframe)
    }
    this.render3D()
  }

  showBgImage (show) {
    const scene = this.scene
    if (show) {
      scene.background = this.bgTexture
    } else {
      scene.background = null
    }
    this.render3D()
  }

  _initCamera2 (width, height) {
    const camera = new THREE.OrthographicCamera(-2, 2, 2.5, -2.5, 1, 2000)
    camera.position.z = 1000
    this.ocamera = camera
  }

  _initOgCamera (object) {
    const geometry = object.geometry
    const size = geometry.boundingBox.getSize(new THREE.Vector3())

    const camera = new THREE.OrthographicCamera(size.x / -2, size.x / 2, size.y / 2, size.y / -2, 0.1, 2000)
    camera.position.z = 1000
    this.ocamera = camera
  }

  _moveCameraToObject (object) {
    const middle = new THREE.Vector3()
    const geometry = object.geometry
    const camera = this.camera

    geometry.computeBoundingBox()

    middle.x = (geometry.boundingBox.max.x + geometry.boundingBox.min.x) / 2
    middle.y = (geometry.boundingBox.max.y + geometry.boundingBox.min.y) / 2
    middle.z = (geometry.boundingBox.max.z + geometry.boundingBox.min.z) / 2

    camera.position.x = middle.x
    camera.position.y = middle.y
    camera.position.z = 1000

    camera.lookAt(middle.x, middle.y, middle.z)
    this.controls.target.set(middle.x, middle.y, middle.z)
  }

  _fitCameraToObject (object, offset) {
    const camera = this.camera
    offset = offset || 1.5

    const boundingBox = new THREE.Box3()

    boundingBox.setFromObject(object)

    const center = boundingBox.getCenter(new THREE.Vector3())
    const size = boundingBox.getSize(new THREE.Vector3())

    const startDistance = center.distanceTo(camera.position)
    // here we must check if the screen is horizontal or vertical, because camera.fov is
    // based on the vertical direction.
    const endDistance = camera.aspect > 1
      ? ((size.y / 2) / offset) / Math.abs(Math.tan(camera.fov / 2))
      : ((size.y / 2) / offset) / Math.abs(Math.tan(camera.fov / 2)) / camera.aspect

    this.camera.position.z = camera.position.z * endDistance / startDistance
    this.controls.target.set(center.x, center.y, center.z)
  }

  _fitCameraToBgImage (bgImage) {
    const boundingBox = new THREE.Box3()

    boundingBox.setFromObject(bgImage)

    const center = boundingBox.getCenter(new THREE.Vector3())
    const size = boundingBox.getSize(new THREE.Vector3())

    const dist = Math.max(size.x, size.y, size.z)

    const camera = this.camera
    camera.position.set(
      center.x,
      center.y,
      dist
    )

    camera.lookAt(center)
    camera.fov = 2 * Math.atan(size.y / (2 * dist)) * (180 / Math.PI)
    camera.updateProjectionMatrix()
    // this.controls.target.set(center.x,center.y,center.z)
  }

  _glbCanvas () {
    return this.renderer.domElement
  }

  designPNG () {
    this.exported = true
    this.render3D()
    return this.renderer.domElement.toDataURL('image/png')
  }

  designBlob (callback, printSize = 1500) {
    if (!this.hasDesign) { return callback(this.hasDesign) }
    const originSize = this.renderer.getSize(new Vector2())
    const width = printSize
    const height = printSize * 1.25
    this.renderer.setSize(width, height)
    this.render3D()
    this.renderer.domElement.toBlob(callback, 'image/png', 100)
    this.renderer.setSize(originSize.width, originSize.height)
  }

  addShortcutCanvas (containerID, width) {
    if (this.shortcut) { return this.shortcut }

    const container = document.getElementById(containerID)
    const divEl = document.createElement('div')
    divEl.style.float = 'left'
    const canvasEl = document.createElement('canvas')
    divEl.appendChild(canvasEl)
    container.appendChild(divEl)
    const canvas = new fabric.Canvas(canvasEl)
    if (!width) { width = this.width }
    const height = this.height * width / this.width

    canvas.setWidth(width)
    canvas.setHeight(height)

    const img = new fabric.Image(this.canvasEl)

    img.scaleToWidth(width)
    canvas.add(img)

    // disable selection
    canvas.selection = false
    canvas.forEachObject(function (o) {
      o.selectable = false
      o.hoverCursor = 'default'
    })

    canvas.renderAll()

    this.shortcut = canvas
    return canvas
  }

  viewDesign () {
    const w = window.open('', '')
    w.document.title = 'Screenshot'
    const img = new Image()
    img.onload = function () {
      w.document.body.appendChild(img)
    }
    img.src = this.designPNG()
  }

  show () {
    if (this.canvasEl) {
      this.canvasEl.parentElement.style.display = 'block'
    }
  }

  hide () {
    if (this.canvasEl) {
      this.canvasEl.parentElement.style.display = 'none'
    }
  }
}

export class TMockupLoader extends MockupLoader {
  constructor () {
    super()
    this.gltfLoader = new GLTFLoader()
    this.gltfLoader.setCrossOrigin('anonymous')
    THREE.ImageUtils.crossOrigin = ''
  }

  _loadObj (fileUrl) {
    if (!fileUrl) { return }
    const parent = this
    return new Promise((resolve, reject) => {
      this.gltfLoader.load(fileUrl, function (gltf) {
        parent.gltf = gltf
        resolve(gltf)
      })
    })
  }

  _loadTexture (fileUrl) {
    if (!fileUrl) { return }
    return new Promise((resolve, reject) => {
      const textureLoader = new THREE.TextureLoader()
      textureLoader.setCrossOrigin('anonymous')
      textureLoader.load(fileUrl, function (texture) {
        resolve(texture)
      })
    })
  }
}

export default (context, inject) => {
  class MockupManager {
    constructor (containerID) {
      this.container = containerID ? document.getElementById(containerID) : document.createElement('div')
      this.mockupLoader = new TMockupLoader()
      this.mockups = {}
    }

    async loadMockup (params) {
      if (this.mockups[params.id]) {
        return this.mockups[params.id]
      }

      const parent = this

      const canvasEl = document.createElement('canvas')

      // set id to canvasEl
      canvasEl.id = `design_${params.id}`

      this.container.appendChild(canvasEl)
      params.canvasEl = canvasEl

      const mockup = await parent.mockupLoader.loadMockup(params)

      const mockupRender = new TMockupRender(mockup)
      await mockupRender.render()
      parent.mockups[params.id] = mockupRender

      if (!parent.activeMockup) {
        parent.activeMockup = mockupRender
      } else {
        mockupRender.hide()
      }

      this.getActiveMockup()
      return mockupRender
    }

    getMockup (id) {
      if (this.mockups[id]) { return this.mockups[id] }
      return false
    }

    getActiveMockup () {
      return this.activeMockup
    }

    setActiveMockup (id) {
      if (!id) { id = this.activeMockupId }
      if (!id) { return false }

      if (this.mockups[id]) {
        if (this.activeMockup !== this.mockups[id]) {
          this.activeMockup.hide()
          this.activeMockup = this.mockups[id]
          this.activeMockup.show()
          return this.activeMockup
        }
        this.activeMockup.show()
      }
      this.activeMockupId = id
      return false
    }

    setInactiveMockup () {
      return this.activeMockup.hide()
    }

    hideActiveMockup () {
      if (this.activeMockup) {
        this.activeMockup.hide()
      }
    }

    isAllExported (mockupType = null) {
      const mockups = Object.values(this.mockups)
      let isAllExported = true
      mockups.map((mockup) => {
        if (mockup.hasDesign && !mockup.exported && (mockupType === mockup.mockupType || !mockup.mockupType)) {
          isAllExported = false
        }
      })
      return isAllExported
    }

    totalMockupsToExport (mockupType = null) {
      const mockups = Object.values(this.mockups)
      let total = 0
      mockups.map((mockup) => {
        if (mockup.hasDesign && !mockup.exported && (mockupType === mockup.mockupType || !mockup.mockupType)) {
          total++
        }
      })
      return total
    }

    toDataURL (format = 'png') {
      if (this.activeMockup && this.activeMockup.mockupPNG) {
        return this.activeMockup.mockupPNG()
      }
      return null
    }

    getElement () {
      if (this.activeMockup && this.activeMockup.renderer) {
        return this.activeMockup.renderer.domElement
      }
      return null
    }
  }

  inject('MockupManager', MockupManager)
}
