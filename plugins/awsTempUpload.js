import SparkMD5 from 'spark-md5'
import axios from 'axios'
import { v4 as uuidv4 } from 'uuid'
import lscache from 'lscache'
/**
 * @returns string
 */
const getSessionID = () => {
  let sessionID
  if (lscache.supported()) {
    sessionID = lscache.get('senprints-session-id')
    if (!sessionID) {
      sessionID = uuidv4()
    }
  } else {
    sessionID = uuidv4()
  }
  return sessionID
}
const sessionID = getSessionID()
export default (ctx, inject) => {
  /**
   * @param fileName
   * @returns {{ext: *, name: *}}
   */
  const parseFileInfo = (fileName) => {
    const els = fileName.split('.')
    return {
      name: els.slice(0, -1).join('.'),
      ext: els.pop()
    }
  }

  /**
   * @param file
   * @returns {Promise<*|null>}
   */
  const createSignedURL = async (file) => {
    const token = await geCSRFToken()
    const fileInfo = parseFileInfo(file.name)
    const currentTime = new Date()
    const fileDate = currentTime.getFullYear() + '|' + (currentTime.getMonth() + 1) + '|' + currentTime.getDate() + '|' + currentTime.getTime()
    const fileName = SparkMD5.hash(fileInfo.name + '|' + fileDate) + '.' + fileInfo.ext
    const response = await ctx.$axios.$post('/public/pre-signed-url', { fileName }, {
      headers: {
        'x-session-id': sessionID,
        'x-csrf-token': token
      }
    })
    const { success, data } = response
    if (success) {
      return data
    }
    return null
  }

  /**
   * @returns {Promise<*|null>}
   */
  const geCSRFToken = async () => {
    const result = await ctx.$axios.$post('/public/csrf-token', null, {
      headers: {
        'x-session-id': sessionID
      }
    })
    if (result && result.success && result.data) {
      return result.data && result.data.token
    }
    return null
  }

  /**
   * @param file
   * @returns {Promise<{Key}|boolean>}
   */
  const uploaderPreSigned = async (file) => {
    const preSignedResult = await createSignedURL(file)
    if (!preSignedResult) {
      return false
    }
    try {
      const { filePath, preSignedUrl } = preSignedResult
      const axiosResponse = await axios.put(preSignedUrl, file, {
        headers: {
          'Content-Type': file.type
        }
      })
      if (axiosResponse.status === 200) {
        if (typeof window !== 'undefined' && typeof CustomEvent !== 'undefined') {
          window.dispatchEvent(new CustomEvent('sp_file_uploaded', { detail: filePath }))
        }
        return Promise.resolve({
          Key: filePath
        })
      }
      return false
    } catch (error) {
      return false
    }
  }
  inject('preSignedUploader', uploaderPreSigned)
}
