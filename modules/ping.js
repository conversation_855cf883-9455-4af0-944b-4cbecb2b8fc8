import axios from 'axios'

const defaults = {
  path: '/ping',
  contentType: 'text/plain',
  healthy: () => {
    return 'OK'
  }
}
const baseApiUrl = process.env.PROXY_API_URL || null

module.exports = function pingz (moduleOptions) {
  const options = Object.assign({}, defaults, this.options.pingz, moduleOptions)

  this.addServerMiddleware({
    path: options.path,
    async handler (req, res, next) {
      let isApiHealth = 0
      if (baseApiUrl !== null) {
        await axios.head(baseApiUrl + '/public/ping', {
          timeout: 1000 * 5
        }).then((data) => {
          if (data.status === 200) {
            isApiHealth = 1
          }
        }).catch(() => {
          isApiHealth = 2
        })
      }
      if (isApiHealth === 2) {
        res.statusCode = 503
        res.end()
        return false
      }
      res.setHeader('Content-Type', options.contentType)
      res.end(options.healthy())
    }
  })
}
