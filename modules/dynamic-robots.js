function generate (host) {
  const content = [
    `Sitemap: https://${host}/sitemap.xml`,
    'User-agent: *',
    'User-agent: FacebookBot',
    'user-agent: Pinterestbot',
    'User-agent: Googlebot',
    'Crawl-delay: 5',
    'Disallow:',
    '',
    'User-agent: Googlebot',
    'Disallow: /admin/',
    'Allow: /',
    '',
    'User-agent: Googlebot-image',
    'Disallow: /admin/',
    'Allow: /',
    '',
    'User-agent: *',
    'Disallow: /bot',
    'Allow: /',
  ]

  return content.join('\n')
}

module.exports = function robots () {
  this.addServerMiddleware({
    path: '/robots.txt',
    handler (req, res) {
      res.setHeader('Content-Type', 'text/plain')
      res.end(generate(req.headers.host))
    }
  })
}
