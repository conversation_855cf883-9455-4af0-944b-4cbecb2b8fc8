import axios from 'axios'

function getId (url) {
  if (!url.includes('?')) {
    return null
  }

  try {
    const searchParams = new URLSearchParams(`?${url.split('?')[1]}`)
    return searchParams.get('id')
  } catch (e) {
    return null
  }
}

async function fetchSitemap (req) {
  const id = getId(req.url)
  // const url = `${process.env.PROXY_API_URL || 'https://apis.dev.senprints.net'}/api/public/sitemap${id ? `/${id}` : ''}`
  let url = id ? `/api/public/sitemap/${id}` : '/api/public/sitemap'

  const host = req.headers.host

  // fix: connect ECONNREFUSED ::1:80
  // ref: https://github.com/axios/axios/issues/3821#issuecomment-1074294296
  const protocol = host.includes('localhost') ? 'http' : 'https'

  url = `${protocol}://${host}${url}`

  try {
    const { data } = await axios.get(url, {
      headers: {
        'X-DOMAIN': host
      }
    })

    return {
      success: true,
      data
    }
  } catch (error) {
    if (process.sentry) {
      process.sentry.captureException(error)
    }
    return {
      success: false,
      error
    }
  }
}

module.exports = function sitemap () {
  this.addServerMiddleware({
    path: '/sitemap.xml',
    async handler (req, res) {
      try {
        const json = await fetchSitemap(req)

        if (json.success) {
          res.setHeader('Content-Type', 'text/xml')
          res.end(json.data)
        } else {
        // https://stackoverflow.com/a/60261817/3872002
        // res.writeHead(302, { Location: '/' })
        // res.end()
          res.setHeader('Content-Type', 'text/plain')
          res.end('Error 1: please contact support')
          // res.end(JSON.stringify(json.error))
        }
      } catch (e) {
        res.setHeader('Content-Type', 'text/plain')
        res.end('Error 2: please contact support')
        // res.end(JSON.stringify(e))
      }
    }
  })
}
