<template>
  <main id="listingPage" class="container-xl py-4">
    <breadcrumbs :bread-crumbs-list="breadCrumbsList" class="scroll-able" />
    <div class="row justify-content-between position-relative">
      <filter-box-1
        v-if="isShowFilterBox1"
        class="filter-box col-lg-3 col-md-4 col-12 collection-filters order-md-0 order-1"
        :filter="listingDataFilter"
        :max-price="maxPrice"
        :min-price="minPrice"
        :current-category="currentCategory"
        :current-color="currentColor"
        :current-min-price="currentMinPrice"
        :current-max-price="currentMaxPrice"
        :page-type="pageType"
        :categories="filterCategories"
        :current-template="currentTemplate"
        :template-filter-list="templateFilterList"
        :general-templates="generalTemplates"
      />
      <div
        class="listing-box bg-white col-12 order-md-1 order-0"
        :class="isShowFilterBox1 ? 'col-lg-9 col-md-8' : ''"
      >
        <filter-box-2
          v-if="isShowFilterBox2"
          :title="title"
          :current-sort-type="currentSortType"
          :total-campaign="listingData.total"
          :page-type="pageType"
          :filter="listingDataFilter"
          :current-collection="currentCollection"
        />
        <template v-if="listingData.campaigns&&listingData.campaigns.length">
          <div
            v-if="listingData.banner_url"
            class="collection-banner"
            :style="`background-image: url(${$imgUrl(listingData.banner_url, 'full')});`"
          />
          <div class="campain-list row mt-4">
            <div
              v-for="(campaign, index) in listingData.campaigns"
              :key="index"
              class="col-6 mb-3"
              :class="isShowFilterBox1 ? 'col-lg-4' : 'col-lg-3 col-md-4'"
            >
              <campaign-item :campaign="campaign" :color="currentColor" />
            </div>
          </div>
          <b-pagination
            v-if="isShowPagination"
            class="mt-4 justify-content-end"
            :value="currentPage"
            :total-rows="listingData.total"
            :per-page="listingData.perPage"
            @change="page=>{$router.push(localePath({ path: $route.currentPath, query: {...$route.query, ...{page}} }))}"
          />
        </template>
        <p v-else class="h5 py-5 text-center">
          {{ $t('No result') }}
        </p>
      </div>
    </div>
    <client-only>
      <div v-if="cartTotalQuantity" class="opacity-9 position-fixed bottom-0 left-0 w-100 p-2 bg-white border z-index-100 d-md-none d-flex justify-content-between">
        <div>
          <div class="font-weight-500 font-sm">
            {{ $formatPrice($totalPrice($store.state.cart.products)) }}
          </div>
          <div><small>{{ cartTotalQuantity }} {{ cartTotalQuantity === 1 ? $t('item') : $t('items') }}</small></div>
        </div>
        <nuxt-link :to="localePath('/cart')">
          <b-button
            block
            variant="custom"
            class="font-weight-500 py-2-5 text-uppercase"
          >
            <span>{{ $t('View cart') }}</span>
          </b-button>
        </nuxt-link>
      </div>
    </client-only>
  </main>
</template>

<script>
import shopMixin from '~/mixins/listing'

import Breadcrumbs from '~/themes/default/components/common/Breadcrumbs'

import FilterBox1 from '~/themes/default/components/listing/FilterBox1'
import FilterBox2 from '~/themes/default/components/listing/FilterBox2'

import CampaignItem from '~/themes/default/components/products/CampaignItem'

export default {
  components: {
    Breadcrumbs,
    FilterBox1,
    FilterBox2,
    CampaignItem
  },
  mixins: [shopMixin],
  computed: {
    isShowFilterBox1 () {
      const storeInfo = this.$store.state.storeInfo
      return storeInfo.store_type !== 'express_listing' && storeInfo.enable_search
    },
    isShowFilterBox2 () {
      const storeInfo = this.$store.state.storeInfo
      return storeInfo.enable_search
    },
  }
}

</script>
<style lang="scss">

  #listingPage {
    background-color: white;

    @media (min-width: 768px) {
      .listing-box {
        min-height: 80vh;
      }

      .filter-box {
        position: sticky;
        top: 100px;
        height: max-content;
      }
    }
  }

  .page-item {

    .page-link {
      color: var(--primary-color);
    }
    &.active{
      .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }
    }
  }
  .collection-banner {
    background-size: cover;
    background-repeat:   no-repeat;
    background-position: center center;
    width: 100%;
    aspect-ratio: 13 / 2;
    margin-top: 10px;
  }
</style>
