<template>
  <div class="blog-detail blog-detail-page container">
    <!-- Blog Header -->
    <div class="blog-header">
      <!-- Category and Collections -->
      <div class="blog-meta">
        <span v-if="blog.category" class="category-text">
          {{ blog.category.name }} / {{ blog.title }}
        </span>
      </div>

      <!-- Title -->
      <h1 class="blog-title">
        {{ blog.title }}
      </h1>

      <!-- Collections Tags -->
      <div v-if="blog.collections && blog.collections.length" class="collections-tags">
        <collection-item
          v-for="(collection, index) in blog.collections"
          :key="index"
          :value="collection.name"
          :slug="`/collection/${collection.slug}`"
        />
      </div>

      <!-- Sub Description -->
      <p v-if="blog.sub_description" class="sub-description">
        {{ blog.sub_description }}
      </p>
    </div>

    <!-- Main Image -->
    <div v-if="blog.main_image" class="main-image-container">
      <img :src="$imgUrl(blog.main_image, 'full')" :alt="blog.title" class="main-image">
    </div>

    <!-- Blog Content -->
    <div class="blog-content" v-html="blog.html" />

    <!-- Discover More -->
    <div class="discover-more">
      <h3>Discover more blogs</h3>
      <ul v-if="suggestionBlogs.length">
        <li
          v-for="blog in suggestionBlogs"
          :key="blog.id"
          @click="navigateToBlog(blog.slug)"
        >
          {{ blog.title }}
        </li>
      </ul>
      <div v-else class="no-suggestions">
        <p>No related blogs found.</p>
      </div>
    </div>
  </div>
</template>

<script>
import { API_LIST } from '~/helpers/variableConst'
import CollectionItem from '~/themes/default/components/common/CollectionItem'

export default {
  name: 'BlogDetail',
  components: {
    CollectionItem
  },
  props: {
    blog: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data () {
    return {
      suggestionBlogs: []
    }
  },
  mounted () {
    this.fetchSuggestionBlogs()
  },
  methods: {
    fetchSuggestionBlogs () {
      this.$httpDefault('GET', `${API_LIST.API_BLOG}/suggestion/${this.blog.slug}`)
        .then((res) => {
          this.suggestionBlogs = res.data || []
        })
        .catch((error) => {
          console.error('Error fetching suggestion blogs:', error)
          this.suggestionBlogs = []
        })
    },
    navigateToBlog (slug) {
      this.$router.push({ path: this.localePath(`/blog/${slug}`) })
    }
  }
}
</script>

<style lang="scss">
.blog-detail-page {
  .blog-detail {
    margin: 0 auto;
    padding: 40px 0px;
  }

  .blog-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    margin-bottom: 47px;

    @media (max-width: 768px) {
      margin-bottom: 24px;
      gap: 6px;
    }
  }

  .blog-meta {
    .category-text {
      font-size: 12px;
      font-weight: 400;
      line-height: 1.5em;
      color: #373737;
      text-align: center;
    }
  }

  .blog-title {
    font-size: 36px;
    font-weight: 400;
    line-height: 1.47em;
    color: #000000;
    text-align: center;
    max-width: 90%;
    margin: 0;

    @media (max-width: 768px) {
      font-size: 24px;
      margin-top: 40px;
      font-weight: 500;
    }
  }

  .collections-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .sub-description {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5em;
    color: #000000;
    text-align: center;
    margin: 0;
    max-width: 800px;
  }

  .main-image-container {
    margin-bottom: 47px;
    text-align: center;

    @media (max-width: 768px) {
      margin-bottom: 24px;
      width: 100vw;
      margin-left: calc(-50vw + 50%);
      margin-right: calc(-50vw + 50%);
    }
  }

  .main-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
    object-fit: cover;

    @media (max-width: 768px) {
      border-radius: 0;
      max-width: none;
    }
  }

  // Blog content styling
  .blog-content {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.6em;
    color: #000000;
    margin-bottom: 30px;
    margin-left: auto;
    margin-right: auto;

    ::v-deep {
      p {
        margin-bottom: 1.2em;
        padding: 0 20px;
        &:last-child {
          margin-bottom: 0;
        }
      }

      h1, h2, h3, h4, h5, h6 {
        line-height: 1.4em;
        margin-top: 1.5em;
        margin-bottom: 0.8em;
        padding: 0 20px;
      }

      h1 { font-size: 38px; }
      h2 { font-size: 24px; }
      h3 { font-size: 22px; }
      h4 { font-size: 20px; }
      h5 { font-size: 16px; }
      h6 { font-size: 14px; }

      ul, ol {
        margin: 1.2em 0;
        padding-left: 2em;
        padding-right: 20px;
        margin-left: 20px;
        text-align: left;
      }

      li {
        margin-bottom: 0.5em;
      }

      blockquote {
        margin: 1.5em 0;
        padding: 1em 2em;
        margin-left: 20px;
        margin-right: 20px;
        border-left: 4px solid #E4E4E4;
        background-color: #F9F9F9;
        font-style: italic;
      }

      img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
        margin: 1.5em 0;
        object-fit: cover;

        @media (max-width: 768px) {
          width: 100vw;
          max-width: none;
          border-radius: 0;
          margin-left: calc(-50vw + 50%);
          margin-right: calc(-50vw + 50%);
        }
      }

      a {
        color: var(--primary-color);
        text-decoration: none;
        &:hover {
          text-decoration: underline;
        }
      }

      pre {
        background-color: #F5F5F5;
        padding: 1em;
        border-radius: 8px;
        overflow-x: auto;
        margin: 1.2em 20px;
      }

      code {
        background-color: #F5F5F5;
        padding: 0.2em 0.4em;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.9em;
      }

      pre code {
        background-color: transparent;
        padding: 0;
      }

      table {
        width: calc(100% - 40px);
        border-collapse: collapse;
        margin: 1.2em 20px;
      }

      th, td {
        border: 1px solid #E4E4E4;
        padding: 0.75em;
        text-align: left;
      }

      th {
        background-color: #F5F5F5;
        font-weight: 600;
      }
    }
  }

  // Products Section
  .products-section {
    margin-bottom: 47px;
  }

  .product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(237px, 1fr));
    gap: 12px;
    margin: 0 auto;
    justify-content: center;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .product-card {
    border: 1px solid #EBEBEB;
    border-radius: 10px;
    overflow: hidden;
    background: white;
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    text-decoration: none;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }
  }

  .product-image-placeholder {
    width: 100%;
    aspect-ratio: 1;
    display: block;
    position: relative;
    overflow: hidden;

    img {
      padding: 10px;
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
      margin: 0 !important;
      display: block !important;
      max-height: none !important;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .product-info {
    padding: 18px 15px;
    background: linear-gradient(180deg, rgba(241, 241, 241, 0.5) 0%, rgba(255, 255, 255, 0.5) 27.4%);
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .product-price {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: 500;
    line-height: 1.5em;
    color: #000000;
    margin: 0;
  }

  .product-name {
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5em;
    color: #000000;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  // Promotion Card
  .blog-card {
    display: flex;
    margin: 47px -38px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 20px;
    overflow: hidden;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .blog-content {
    flex: 1;
    padding: 40px;

    @media (max-width: 768px) {
      padding: 0px;
    }

    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 15px;
    background: white;

    h2 {
      font-size: 24px;
      font-weight: 400;
      line-height: 1.25em;
      color: #000000;
      margin: 0;
    }

    strong {
      font-weight: 600;
    }

    p {
      font-size: 16px;
      font-weight: 400;
      line-height: 1.41em;
      color: #000000;
      margin: 0;
    }
  }

  .blog-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5em;
    cursor: pointer;
    padding: 0;
    transition: gap 0.3s ease;

    &:hover {
      gap: 12px;
    }

    svg {
      width: 10px;
      height: 9px;
    }
  }

  .blog-image {
    width: 480px;
    flex-shrink: 0;

    @media (max-width: 768px) {
      width: 100%;
      height: 250px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 10px;
    }
  }

  // Discover More
  .discover-more {
    margin-top: 47px;

    h3 {
      font-size: 20px;
      font-weight: 500;
      line-height: 1.5em;
      color: #000000;
      margin: 0 0 9px 0;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    li {
      font-size: 16px;
      font-weight: 400;
      line-height: 1.8em;
      color: #000000;
      cursor: pointer;
      transition: color 0.3s ease;
      padding: 4px 0;

      &:hover {
        color: var(--primary-color);
      }
    }

    .no-suggestions {
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.8em;
        color: #999999;
        margin: 0;
        font-style: italic;
      }
    }
  }

  .main-image-container img {
    max-height: 500px;
    object-fit: cover;
    margin: 0 auto !important;
    display: inline-block !important;
    border-radius: 10px;

    @media (max-width: 768px) {
      max-height: none;
      width: 100%;
      border-radius: 0;
      display: block !important;
    }
  }

  .blog-content img:not(.product-image-placeholder img) {
    max-height: none !important;
    margin: 1.5em auto !important;
    display: block !important;
    border-radius: 10px !important;

    @media (max-width: 768px) {
      margin-left: calc(-50vw + 50%) !important;
      margin-right: calc(-50vw + 50%) !important;
      border-radius: 0 !important;
      max-width: 100% !important;
      margin: 0 !important;
    }
  }

  figure {
    width: 100% !important;
    display: block !important;

    @media (max-width: 768px) {
      width: 100vw !important;
      margin-left: calc(-50vw + 50%) !important;
      margin-right: calc(-50vw + 50%) !important;
    }
  }
}
</style>
