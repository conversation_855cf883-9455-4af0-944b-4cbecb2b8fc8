<template>
  <main class="blog-listing">
    <div class="container-xl px-4">
      <div class="blog-content">
        <!-- Structured Blog Layout: Featured + Regular Groups -->
        <div v-for="(section, sectionIndex) in structuredBlogs" :key="`section-${sectionIndex}`" class="blog-section">
          <!-- Featured Post -->
          <div v-if="section.featured" class="featured-posts">
            <div class="featured-post-row">
              <nuxt-link :to="localePath(`/blog/${section.featured.slug}`)" class="featured-post-link">
                <div class="featured-post-main">
                  <div class="featured-post-content">
                    <h1 class="featured-title">
                      {{ section.featured.title }}
                    </h1>
                    <p class="featured-description">
                      {{ section.featured.excerpt }}
                    </p>
                    <div class="featured-cta">
                      <span>Read More</span>
                      <i class="icon-sen-chevron-right" />
                    </div>
                  </div>
                  <div class="featured-post-image">
                    <img :src="$imgUrl(section.featured.featured_image, 'full')" :alt="section.featured.title">
                  </div>
                </div>
              </nuxt-link>
            </div>
          </div>

          <!-- Regular Posts Grid (6 posts in 2 rows of 3) -->
          <div v-if="section.regularPosts && section.regularPosts.length > 0" class="blog-posts-grid">
            <div v-for="(row, rowIndex) in section.regularPostRows" :key="`row-${rowIndex}`" class="blog-posts-row">
              <div v-for="post in row" :key="post.id" class="blog-post-card">
                <nuxt-link :to="localePath(`/blog/${post.slug}`)" class="post-link">
                  <article class="post-card">
                    <div class="post-image">
                      <img :src="$imgUrl(post.featured_image, 'full')" :alt="post.title">
                    </div>
                    <div class="post-content">
                      <span class="post-category">{{ post.category }}</span>
                      <h3 class="post-title">
                        {{ post.title }}
                      </h3>
                      <p class="post-excerpt">
                        {{ post.excerpt }}
                      </p>
                    </div>
                  </article>
                </nuxt-link>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="blogPagination && blogPagination.total > blogPagination.per_page" class="blog-pagination d-flex justify-content-center justify-content-md-end mt-5">
          <b-pagination
            :value="blogPagination.current_page"
            :per-page="blogPagination.per_page"
            :total-rows="blogPagination.total"
            pills
            @change="page => { $emit('changePage', page) }"
          />
        </div>
      </div>
    </div>
  </main>
</template>

<script>
export default {
  name: 'BlogAll',
  props: {
    blogs: {
      type: [Array, Object],
      required: true
    }
  },
  computed: {
    blogPagination () {
      // Handle pagination data from API response
      if (this.blogs && typeof this.blogs === 'object' && this.blogs.data) {
        return {
          current_page: this.blogs.current_page,
          per_page: this.blogs.per_page,
          total: this.blogs.total,
          last_page: this.blogs.last_page
        }
      }
      return null
    },

    processedBlogs () {
      // Handle real API data structure
      if (this.blogs && typeof this.blogs === 'object' && this.blogs.data && Array.isArray(this.blogs.data)) {
        return this.blogs.data.map((blog, index) => ({
          id: blog.id,
          title: blog.title,
          slug: blog.slug,
          excerpt: blog.sub_description,
          featured_image: blog.main_image,
          category: blog.category ? blog.category.name : 'Blog',
          is_featured: index === 0 || index === 7 // First post (index 0) and 8th post (index 7) are featured
        }))
      }

      // Handle direct array (legacy support)
      if (Array.isArray(this.blogs)) {
        return this.blogs.map((blog, index) => ({
          id: blog.id,
          title: blog.title,
          slug: blog.slug,
          excerpt: blog.excerpt || blog.description,
          featured_image: blog.featured_image || blog.image,
          category: blog.category || 'Blog',
          is_featured: index === 0 || index === 7 // First post (index 0) and 8th post (index 7) are featured
        }))
      }

      return []
    },

    featuredPosts () {
      return this.processedBlogs.filter(post => post.is_featured)
    },

    regularPosts () {
      return this.processedBlogs.filter(post => !post.is_featured)
    },

    structuredBlogs () {
      const sections = []
      const featured = this.featuredPosts
      const regular = this.regularPosts

      // Create sections: 1 featured + 6 regular posts pattern
      let regularIndex = 0

      for (let i = 0; i < featured.length; i++) {
        const section = {
          featured: featured[i],
          regularPosts: [],
          regularPostRows: []
        }

        // Get next 6 regular posts
        const nextRegularPosts = regular.slice(regularIndex, regularIndex + 6)
        section.regularPosts = nextRegularPosts

        // Group regular posts into rows of 3
        const rows = []
        for (let j = 0; j < nextRegularPosts.length; j += 3) {
          rows.push(nextRegularPosts.slice(j, j + 3))
        }
        section.regularPostRows = rows

        sections.push(section)
        regularIndex += 6
      }

      // If there are remaining regular posts after all featured posts, add them as a final section
      if (regularIndex < regular.length) {
        const remainingPosts = regular.slice(regularIndex)
        const rows = []
        for (let j = 0; j < remainingPosts.length; j += 3) {
          rows.push(remainingPosts.slice(j, j + 3))
        }

        sections.push({
          featured: null,
          regularPosts: remainingPosts,
          regularPostRows: rows
        })
      }

      return sections
    }
  }
}
</script>

<style lang="scss" scoped>
.blog-listing {
  padding: 2rem 0;

  @media (max-width: 768px) {
    margin-top: 20px;
  }
}

.blog-content {
  display: flex;
  flex-direction: column;
  gap: 69px;
  max-width: 1501px;
  margin: 0 auto;
}

.blog-section {
  display: flex;
  flex-direction: column;
  gap: 69px;
}

/* Featured Posts Section */
.featured-posts {
  display: flex;
  flex-direction: column;
  gap: 69px;
}

.featured-post-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 71px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
  }
}

.featured-post-link {
  text-decoration: none;
  color: inherit;
  display: block;
  width: 100%;

  &:hover {
    text-decoration: none;
    color: inherit;
  }
}

.featured-post-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 71px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    .featured-post-image {
      transform: scale(1.02);

      img {
        filter: brightness(1.1);
      }
    }

    .featured-cta {
      transform: translateX(5px);

      i {
        transform: translateX(3px);
      }
    }

    .featured-title {
      color: var(--primary-color);
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
  }
}

.featured-post-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 666px;

  @media (max-width: 768px) {
    width: 100%;
  }
}

.featured-title {
  font-weight: 300;
  font-size: 36px;
  line-height: 1.4;
  color: #000000;
  margin: 0;
  transition: color 0.3s ease;

  @media (max-width: 768px) {
    font-size: 28px;
  }
}

.featured-description {
  line-height: 1.5;
  color: #000000;
  margin: 0;
}

.featured-cta {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-color);
  font-weight: 500;
  line-height: 1.5;
  width: fit-content;
  transition: transform 0.3s ease;
  cursor: pointer;

  i {
    transition: transform 0.3s ease;
  }
}

.featured-post-image {
  width: 748px;
  height: 492px;
  border-radius: 20px;
  overflow: hidden;
  transition: transform 0.3s ease;

  @media (max-width: 768px) {
    width: 100%;
    height: 300px;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: filter 0.3s ease;
  }
}

/* Blog Posts Grid */
.blog-posts-grid {
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.blog-posts-row {
  display: flex;
  align-items: stretch;
  gap: 26px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
  }
}

.blog-post-card {
  flex: 1;
  min-width: 0;
}

.post-link {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;

  &:hover {
    text-decoration: none;
    color: inherit;
  }
}

.post-card {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  transition: transform 0.3s ease;
  background: white;

  &:hover {
    transform: translateY(-5px);
  }
}

.post-image {
  width: 100%;
  height: 419px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.post-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 7px;
  flex-grow: 1;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-top: none;
  border-radius: 0 0 20px 20px;
}

.post-category {
  line-height: 1.5;
  color: #000000;
  letter-spacing: 0.5px;
}

.post-title {
  font-weight: 500;
  font-size: 18px;
  line-height: 1.39;
  color: #000000;
  margin: 0;
  height: 50px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.post-excerpt {
  line-height: 1.5;
  color: #000000;
  margin: 0;
  height: 52px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

/* Pagination */
.blog-pagination {
  margin-top: 3rem;

  ::v-deep .pagination {
    margin-bottom: 0;

    .page-link {
      color: #000000;
      border-color: rgba(0, 0, 0, 0.2);

      &:hover {
        color: var(--primary-color);
        border-color: var(--primary-color);
        background-color: rgba(241, 100, 30, 0.1);
      }
    }

    .page-item.active .page-link {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      color: white;
    }
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .blog-content {
    max-width: 100%;
    padding: 0 1rem;
  }

  .featured-post-image {
    width: 100%;
    max-width: 600px;
  }

  .featured-post-content {
    width: 100%;
    max-width: 500px;
  }
}

/* Tablet Featured Posts */
@media (max-width: 1024px) and (min-width: 769px) {
  .featured-post-main {
    gap: 40px;
  }

  .featured-post-image {
    width: 100%;
    max-width: 450px;
    height: 350px;
  }

  .featured-post-content {
    width: 100%;
    max-width: 400px;
  }

  .featured-title {
    font-size: 32px;
  }
}

/* Tablet View - 2 columns */
@media (max-width: 1024px) and (min-width: 769px) {
  .blog-posts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 26px;
  }

  .blog-posts-row {
    display: contents; /* This makes the row wrapper transparent to CSS Grid */
  }

  .blog-post-card {
    flex: none; /* Reset flex properties */
    min-width: auto; /* Reset min-width */
  }

  .post-image {
    height: 300px;
  }
}

/* Mobile View - 1 column */
@media (max-width: 768px) {
  .blog-content {
    gap: 40px;
  }

  .blog-section {
    gap: 40px;
  }

      .featured-posts {
    gap: 0;
  }

  .featured-post-row {
    gap: 0;
  }

  .featured-post-link {
    display: block;
    width: 100%;
  }

  .featured-post-main {
    position: relative;
    height: 320px;
    gap: 0;

    &:hover {
      .featured-post-image {
        transform: none;
      }
    }
  }

  .featured-post-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 60%, transparent 100%);
      z-index: 1;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .featured-post-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24px 20px;
    z-index: 2;
    gap: 12px;
    width: 100%;
  }

  .featured-title {
    font-size: 24px;
    color: white;
    margin-bottom: 8px;
  }

  .featured-description {
    font-size: 14px;
    line-height: 1.4;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 12px;
  }

  .featured-cta {
    font-size: 14px;
    background: white;
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    gap: 6px;

    &:hover {
      background: rgba(255, 255, 255, 0.95);
    }
  }

  .blog-posts-grid {
    gap: 20px;
  }

  .blog-posts-row {
    flex-direction: column;
    gap: 20px;
  }

  .blog-post-card {
    width: 100%;
    max-width: 100%;
    margin: 0;
  }

        .post-card {
    display: grid;
    grid-template-columns: 160px 1fr;
    gap: 16px;
    align-items: start;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    min-height: 104px; /* Matches image height with 1.15 aspect ratio */

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }
  }

    .post-image {
    width: 160px;
    aspect-ratio: 1.15;
    height: 100%;
    border-radius: 12px 0 0 12px;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .post-content {
    padding: 16px 16px 16px 0;
    border: none;
    border-radius: 0;
    gap: 4px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .post-category {
    font-size: 12px;
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    padding: 0;
    background: none;
  }

  .post-title {
    font-size: 16px;
    line-height: 1.3;
    height: auto;
    margin-bottom: 4px;
    line-clamp: 2;
    -webkit-line-clamp: 2;
  }

  .post-excerpt {
    font-size: 14px;
    line-height: 1.4;
    height: auto;
    color: #666;
    line-clamp: 2;
    -webkit-line-clamp: 2;
  }

  .blog-pagination {
    justify-content: center !important;
    margin-top: 2rem;
  }
}

/* Extra small devices */
@media (max-width: 480px) {
  .blog-content {
    padding: 0;
  }

  .featured-post-main {
    height: 280px;
  }

  .featured-post-content {
    padding: 20px 16px;
  }

  .featured-title {
    font-size: 20px;
    line-height: 1.2;
  }

  .featured-description {
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .featured-cta {
    font-size: 13px;
    padding: 6px 14px;
  }

      .post-card {
    gap: 12px;
    min-height: 87px; /* Matches image height with 1.15 aspect ratio */
  }

  .post-image {
    aspect-ratio: 1.15;
  }

  .post-content {
    padding: 12px 12px 12px 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .post-title {
    font-size: 15px;
  }

  .post-excerpt {
    font-size: 13px;
  }
}
</style>
