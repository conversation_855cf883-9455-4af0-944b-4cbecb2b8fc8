<template>
  <main id="thankyouPage" class="container-xl" dusk="thank-you-page">
    <div class="row mt-5 mt-sm-0">
      <!-- form -->
      <div class="col-12 col-md-7 mb-5">
        <div class="mb-3">
          <h3 class="mb-4" dusk="h3-receive-your-order">
            {{ $t('We’ve receive your order') }}
          </h3>
          <h5 class="mb-0">
            <span>
              {{ $t('Your order number is') }}
            </span>
            <span class="text-custom"> {{ order.order_number }}</span>
          </h5>
          <a :href="localePath(`/order/status/${order.access_token}`)" target="_blank" rel="noopener">
            <b-button
              variant="custom"
              type="button"
              class="border-radius-none mt-2 mb-2"
              style="text-transform: uppercase;"
            >
              {{ $t('Track Your Order') }}
            </b-button>
          </a><br>
          <i>
            {{ $t('Your order confirmation is sent to') }}
            <span id="email-purchase" class="font-weight-500">{{ order.customer_email }}</span><br>
            <small>{{ $t("Please check your spam box if you don't see the email.") }}<br>
              <a
                :href="localePath('/page/contact-us')"
                target="_blank"
                rel="noopener"
                class="text-primary"
              >
                {{ $t('Or contact us to edit your email address') }}
              </a>
              {{ $t('if it is incorrect.') }}
            </small>
          </i>
        </div>

        <div v-if="currentShippingMethod !== null" class="mb-3">
          <h5>
            {{ $t('Shipping method') }}
          </h5>
          <p class="text-capitalize">
            <span>{{ $t(currentShippingMethod.name + ' delivery') }} </span>
            <span v-if="currentShippingMethod.name ==='express' " class="text-danger font-weight-500">({{ $t('No cancellation allowed') }})</span>
          </p>
          <div v-if="order.is_notify_china_delivery_late" style="color: rgb(255, 145, 0);">
            <small>{{ $t('* Due to stock shortages during this season, there maybe slight delays in the delivery of your order.') }}</small>
          </div>
        </div>

        <div class="mb-3">
          <h5>
            {{ $t('Shipping to') }}
          </h5>
          <div>
            <a
              v-if="canEditAddress"
              href="#"
              class="text-info edit-address float-right"
              @click.prevent="$refs.modalEditInfo.showModalEditAddress = true"
            >{{ $t('Edit') }}
            </a>
            <p v-if="order.customer_name" class="mb-1">
              <i><strong id="full-name-purchase">{{ order.customer_name }}</strong></i>
            </p>
            <p v-if="order.address" class="mb-1">
              <i id="address-purchase">{{ order.address }} {{ order.address_2 || '' }}</i>
            </p>
            <p v-if="order.city" class="mb-1">
              <i><span id="city-purchase">{{ order.city }}</span>&nbsp;<span id="region-purchase">{{ order.state || '' }}</span>&nbsp;<span id="postal-code-purchase">{{ order.postcode || '' }}</span></i>
            </p>
            <p v-if="hasHouseNumber" class="mb-1">
              <i>{{ $t('House number') }}: {{ order.house_number }}</i>
            </p>
            <p v-if="hasMailboxNumber" class="mb-1">
              <i>{{ $t('Mailbox number') }}: {{ order.mailbox_number }}</i>
            </p>
            <p v-if="currentCountry.name" class="mb-1">
              <i id="country-purchase">{{ currentCountry.name }}</i>
            </p>
            <p v-if="order.customer_phone" class="mb-1">
              <i id="phone-purchase">{{ order.customer_phone }}</i>
            </p>
          </div>
        </div>
        <GenericReprintsRefundPolicy :order-note="order.order_note" />
        <div v-if="storeDetail && storeDetail.promotion_title && storeDetail.discount_code" class="promotion-box p-3 my-5">
          <h6 class="promotion-title px-2 text-uppercase">
            {{ $t('Thank you for your order') }}
          </h6>
          <p>
            <i>{{ storeDetail.promotion_title||'Promotion title' }}</i>
          </p>
          <b-form class="my-3" @submit.prevent="$router.push(localePath('/collection'))">
            <b-input-group>
              <b-form-input
                id="coupon_code"
                :value="storeDetail.discount_code"
                type="text"
                name="coupon_code"
                required
                :placeholder="$t('Discount Code')"
              />
              <b-input-group-append>
                <b-button
                  variant="custom"
                  type="submit"
                  class="border-radius-none "
                >
                  {{ $t('Shop now') }}
                </b-button>
              </b-input-group-append>
            </b-input-group>
          </b-form>
        </div>
      </div>
      <!-- <order-summary /> -->
      <!-- <order-summary /> -->
      <div class="col-12 col-md-5">
        <h3 class="mb-3">
          {{ $t('Order Summary') }}
        </h3>
        <div class="list-items">
          <!-- Item 1 -->
          <product-item
            v-for="(item, index) in order.products"
            :key="index"
            :item="item"
            :index="index"
            :currency-code="order.currency_code"
            :currency-rate="order.currency_rate"
            page="thank-you"
          />
        </div>
        <caculate-price
          :order="order"
        />

        <p
          v-if="statement_descriptor"
          class="mt-3 text-danger"
        >
          {{ $t('Please note that this charge will appear as') }}&nbsp;<strong>{{ statement_descriptor }}</strong>&nbsp;{{ $t('on your credit card statement.') }}
        </p>
      </div>
    </div>
    <div v-if="relatedCart&&relatedCart.length" class="my-5">
      <h3 class="text-center text-uppercase">
        {{ $t('You May Be Intertested In These Products') }}
      </h3>
      <carousel-campaign :campaigns="relatedCart" />
    </div>

    <modal-edit-info
      v-if="order.address_verified === 'invalid'"
      ref="modalEditInfo"
      :order="order"
    />

    <modal-confirm-address
      v-if="canEditAddress"
      ref="modalConfirmAddress"
      :order="order"
      @showEditAddress="$refs.modalEditInfo.showModalEditAddress = true"
    />
  </main>
</template>

<script>
import thankyouMixin from '~/mixins/thankYou'
import ProductItem from '~/themes/default/components/order/ProductItem'
import CaculatePrice from '~/themes/default/components/order/CaculatePrice'
import CarouselCampaign from '~/themes/default/components/products/CarouselCampaign'
import ModalEditInfo from '~/themes/default/components/order/ModalEditInfo'
import ModalConfirmAddress from '~/themes/default/components/order/ModalConfirmAddress'
import GenericReprintsRefundPolicy from '~/components/GenericReprintsRefundPolicy.vue'

export default {
  components: {
    ProductItem,
    CaculatePrice,
    CarouselCampaign,
    ModalEditInfo,
    ModalConfirmAddress,
    GenericReprintsRefundPolicy
  },
  mixins: [thankyouMixin],
  computed: {
    canEditAddress () {
      const DAY = 86400000
      const order = this.order
      const numberDay = order.type === 'regular' && order.address_verified === 'invalid' ? 2 : 1
      return (new Date(order.paid_at).getTime() + numberDay * DAY) > (new Date().getTime())
    },
    hasHouseNumber () {
      return this.order.house_number && this.order.house_number.trim().length > 0
    },
    hasMailboxNumber () {
      return this.order.mailbox_number && this.order.mailbox_number.trim().length > 0
    },
  }
}

</script>

<style lang="scss">
.promotion-box {
  position: relative;
  border: dashed 2px var(--primary-color);

  .promotion-title {
    background-color: white;
    position: absolute;
    top: -12px;
  }
}

#coupon_code {
  max-width: 300px;
}
</style>
