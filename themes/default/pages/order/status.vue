<template>
  <main id="StatusPage" class="sen-tracking">
    <div v-if="tracking" class="container-xl mt-5 mt-sm-0">
      <!-- processing -->
      <div class="row justify-content-center mb-md-5">
        <div class="col-10 col-lg-8 d-flex flex-wrap order-process py-4">
          <div class="col-12 col-md-auto milestone active">
            <i class="icon-sen-hand-coin" />
            <span>{{ $t('Received') }}</span>
            <span class="text-secondary"> ( {{ getTimeTracking(timeframe.received) }} )</span>
          </div>
          <div class="col step" :class="{active: currentTime >= timeframe.validate}" />
          <div class="col-12 col-md-auto milestone" :class="{active: currentTime >= timeframe.validate}">
            <i class="icon-sen-account-check" />
            <span>{{ $t('Validated') }}</span>
            <span class="text-secondary"> ( {{ getTimeTracking(timeframe.validate) }} )</span>
          </div>

          <div class="col step" :class="{active: currentTime >= timeframe.print}" />
          <div class="col-12 col-md-auto milestone" :class="{active: currentTime >= timeframe.print}">
            <i class="icon-sen-printer-check" />
            <span>{{ $t('Printed') }}</span>
            <span class="text-secondary"> ( {{ getTimeTracking(timeframe.print) }} )</span>
          </div>

          <div class="col step" :class="{active: currentTime >= timeframe.package}" />
          <div class="col-12 col-md-auto milestone" :class="{active: currentTime >= timeframe.package}">
            <i class="icon-sen-package-variant" />
            <span>{{ $t('Packaged') }}</span>
            <span class="text-secondary"> ( {{ getTimeTracking(timeframe.package) }} )</span>
          </div>

          <div class="col step" :class="['completed', 'cancelled', 'refunded'].includes(order.status) || currentTime >= timeframe.ship ? 'active' : ''" />
          <div class="col-12 col-md-auto milestone" :class="['completed', 'cancelled', 'refunded'].includes(order.status) || currentTime >= timeframe.ship ? 'active' : ''">
            <i v-if="order.status === 'refunded'" class="icon-sen-cash-refund" />
            <i v-else-if="order.status === 'cancelled'" class="icon-sen-cart-remove" />
            <i v-else class="icon-sen-truck-delivery-outline" />
            <span v-if="order.status === 'refunded'">{{ $t('Refunded') }}</span>
            <span v-else-if="order.status === 'cancelled'">{{ $t('Cancelled') }}</span>
            <span v-else-if="order.fulfill_status === 'fulfilled'">{{ $t('Delivered') }}</span>
            <span v-else>{{ $t('On Delivery') }}</span>
            <span v-if="order.status === 'completed'" class="text-secondary"> ( {{ getTimeTracking(timeframe.delivered && timeframe.delivered > timeframe.ship ? timeframe.delivered : timeframe.ship) }} )</span>
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <!-- list product -->
        <div v-if="listKeys && listKeys.length > 0" class="col-12 col-md-7 mb-5 mb-md-0">
          <div class="mb-3">
            <h4 class="mb-0">
              <span>{{ $t('Details for order') }}</span><strong> #{{ order.order_number }}</strong>
            </h4>
            <div v-if="$isShowWarningShipLate()" style="color: rgb(255, 145, 0);">
              <span>{{ $t('* Order may arrive post-Christmas due to peak season.') }}</span>
            </div>
            <div v-if="order.is_notify_china_delivery_late" style="color: rgb(255, 145, 0);">
              <small>{{ $t('* Due to stock shortages during this season, there maybe slight delays in the delivery of your order.') }}</small>
            </div>
          </div>
          <div class="d-flex justify-content-between">
            <p>
              <span>{{ $t('Need support?') }} </span>
              <a
                :href="localePath('/page/contact-us')"
                target="_blank"
                rel="noopener"
                class="text-info font-weight-500"
              >{{ $t('Submit a ticket') }}</a>
            </p>
            <template v-if="order.shipping_method === 'standard' && !['completed', 'cancelled', 'refunded'].includes(order.status)">
              <span v-if="!order.request_cancel && (new Date(order.paid_at).getTime() + 43200000 > new Date().getTime())" class="text-info font-weight-500 cursor-pointer" @click="$refs.modalRequestCancelOrder.isShowModal = true">{{ $t('Request to cancel order') }}</span>
              <span v-else-if="order.request_cancel && order.request_cancel.status === 'pending' && parseInt(order.request_cancel.sent_email) === 0" class="text-info font-weight-500 cursor-pointer" @click="showConfirmDeleteRequestCancelOrder()">{{ $t('Cancel requested') }}</span>
              <span v-else-if="order.request_cancel && order.request_cancel.status === 'pending' && parseInt(order.request_cancel.sent_email) === 1" class="text-info font-weight-500 cursor-pointer" @click="showModalPendingCustomerAction()">{{ $t('Pending Customer Action') }}</span>
              <span v-else-if="order.request_cancel && order.request_cancel.status === 'confirmed'" class="text-danger font-weight-500 cursor-pointer" @click="showConfirmDeleteRequestCancelOrder()">{{ $t('Cancellation confirmed') }}</span>
              <span v-else-if="order.request_cancel && order.request_cancel.status === 'processing'" class="text-info font-weight-500">{{ $t('Processing refund') }}</span>
            </template>
          </div>
          <div class="d-flex">
            <b-alert
              variant="success"
              dismissible
              fade
              :show="successCancelOrder"
              @dismissed="successCancelOrder = false; isShowMessageOrderProcessing = false; isShowMessageRequestExpired = false;"
            >
              {{ $t('Your cancellation request has been submitted. We will send you a confirmation email shortly. Please check your inbox in a few minutes. You have 12 hours to confirm the cancellation of your order via email, otherwise it will automatically be resumed.') }}
            </b-alert>
            <b-alert
              variant="danger"
              dismissible
              fade
              :show="isShowMessageOrderProcessing"
              @dismissed="isShowMessageOrderProcessing = false; successCancelOrder = false; isShowMessageRequestExpired = false;"
            >
              {{ $t('You cannot cancel your order cancellation request. Your order is in processing status.') }}
            </b-alert>
            <b-alert
              variant="danger"
              class="w-100"
              dismissible
              fade
              :show="isShowMessageRequestExpired"
              @dismissed="isShowMessageRequestExpired = false; isShowMessageOrderProcessing = false; successCancelOrder = false;"
            >
              {{ $t('Your request cancel order is expired.') }}
            </b-alert>
          </div>
          <div v-for="(item, index) in listKeys" :key="index">
            <div class="product-status mb-5">
              <div class="p-3 border">
                <p class="font-weight-500 mb-2">
                  <span :class="`text-${getOrderStatus(item).className}`">{{ getOrderStatus(item).text }}</span>&nbsp;({{ getTotalTrackingItemText(tracking[item]) }})
                </p>
                <div v-if="item !== 'unfulfilled' && (item.includes('on_delivery') || item.includes('fulfilled'))" class="d-flex">
                  <span>
                    {{ $t('Tracking') }}:
                  </span>
                  <div class="d-flex flex-column ml-2">
                    <template v-for="(trackingCode, idx) in tracking[item][0].tracking_info.tracking_code">
                      <a
                        v-if="tracking[item][0].tracking_info.tracking_url?.[idx]"
                        :key="idx"
                        :href="tracking[item][0].tracking_info.tracking_url?.[idx]"
                        target="_blank"
                        rel="noopener noreferrer nofollow"
                        class="text-primary text-underline text-capitalize"
                      >
                        {{ tracking[item][0].tracking_info.shipping_carrier?.[idx] }} ({{ trackingCode }})
                      </a>
                      <span
                        v-else
                        :key="idx"
                        class="text-info text-capitalize"
                      >{{ tracking[item][0].tracking_info.shipping_carrier?.[idx] }} ({{ trackingCode }})</span>
                    </template>
                  </div>
                </div>
              </div>
              <div class="p-2 py-lg-2 px-lg-3 border-bottom border-left border-right">
                <div class="list-items">
                  <product-item
                    v-for="(product, productIndex) in tracking[item]"
                    :key="productIndex"
                    :pos="productIndex"
                    :item="product"
                    :order-status="order.status"
                    :currency-code="order.currency_code"
                    :currency-rate="order.currency_rate"
                    :customer-name="order.customer_name"
                    page="order-status"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="concern-message">
            {{ $t('For any concerns regarding product quality or defects, kindly contact our customer support for prompt resolution and assistance') }}.
            <div>
              <NuxtLink to="/page/contact-us" class="text-info font-weight-500">
                {{ $t('Contact us') }}
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- order-summary -->
        <div class="col-12 col-md-5">
          <h4 class="mb-0">
            {{ $t('Delivery to') }}
          </h4>
          <p class="text-capitalize">
            <span class="font-weight-500">{{ $t(order.shipping_method + ' delivery') }} </span>
            <span v-if="order.shipping_method ==='express' " class="text-danger font-weight-500">({{ $t('No refund allowed') }})</span>
          </p>
          <div class="mb-3">
            <div :class="{'order-info': order.fulfill_status === 'unfulfilled'}">
              <a
                v-if="canEditAddress"
                href="#"
                class="text-info edit-address"
                @click.prevent="$refs.modalEditInfo.showModalEditAddress = true"
              >{{ $t('Edit') }}</a>
              <p v-if="order.customer_name" class="mb-1">
                <i><strong>{{ order.customer_name }}</strong></i>
              </p>
              <p v-if="order.address" class="mb-1">
                <i>{{ order.address }} {{ order.address_2 || '' }}</i>
              </p>
              <p v-if="order.city" class="mb-1">
                <i>{{ order.city }} {{ order.state || '' }} {{ order.postcode || '' }}</i>
              </p>
              <p v-if="hasHouseNumber" class="mb-1">
                <i>{{ $t('House number') }}: {{ order.house_number }}</i>
              </p>
              <p v-if="hasMailboxNumber" class="mb-1">
                <i>{{ $t('Mailbox number') }}: {{ order.mailbox_number }}</i>
              </p>
              <p v-if="currentCountry.name" class="mb-1">
                <i>{{ currentCountry.name }}</i>
              </p>
              <p v-if="order.customer_phone" class="mb-1">
                <i>{{ order.customer_phone }}</i>
              </p>
            </div>
          </div>

          <h4 class="mb-3">
            {{ $t('Order Summary') }}
          </h4>

          <caculate-Price
            :order="order"
          />

          <p
            v-if="statement_descriptor"
            class="mt-3 text-danger"
          >
            {{ $t('Please note that this charge will appear as') }}&nbsp;<strong>{{ statement_descriptor }}</strong>&nbsp;{{ $t('on your credit card statement.') }}
          </p>

          <div v-if="storeDetail && storeDetail.promotion_title && storeDetail.discount_code" class="promotion-box p-3 my-5">
            <h6 class="promotion-title px-2 text-uppercase">
              {{ $t('Thank you for your order') }}
            </h6>
            <p>
              <i>{{ storeDetail.promotion_title||'Promotion title' }}</i>
            </p>
            <b-form class="my-3" @submit.prevent="$router.push(localePath('/collection'))">
              <b-input-group>
                <b-form-input
                  id="coupon_code"
                  :value="storeDetail.discount_code"
                  type="text"
                  name="coupon_code"
                  required
                  :placeholder="$t('Discount Code')"
                />
                <b-input-group-append>
                  <b-button
                    variant="custom"
                    type="submit"
                    class="border-radius-none "
                  >
                    {{ $t('Shop now') }}
                  </b-button>
                </b-input-group-append>
              </b-input-group>
            </b-form>
          </div>
        </div>
      </div>
      <!-- Related Products -->
      <client-only>
        <div v-if="relatedCart && relatedCart.length && $store.state.storeInfo.status!=='blocked'" class="my-5">
          <h3 class="text-center text-uppercase">
            {{ $t('You May Be Interested In These Products') }}
          </h3>
          <carousel-campaign :campaigns="relatedCart" />
        </div>
      </client-only>
    </div>

    <modal-edit-info
      v-if="canEditAddress"
      ref="modalEditInfo"
      :order="order"
    />
    <modal-request-cancel-order
      ref="modalRequestCancelOrder"
      :order="order"
      @setSuccessCancelOrder="setSuccessCancelOrder"
    />
    <b-modal
      v-model="isShowConfirmDeleteRequest"
      centered
      body-class="pb-0"
      hide-footer
      :title="$t('Confirmation')"
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalConfirmDeleteRequestCancelOrder', null)"
      @hidden="$tracking.newCustomTracking('modal-hidden', null, 'modalConfirmDeleteRequestCancelOrder', null)"
    >
      <p>{{ $t('Are you sure you want to resume this order?') }}</p>
      <div class="row p-1">
        <b-button
          block
          variant="custom"
          class="border-radius-none"
          @click="deleteRequestCancelOrder(order)"
        >
          {{ $t('Resume order') }}
        </b-button>
      </div>
    </b-modal>
    <b-modal
      id="modalShowPendingCustomerAction"
      v-model="isShowPendingCustomerAction"
      centered
      hide-footer
      size="lg"
      body-class="d-flex flex-wrap justify-content-center"
      header-class="py-2 border-none"
      :no-close-on-backdrop="true"
      :no-close-on-esc="true"
      :title="$t('Notification')"
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalShowPendingCustomerAction', null)"
      @hidden="$tracking.newCustomTracking('modal-hidden', null, 'modalShowPendingCustomerAction', null)"
    >
      <template #modal-header-close>
        <span class="close-icon"><i class="icon-sen-close-circle" /></span>
      </template>
      <div class="col-12 py-2 text-center">
        <h6 class="mb-5 text-center w-100">
          {{ $t('We have sent an email to your email, please check your inbox to confirm the cancellation of this order.') }}
        </h6>
      </div>
      <div class="col-6">
        <b-button
          block
          variant="secondary"
          class="border-radius-none"
          @click="isShowPendingCustomerAction = false"
        >
          {{ $t('Close') }}
        </b-button>
      </div>
      <div class="m-0 col-6">
        <b-button
          block
          variant="primary"
          class="border-radius-none"
          @click="deleteRequestCancelOrder(order)"
        >
          {{ $t("Resume order") }}
        </b-button>
      </div>
    </b-modal>
  </main>
</template>
<script>
import statusMixin from '~/mixins/status'

import ProductItem from '~/themes/default/components/order/ProductItem'
import CaculatePrice from '~/themes/default/components/order/CaculatePrice'
import ModalEditInfo from '~/themes/default/components/order/ModalEditInfo'

import CarouselCampaign from '~/themes/default/components/products/CarouselCampaign'
import ModalRequestCancelOrder from '~/themes/default/components/order/ModalRequestCancelOrder'

export default {
  components: {
    CarouselCampaign,
    ProductItem,
    CaculatePrice,
    ModalEditInfo,
    ModalRequestCancelOrder
  },
  mixins: [statusMixin],
  data () {
    return {
      successCancelOrder: false,
      isShowConfirmDeleteRequest: false,
      isShowMessageRequestExpired: false,
      isShowMessageOrderProcessing: false,
      isShowPendingCustomerAction: false
    }
  },
  computed: {
    hasMailboxNumber () {
      return this.order.mailbox_number && this.order.mailbox_number.trim() !== ''
    },
    hasHouseNumber () {
      return this.order.house_number && this.order.house_number.trim() !== ''
    }
  },
  created () {
    this.isShowMessageOrderProcessing = this.$route.query.error && parseInt(this.$route.query.error) === 1
    this.isShowMessageRequestExpired = this.$route.query.error && parseInt(this.$route.query.error) === 2
  },
  methods: {
    setSuccessCancelOrder (value) {
      this.successCancelOrder = value
    },
    showConfirmDeleteRequestCancelOrder () {
      this.isShowPendingCustomerAction = false
      this.isShowConfirmDeleteRequest = true
    },
    showModalPendingCustomerAction () {
      this.isShowConfirmDeleteRequest = false
      this.isShowPendingCustomerAction = true
    },
    deleteRequestCancelOrder (order) {
      this.$store.dispatch('order/deleteRequestCancelOrder', order.access_token).then((result) => {
        this.isShowConfirmDeleteRequest = false
        if (result && result.success) {
          this.$store.commit('order/UPDATE_ORDER_DATA', {
            order: {
              request_cancel: null
            }
          })
          this.successCancelOrder = false
          this.isShowPendingCustomerAction = false
          this.$nuxt.refresh()
        } else {
          this.$toast.error(result.message)
        }
      })
    },
    getTotalTrackingItemText (trackingItem) {
      let total = 0
      for (let i = 0; i < trackingItem.length; i++) {
        total += trackingItem[i].quantity
      }
      if (total > 1) {
        return `${total} ${this.$t('items').toLowerCase()}`
      }
      return `${total} ${this.$t('Item').toLowerCase()}`
    },
  }
}
</script>

<style lang="scss">
.promotion-box {
  position: relative;
  border: dashed 2px var(--primary-color);

  .promotion-title {
    background-color: white;
    position: absolute;
    top: -12px;
  }
}

.order-process {
  .step {
    background-color: gray;

    &.active {
      background-color: var(--primary-color);
    }
  }

  .milestone {
    color: #999;
    padding: 0 20px;

    &.active {
      color: var(--primary-color);

      &::before {
        color: var(--primary-color);
      }
    }

    i {
      font-size: 24px;
      position: absolute;
      top: -8px;
      transform: translateX(-150%);
    }
  }

  @media (min-width: 768px) {
    .step {
      height: 3px;
      margin-top: 5px;
    }

    .milestone {
      padding-top: 20px;
      font-weight: 500;

      span {
        position: absolute;
        transform: translateX(-50%);
        width: max-content;
      }

      i {
        font-size: 32px;
        top: -18px;
        transform: translateX(-50%);
      }

      .text-secondary {
        transform: translate(-50%, 100%);
      }
    }
  }
}

.order-info {
  position: relative;

  .edit-address {
    position: absolute;
    right: 0;
  }
}

#coupon_code {
  max-width: 300px;
}

.phone-tooltip {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgb(185, 185, 185);
  color: #fff;
  border-radius: 50%;
  height: 15px;
  width: 15px;
  text-align: center;
  line-height: 15px;
  cursor: pointer;

  .tooltip-content {
    position: absolute;
    display: none;
    background-color: rgb(66, 66, 66);
    width: max-content;
    border-radius: 10px;
    padding: 0.5rem;
    top: -100%;
    left: 50%;
    transform: translate(-50%, -100%);

    &::after {
      content: "";
      position: absolute;
      bottom: -9px;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid rgb(66, 66, 66);
      left: 50%;
      transform: translateX(-50%);
    }

    @media (max-width: 992px) {
      transform: translate(-80%, -100%);

      &::after {
        left: 80%;
      }
    }
  }

  &:hover {
    .tooltip-content {
      display: inherit;
    }
  }
}

.total {
  font-size: 1.3rem;
}

.concern-message {
  margin-top: 10px;
  color: #dc3545;
}
</style>
