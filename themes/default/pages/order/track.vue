<template>
  <main id="trackPage" class="container-xl">
    <div class="row">
      <div class="col py-6">
        <h1 class="mb-3">
          {{ $t('Track Your Order') }}
        </h1>

        <div v-if="error" class="alert alert-danger mb-3" role="alert">
          <strong>{{ $t('Error') }}! </strong>{{ $t('Order not found.') }}
        </div>

        <form action="#" method="post" @submit.prevent="onSubmit">
          <div class="form-group">
            <label for="email">{{ $t('Email') }} <span class="text-danger">*</span></label>
            <div class="input-group">
              <input
                id="email"
                v-model="form.email"
                type="email"
                name="email"
                class="form-control mt-2"
                placeholder="Email"
                required
                autofocus
              >
              <div class="input-group-append mt-2 d-none d-sm-block">
                <b-button type="button" @click="resendConfirmationEmail">
                  {{ $t('Resend Confirmation Email') }}
                </b-button>
              </div>
            </div>
          </div>

          <b-button type="button" class="mb-3 d-sm-none" @click="resendConfirmationEmail">
            {{ $t('Resend Confirmation Email') }}
          </b-button>

          <div class="form-group">
            <label for="order_number">{{ $t('Order number') }} <span class="text-danger">*</span></label>
            <input
              id="order_number"
              ref="orderNumberInput"
              v-model="form.order_number"
              type="text"
              name="order_number"
              class="form-control mt-2"
              placeholder="Order number"
              required
            >
          </div>

          <b-button type="submit" variant="danger" class="px-4">
            {{ $t('Track Order') }}
          </b-button>

          <b-button
            v-if="lastOrder"
            type="button"
            variant="primary"
            class="px-4 ml-3 btn-track-order"
            :to="trackLastOrderUrl"
          >
            {{ $t('View Last Order') }}
          </b-button>
        </form>
      </div>
    </div>
  </main>
</template>

<script>
import trackOrderMixin from '~/mixins/trackOrder'

export default {
  mixins: [trackOrderMixin]
}
</script>

<style>
.btn-track-order {
  border-radius: 0;
}
</style>
