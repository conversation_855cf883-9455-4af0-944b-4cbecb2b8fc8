<template>
  <main id="reportPage" class="py-4">
    <div class="container-xl">
      <breadcrumbs :bread-crumbs-list="breadCrumbsList" />
      <b-tabs
        v-model="currentTab"
        class="mt-3 campaign-info"
        no-nav-style
        nav-wrapper-class="border-bottom"
        nav-class="font-weight-500"
        content-class="mt-3"
      >
        <b-tab :title="$t('Intellectual Property Claim')">
          <form action="#" method="post" @submit.prevent="submit">
            <!-- campaign url -->
            <div class="form-group">
              <label for="campaignUrl">{{ $t('Campaign URL') }} <span class="text-danger">*</span></label>
              <input
                id="campaignUrl"
                v-model="campaignSlug"
                type="text"
                name="campaign-url"
                class="form-control mt-2"
                required
              >
            </div>

            <h4 class="border-bottom mb-3 pb-1">
              {{ $t('Your Contact Information') }}
            </h4>

            <div class="row">
              <!-- firstname -->
              <div class="col-12 col-md-6 form-group">
                <label for="firstname">{{ $t('First Name') }} <span class="text-danger">*</span></label>
                <input
                  id="firstname"
                  v-model="form1.firstName"
                  type="text"
                  name="firstname"
                  class="form-control mt-2"
                  required
                >
              </div>
              <!-- last name -->
              <div class="col-12 col-md-6 form-group">
                <label for="lastname">{{ $t('Last Name') }} <span class="text-danger">*</span></label>
                <input
                  id="lastname"
                  v-model="form1.lastName"
                  type="text"
                  name="lastname"
                  class="form-control mt-2"
                  required
                >
              </div>
              <!-- address1 -->
              <div class="col-12 col-md-6 form-group">
                <label for="address1">{{ $t('Address Line 1') }} <span class="text-danger">*</span></label>
                <input
                  id="address1"
                  v-model="form1.address1"
                  type="text"
                  name="address1"
                  class="form-control mt-2"
                  required
                >
              </div>
              <!-- address2 -->
              <div class="col-12 col-md-6 form-group">
                <label for="address2">{{ $t('Address Line 2') }} <span>({{ $t('Optional') }})</span></label>
                <input
                  id="address2"
                  v-model="form1.address2"
                  type="text"
                  name="address2"
                  class="form-control mt-2"
                >
              </div>
              <!-- state -->
              <div class="col-12 col-md-6 form-group">
                <label for="state">{{ $t('State') }} <span>({{ $t('Optional') }})</span></label>
                <input
                  id="state"
                  v-model="form1.state"
                  type="text"
                  name="state"
                  class="form-control mt-2"
                >
              </div>
              <!-- country -->
              <div class="col-12 col-md-6 form-group">
                <label for="country">{{ $t('Country') }} <span class="text-danger">*</span></label>
                <v-select
                  v-model="form1.country"
                  label="name"
                  class="country-chooser mt-2"
                  :options="countryArray"
                  :reduce="country => country.name"
                  :clearable="false"
                >
                  <template #search="{attributes, events}">
                    <input
                      class="vs__search"
                      :required="!form1.country"
                      v-bind="attributes"
                      v-on="events"
                    >
                  </template>
                </v-select>
              </div>
              <!-- email -->
              <div class="col-12 col-md-6 form-group">
                <label for="email">{{ $t('E-mail Address') }} <span class="text-danger">*</span></label>
                <input
                  id="email"
                  v-model="form1.email"
                  type="email"
                  name="email"
                  class="form-control mt-2"
                  required
                >
              </div>
              <!-- confirm email -->
              <div class="col-12 col-md-6 form-group">
                <label for="email-confirm">{{ $t('Confirm E-mail Address') }}<span class="text-danger">*</span></label>
                <input
                  id="email-confirm"
                  v-model="form1.emailConfirm"
                  type="email"
                  name="email-confirm"
                  class="form-control mt-2"
                  required
                >
              </div>
              <!-- confirm email -->
              <div class="col-12 col-md-6 form-group">
                <label for="phone">{{ $t('Phone Number') }} <span class="text-danger">*</span></label>
                <input
                  id="phone"
                  v-model="form1.phone"
                  type="text"
                  name="phone"
                  class="form-control mt-2"
                  required
                >
              </div>
            </div>

            <h4 class="border-bottom mb-3 pb-1">
              {{ $t('IP Claim Details') }}
            </h4>
            <div class="row">
              <!-- check -->
              <div class="col-12 col-md-6 form-group">
                <div>{{ $t('Are you the Rights Owner or an Agent') }}?</div>
                <div class="row mt-4">
                  <div class="col-12 col-sm-6 form-check">
                    <input
                      id="userType1"
                      v-model="form1.userType"
                      type="radio"
                      name="userType"
                      value="1"
                    >
                    <label class="ml-2 form-check-label" for="userType1">
                      {{ $t('Right Owner') }}
                    </label>
                  </div>
                  <div class="col-12 col-sm-6 form-check">
                    <input id="userType2" v-model="form1.userType" type="radio" name="userType" value="2">
                    <label class="ml-2 form-check-label" for="userType2">
                      {{ $t('Agent') }}
                    </label>
                  </div>
                </div>
              </div>
              <!-- ip Owner -->
              <div class="col-12 col-md-6 form-group">
                <label for="legalName">{{ $t('IP Owner') }} <span>({{ $t('Legal Name') }})</span> <span class="text-danger">*</span></label>
                <input
                  id="legalName"
                  v-model="form1.legalName"
                  type="text"
                  name="firstname"
                  class="form-control mt-2"
                  required
                >
              </div>
            </div>

            <div class="form-group">
              <label for="reason">{{ $t('The specific concern is') }} <span class="text-danger">*</span></label>
              <textarea id="reason" v-model="form1.additionalInfo.specificConcern" required class="form-control mt-2" rows="3" />
            </div>

            <div class="form-group">
              <label for="original">{{ $t('URL(s) to original work') }} <span class="text-danger">*</span></label>
              <textarea id="original" v-model="form1.additionalInfo.originalWork" class="form-control mt-2" required rows="3" />
            </div>

            <div class="form-group">
              <label for="additionalInfo">{{ $t('Additional Information (Optional)') }}</label>
              <textarea id="additionalInfo" v-model="form1.additionalInfo.otherInfo" class="form-control mt-2" rows="3" />
            </div>

            <!-- file -->
            <p class="mb-0 mt-3">
              <span class="font-weight-500">{{ $t('File Attachment') }} </span>
              <span>({{ $t('limit 5 file') }})</span>
            </p>
            <p v-if="fileErrorMessage" class="mb-0 text-danger">
              {{ $t(fileErrorMessage) }}
            </p>
            <b-form-file
              v-model="fileUpload"
              accept="image/*"
              :multiple="true"
              :placeholder="$t('No file chosen')"
              :browse-text="$t('Choose file')"
              @change="updateFile"
            />
            <p class="mb-0">
              {{ $t('fileUpload-text-1') }}.
            </p>
            <p>{{ $t('fileUpload-text-2') }}.</p>

            <h4 class="border-bottom my-3 pb-1">
              {{ $t('Statements') }}
            </h4>

            <p>"{{ $t('Statements-text-1') }}."</p>
            <p>"{{ $t('Statements-text-2') }}."</p>

            <div class="form-check my-3">
              <input id="statement" v-model="form1.acceptStatement" class="form-check-input mt-0" type="checkbox">
              <label class="form-check-label" for="statement">
                <strong>{{ $t('Statements-text-3') }}.</strong>
              </label>
            </div>
            <b-button type="submit" variant="danger" class="px-4" :disabled="!form1.acceptStatement || isFileLoading">
              <b-spinner v-if="isFileLoading" class="float-right" />
              {{ $t('Submit Claim') }}
            </b-button>
          </form>
        </b-tab>
        <b-tab :title="$t('Policy Violation')">
          <form action="#" method="post" @submit.prevent="submit">
            <div class="form-group">
              <label for="campaignUrl2">{{ $t('Campaign URL') }} <span class="text-danger">*</span></label>
              <input
                id="campaignUrl2"
                v-model="campaignSlug"
                type="text"
                name="campaign-url"
                class="form-control mt-2"
                required
              >
            </div>

            <div class="form-check my-3">
              <input id="hateSpeech" v-model="form2.hate" class="form-check-input mt-0" type="checkbox">
              <label class="form-check-label" for="hateSpeech">
                {{ $t('policy-text-1') }}
              </label>
            </div>

            <div class="form-check my-3">
              <input id="sexual" v-model="form2.sexual" class="form-check-input mt-0" type="checkbox">
              <label class="form-check-label" for="sexual">
                {{ $t('policy-text-2') }}
              </label>
            </div>

            <div class="form-check my-3">
              <input id="advocatesViolence" v-model="form2.violence" class="form-check-input mt-0" type="checkbox">
              <label class="form-check-label" for="advocatesViolence">
                {{ $t('policy-text-3') }}
              </label>
            </div>

            <div class="form-check my-3">
              <input id="misleading" v-model="form2.misleading" class="form-check-input mt-0" type="checkbox">
              <label class="form-check-label" for="misleading">
                {{ $t('policy-text-4') }}
              </label>
            </div>

            <div class="form-check my-3">
              <input id="advocatesIllegal" v-model="form2.illegal" class="form-check-input mt-0" type="checkbox">
              <label class="form-check-label" for="advocatesIllegal">
                {{ $t('policy-text-5') }}
              </label>
            </div>

            <b-button
              type="submit"
              variant="danger"
              class="px-4"
              :disabled="!(form2.hate||form2.sexual||form2.violence||form2.misleading||form2.illegal) || isFileLoading"
            >
              <b-spinner v-if="isFileLoading" class="float-right" />
              {{ $t('Report') }}
            </b-button>
          </form>
        </b-tab>
      </b-tabs>
    </div>
  </main>
</template>

<script>
import reportMixin from '~/mixins/report'

import Breadcrumbs from '~/themes/default/components/common/Breadcrumbs'

export default {
  components: {
    Breadcrumbs
  },
  mixins: [reportMixin]
}
</script>

<style scoped lang="scss">
label {
  margin-bottom: 0;
}

.nav-link {
  color: #c6c6c6;

  &.active {
    color: var(--primary-color);
  }

  @media (max-width: 767px) {
    padding: 0 5px;
  }
}

.nav-item:first-child {
  .nav-link {
    padding-left: 0;
  }
}

.form-check {
  display: flex;
  align-items: center;
}
</style>

<style lang="scss">
.country-chooser {
  .vs__dropdown-toggle {
    border-radius: unset !important;
    height: calc(1.5em + 0.5rem);
    padding: 0.2rem 0.5rem;
    font-size: 1.25rem;
    line-height: 1.5;
    width: 100%;
    font-weight: 400;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    .vs__selected,
    .vs__search {
      margin: 0;
      padding: 0;
    }

    &:hover,
    &:focus-within {
      border-color: var(--primary-color);
    }
  }

  &.vs--disabled{
    .vs__dropdown-toggle {
      background-color: #f8f8f8;
    }
  }
}
</style>
