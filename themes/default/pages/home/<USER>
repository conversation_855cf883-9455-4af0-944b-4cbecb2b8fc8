<template>
  <main id="homePage" :class="{'mt-5': !(banners&&banners.length)}">
    <carousel-banner v-if="banners&&banners.length" :banners="banners" />

    <div v-if="collectionBanners && collectionBanners.length" id="CollectionBanner" class="position-relative bg-white p-4">
      <div v-show="!isClient" class="mt-5 row row-cols-lg-5 flex-nowrap overflow-hidden justify-content-center">
        <collection-item v-for="(banner, index) in collectionBanners.filter((item, index)=>index<5)" :key="index" :banner="banner" class="col-6 col-md-4 col-lg" />
      </div>
      <div v-show="isClient" class="row">
        <client-only>
          <carousel-collection :banners="collectionBanners" />
        </client-only>
      </div>
    </div>

    <template v-for="(carouselProduct, index) in listCarousel" :id="carouselProduct.name">
      <section v-if="carouselProduct.products && carouselProduct.products.length" :id="carouselProduct.name" :key="index" class="container-xl position-relative bg-white mb-5 p-4">
        <h2 class="text-center text-uppercase">
          {{ carouselProduct.name }}
        </h2>
        <div v-show="!isClient" class="mt-5 row flex-nowrap overflow-hidden">
          <campaign-item v-for="(campaign, indexCampaign) in carouselProduct.products" :key="indexCampaign" class="col-6 col-md-4 col-lg-3" :campaign="campaign" />
        </div>
        <div v-show="isClient" class="row mt-5">
          <client-only>
            <carousel-campaign :campaigns="carouselProduct.products" />
          </client-only>
        </div>
      </section>
    </template>

    <client-only>
      <div v-if="cartTotalQuantity" class="opacity-9 position-fixed bottom-0 left-0 w-100 p-2 bg-white border z-index-100 d-md-none d-flex justify-content-between">
        <div>
          <div class="font-weight-500 font-sm">
            {{ $formatPrice($totalPrice($store.state.cart.products)) }}
          </div>
          <div><small>{{ cartTotalQuantity }} {{ cartTotalQuantity === 1 ? $t('item') : $t('items') }}</small></div>
        </div>
        <nuxt-link :to="localePath('/cart')">
          <b-button
            block
            variant="custom"
            class="font-weight-500 py-2-5 text-uppercase"
          >
            <span>{{ $t('View cart') }}</span>
          </b-button>
        </nuxt-link>
      </div>
    </client-only>
  </main>
</template>

<script>
import homeMixin from '~/mixins/home'

import CarouselCampaign from '~/themes/default/components/products/CarouselCampaign'
import CampaignItem from '~/themes/default/components/products/CampaignItem'
import CarouselBanner from '~/themes/default/components/home/<USER>'
import CarouselCollection from '~/themes/default/components/home/<USER>'
import CollectionItem from '~/themes/default/components/home/<USER>/CollectionItem'

export default {
  components: {
    CarouselBanner,
    CarouselCampaign,
    CampaignItem,
    CarouselCollection,
    CollectionItem
  },
  mixins: [homeMixin]
}
</script>

<style lang="scss">
#homePage{
  h2 {
    font-size: 24px;
    font-weight: 600;
  }
  .homepage-footer{
    @media (min-width:1300px) {
      margin-top: -80px;
      padding-top: 100px;
    }
  }
}

#CollectionBanner{
  .container-xl{
    top: -40px;
    @media (max-width:1300px) {
      top: 0px;
    }
  }

}
</style>
