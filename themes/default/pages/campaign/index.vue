<template>
  <main id="campaignPage" class="container-xl py-4">
    <div itemscope itemtype="http://schema.org/Product">
      <meta itemprop="brand" :content="campaignData.store_name">
      <meta itemprop="name" :content="campaignData.name">
      <meta itemprop="description" :content="campaignDescription">
      <meta itemprop="productID" :content="campaignData.id">
      <meta itemprop="url" :content="`https://${$store.state.storeInfo.domain}/${campaignSlug}`">
      <meta itemprop="image" :content="thumbnail">
      <div v-if="reviewSummary.summary.review_count > 0 && reviewDisplay !== 'disable'" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
        <meta itemprop="ratingValue" :content="reviewSummary.summary.average_rating">
        <meta itemprop="reviewCount" :content="reviewSummary.summary.review_count">
      </div>
      <div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
        <link itemprop="availability" href="http://schema.org/InStock">
        <link itemprop="itemCondition" href="http://schema.org/NewCondition">
        <meta itemprop="price" :content="currentProduct.price">
        <meta itemprop="priceCurrency" :content="currentProduct.currency_code">
      </div>
    </div>
    <h1 class="title-campaign md-h2 mb-0 mb-md-3 sen-text-truncate">
      {{ title }}
    </h1>
    <div class="campaign-info-box row" data-test-id="campaign-info-box">
      <div class="col-12 col-md-6 col-lg-7">
        <images-box
          id="campaign-images-box"
          ref="imageBox"
          :filter-images="filterImages"
          :current-product="currentProduct"
          :campaign="campaignData"
          :option-list="optionList"

          :current-options="currentOptions"
          :current-design="currentDesign"
          :filter-designs="filterDesigns"
          :is-show-design="isShowDesign"

          :custom-image="customImage"
          @updateShowDesign="updateShowDesign"
        />
        <embroider-warning v-if="currentProduct && currentProduct.full_printed === 4" />
        <client-only>
          <div class="d-none d-md-block">
            <share-box :campaign="campaignData" class="mb-4 d-flex justify-content-between" />
            <!-- promotion -->
            <promotions-list
              v-if="promotionsList && promotionsList.length"
              :promotions-list="promotionsList"
            />
          </div>
          <description-box
            v-if="!promotionsList && bundleDiscount.products.length"
            :data-description="dataDescription"
            :data-product-detail="dataProductDetail"
          />
        </client-only>
      </div>

      <div class="col-12 col-md-6 col-lg-5">
        <general-info
          :is-show-author="false"
          :campaign="campaignData"
          :current-product="currentProduct"
          :option-list="optionList"
          :current-options="currentOptions"
        />
        <div class="d-flex flex-wrap gap-2 d-md-block justify-content-between">
          <rating
            class="order-2"
            :review-display="reviewDisplay"
            :average-rating="reviewSummary.summary.average_rating"
            :review-count="reviewSummary.summary.review_count"
          />
          <price
            :current-product="currentProduct"
            :current-price="currentPrice"
            :current-old-price="currentOldPrice"
          />
        </div>
        <product-list
          v-if="productList && productList.length"
          :campaign="campaignData"
          :products="productList"
          :current-product="currentProduct"
          :current-options="currentOptions"
          :is-dropdown-type="isCampaignDropdownType"
          @updateOption="updateOption"
        />

        <option-list
          v-if="optionList"
          :option-list="optionList"
          :current-options="currentOptions"
          :current-product="currentProduct"
          @click.native="$tracking.customTracking({event: 'interact', action: 'option_click'})"
          @updateOption="updateOption"
          @openSizeGuild="openSizeGuild(currentProduct)"
        />

        <client-only>
          <personalize-custom-option
            v-if="campaignData.personalized === 0 && currentProduct && currentProduct.template_custom_options"
            :campaign="campaignData"
            :current-product="currentProduct"
            :current-options="currentOptions"
            :common-options="currentProduct.common_options"
            :custom-options="currentProduct.template_custom_options"
            :is-loaded-custom-option="isLoadedCustomOption"
            @updateCustomizeOptions="value => customerCustomOptions = value"
            @isLoadingAddToCart="value => isLoadingAddToCart = value"
            @updateCustomOptionGroupNumbers="value => customOptionGroupNumbers = value"
            @updateCustomOptionGroupFee="updateCustomOptionGroupFee"
            @updateAllowCustomCampaign="updateAllowCustomCampaignAddToCart"
            @click.native="$tracking.customTracking({event: 'interact', action: 'personalize_click'})"
          />

          <personalize-custom
            v-if="campaignData.personalized === 1 && filterDesigns && filterDesigns.length"
            class="d-none d-md-block"
            :option-error="optionError"

            :custom-text-list="customTextList"
            :custom-image-list="customImageList"
            :file-upload="fileUpload"
            :is-upload-file="isUploadFile"

            :custom-design-array="customDesignArray"
            :custom-design-type="customDesignType"
            :custom-design="customDesign"
            :loading-change-custom-design="loadingChangeCustomDesign"

            @click.native="$tracking.customTracking({event: 'interact', action: 'personalize_click'})"
            @changeCurrentDesign="changeCurrentDesign"
            @updateCustomDesign="updateCustomDesign"
            @updateCanvasData="updateCanvasData"
            @updateDesignQuery="updateDesignQuery"
            @editDesign="$refs.modalEditCustomImage.isShowModal = true"
          />

          <personalize-pb
            v-if="campaignData.personalized === 2 && filterDesigns && filterDesigns.length"
            :campaign="campaignData"
            :current-design="currentDesign"
            @click.native="$tracking.customTracking({event: 'interact', action: 'personalize_click'})"
            @scrollToImage="scrollToImage"
            @changeCurrentDesign="changeCurrentDesign"
          />

          <personalize-custom-option
            v-if="(campaignData.personalized === 3 && campaignData.options) || ((campaignData.personalized === 3 || campaignData.system_type === 'custom' || campaignData.system_type === 'mockup') && campaignData.common_options)"
            :campaign="campaignData"
            :current-product="currentProduct"
            :common-options="campaignData.common_options"
            :custom-options="campaignData.options"
            :is-loaded-custom-option="isLoadedCustomOption"
            @click.native="$tracking.customTracking({event: 'interact', action: 'personalize_click'})"
            @updateCustomizeOptions="value => customerCustomOptions = value"
            @isLoadingAddToCart="value => isLoadingAddToCart = value"
            @updateCustomOptionGroupNumbers="value => customOptionGroupNumbers = value"
            @updateCustomOptionGroupFee="updateCustomOptionGroupFee"
            @updateAllowCustomCampaign="updateAllowCustomCampaignAddToCart"
          />

          <quantity-box
            :quantity="quantity"
            :current-variant="currentVariant"
            :is-loading-add-to-cart="isLoadingAddToCart"
            :loading-change-custom-design="loadingChangeCustomDesign"
            :allow-custom-campaign-add-to-cart="allowCustomCampaignAddToCart"
            @updateQuantity="updateQuantity"
            @addToCart="addToCart"
          />

          <product-stats
            v-if="productStats"
            :product-stats="productStats"
          />
          <countdown v-if="$store.state.storeInfo.store_type !== 'google_ads' && campaignData && campaignData.show_countdown && (campaignData.end_time || campaignData.show_countdown > 1)" :show-countdown="campaignData.show_countdown" :end-time="campaignData.end_time" />

          <div v-if="$store.state.storeInfo.store_type !== 'google_ads' && $store.state.storeInfo.show_payment_button" class="safe-badge my-4 px-2 pt-3 pb-2">
            <div class="title">
              {{ $t('GUARANTEED:') }}<strong class="pl-1">{{ $t('SAFE CHECKOUT') }}</strong>
            </div>
            <img src="images/safe_badge.webp" alt="Safe Badge">
          </div>
          <template v-if="$store.state.storeInfo && !$store.state.storeInfo.disable_promotion">
            <bundle-box
              v-if="bundleDiscount && bundleDiscount.products && bundleDiscount.products.length"
              class="mt-3"
              :current-variant="currentVariant"
              :filter-images="filterImages"
              :current-product="currentProduct"
              :current-options="currentOptions"
              :bundle-discount="bundleDiscount"
              :total-bundle-discount="totalBundleDiscount"
              :save-bundle-discount="saveBundleDiscount"
              :bundle-product-variant="bundleProductVariant"
              :force-update="bundleForceUpdate"
              :is-loading-add-to-cart="isLoadingAddToCart"
              :allow-custom-campaign-add-to-cart="allowCustomCampaignAddToCart"
              :current-price="$convertPrice(currentPrice, currentProduct.currency_code)"
              @click.native="$tracking.customTracking({event: 'interact', action: 'bundle_box'})"
              @selectBundleVariant="value=>bundleProductVariant = value"
              @updateBundleProduct="updateBundleProduct"
              @openBundleProduct="product => {openBundleProduct(product); typeSubmit = 0}"
              @addToCart="addToCart"
              @resetBundleProduct="productId=>{resetBundleProduct([productId])}"
            />
            <promotions-list
              v-if="promotionsList && promotionsList.length"
              class="d-md-none mt-4"
              :promotions-list="promotionsList"
            />
          </template>
          <description-box
            v-if="(promotionsList && !bundleDiscount.products.length) || ($store.state.storeInfo && $store.state.storeInfo.disable_promotion)"
            :data-description="dataDescription"
            :data-product-detail="dataProductDetail"
            @click.native="$tracking.customTracking({event: 'interact', action: 'description_box'})"
          />
        </client-only>
      </div>

      <client-only>
        <div
          v-if="!!promotionsList === !!bundleDiscount.products.length && $store.state.storeInfo && !$store.state.storeInfo.disable_promotion"
          class="col-12"
        >
          <description-box
            :data-description="dataDescription"
            :data-product-detail="dataProductDetail"
          />
        </div>
      </client-only>
    </div>

    <client-only>
      <div
        v-if="campaignData"
        id="bottomButton"
        ref="bottomButton"
        class="bottom-button left-0 w-100 p-2 bg-white border z-index-100 d-md-none personalize"
        :style="{'transform': `translateY(${isHiddenBottomButton ? campaignData.personalized === 1 && selectedCustom ? 65 : 72 : 0}px)`}"
      >
        <personalize-custom-bottom-input
          v-if="campaignData.personalized === 1 && selectedCustom"
          :option-error="optionError"
          :selected-custom="selectedCustom"
          :custom-design-type="customDesignType"
          :custom-item-list="customItemList"
          :custom-design-array="customDesignArray"
          :custom-design="customDesign"
          :loading-change-custom-design="loadingChangeCustomDesign"
          :is-upload-file="isUploadFile"
          :file-upload="fileUpload"

          @focusInput="$refs.bottomButton.classList.add('fixfixed')"
          @blurInput="$refs.bottomButton.classList.remove('fixfixed')"

          @updateSelectedCustom="value=> selectedCustom = value"
          @changeCurrentDesign="changeCurrentDesign"
          @updateDesignQuery="updateDesignQuery"
          @updateCustomDesign="updateCustomDesign"
          @updateCanvasData="updateCanvasData"
          @editDesign="$refs.modalEditCustomImage.isShowModal = true"
        />

        <b-button
          block
          size="lg"
          variant="custom"
          class="position-relative font-weight-500 py-2-5 text-uppercase"
          :disabled="isLoadingAddToCart || loadingChangeCustomDesign || !!(currentVariant && currentVariant.out_of_stock) "
          @click="addToCart(1)"
        >
          <loading-dot v-if="isLoadingAddToCart || loadingChangeCustomDesign" variant="light" />
          <span v-else-if="(currentVariant && currentVariant.out_of_stock)">{{ $t('Out of stock') }}</span>
          <span v-else> <i class="font-2xl icon-sen-cart-plus mr-1" />{{ $t('Add to cart') }}</span>
        </b-button>
      </div>
      <div v-if="campaignData && campaignData.collections && campaignData.collections.length > 0 && (!$store.state.storeInfo || !$store.state.storeInfo.disable_related_collection)" class="mt-3 filter-color mt-1 d-flex align-items-center overflow-auto">
        <span class="min-w-max-content d-none d-md-block font-weight-500">{{ $t('Related collections') }}: </span>
        <collection-item
          v-for="(collection, index) in campaignData.collections"
          :key="index"
          :value="collection.name"
          :slug="`/collection/${collection.slug}`"
        />
      </div>

      <template v-if="(!$store.state.storeInfo || !$store.state.storeInfo.disable_related_product) && related && related.length">
        <h4 class="mt-3 text-center text-uppercase">
          {{ $t('You may also like') }}
        </h4>
        <carousel-campaign class="mb-4" :campaigns="related" />
      </template>

      <product-review
        v-if="reviewDisplay !== 'disable'"
        :review-summary="reviewSummary"
        :reviews="reviews"
        :campaign-name="campaignData.name"
        :loading="loading"
        :filter="filter"
        @changePage="changePage"
        @changeFilter="changeFilter"
      />
      <share-box :campaign="campaignData" class="mt-5 d-md-none d-flex flex-column justify-content-center align-items-center text-center" />
    </client-only>

    <!-- report -->
    <h6 class="my-4 text-center">
      <nuxt-link class="text-secondary" :to="localePath(`/report?campaign=${campaignData.slug}`)">
        <span> <i class="icon-sen-alert-outline" /> </span>
        <span> {{ $t('Report a policy violation') }}?</span>
      </nuxt-link>
    </h6>

    <client-only>
      <!-- <div v-if="productsSimilar && productsSimilar.length > 0" class="mt-3 filter-color mt-1 d-flex align-items-center overflow-auto">
        <span class="min-w-max-content d-none d-md-block font-weight-500">{{ $t('Similar products') }}: </span>
        <collection-item
          v-for="(product, index) in productsSimilar"
          :key="index"
          :value="product.name"
          :slug="`/${product.slug}`"
        />
      </div> -->
      <modal-add-to-cart
        ref="modalAddToCart"
        :filter-images="filterImages"
        :current-product="currentProduct"
        :quantity="quantity"
        :cart-total-quantity="cartTotalQuantity"
        :current-options="currentOptions"
        :related-cart="relatedCart"
        :option-list="optionList"
        :title="title"
        :upload-file-design-url="uploadFileDesignUrl"
        :is-loading-upload-image="isLoadingUploadImage"
        :campaign="campaignData"
      />

      <modal-select-size
        ref="modalSelectSize"
        :size-guide-list="sizeGuideList"
        :option-list="optionList"
        @openSizeGuild="openSizeGuild(currentProduct)"
        @selectSize="value=>updateOption({size: value}, selectSizeSubmit)"
      />

      <modal-confirm-design
        ref="modalConfirmDesign"
        :img-urls="uploadFileDesignUrl"
        :is-loading-add-to-cart="isLoadingAddToCart"
        @addToCart="addToCart"
      />

      <modal-bundle-product
        ref="modalBundleProduct"
        :current-product="currentBundleDiscount"
        @updateBundleProduct="updateBundleProduct"
      />

      <modal-bundle-custom
        ref="modalBundleCustomText"
        :current-product="currentBundleDiscount"
        :custom-options="currentCustomOptions"
        :common-options="currentCommonOptions"
        @updateBundleProduct="updateBundleProduct"
        @updateCustomOptionGroupFee="updateCustomOptionGroupFee"
        @saveBundleCustom="typeSubmit === 3 ? addToCart() : ''"
      />

      <modal-edit-custom-image
        ref="modalEditCustomImage"
        :design="currentDesign"
        :custom-image="customImage"
        :is-image-low-quanlity="isImageLowQuanlity"
        @openConfirmQuantityImage="isConfirmQuantityImage? '' : isShowModalConfirmQuantityImage = true"
      />

      <b-modal
        v-model="isShowModalConfirmQuantityImage"
        centered
        size="lg"
        body-class="d-flex flex-wrap justify-content-center"
        hide-header
        hide-footer
      >
        <div class="col-12 py-2 text-center">
          <h4>{{ $t('Image resolution is low that cause bad print quality.') }}</h4>
          <p>{{ $t('Do you want to upload higher resolution image (minimum size is heightxwidth px)?', {height: Math.round(customImage && customImage.height * customImage.scaleY / 2) , width: Math.round(customImage && customImage.width * customImage.scaleX / 2)}) }}</p>
        </div>
        <div class="col-12 col-md-6">
          <b-button
            block
            variant="primary"
            class="border-radius-none mb-3"
            @click="confirmQuantityImage(true)"
          >
            {{ $t('Upload new image') }}
          </b-button>
        </div>
        <div class="m-0 col-12 col-md-6">
          <b-button
            block
            variant="secondary"
            class="border-radius-none mb-3"
            @click="confirmQuantityImage(false)"
          >
            {{ $t('Keep old image') }}
          </b-button>
        </div>
      </b-modal>
    </client-only>
  </main>
</template>

<script>
import campaignMixin from '~/mixins/campaign'

import GeneralInfo from '~/themes/default/components/campaign/GeneralInfo'
import Rating from '~/themes/default/components/campaign/Rating'
import Price from '~/themes/default/components/campaign/Price'
import ProductList from '~/themes/default/components/campaign/ProductList'
import OptionList from '~/themes/default/components/campaign/OptionList'
import PersonalizeCustom from '~/themes/default/components/campaign/PersonalizeCustom'
import PersonalizePb from '~/themes/default/components/campaign/PersonalizePb'
import PersonalizeCustomOption from '~/themes/default/components/campaign/PersonalizeCustomOption'
import QuantityBox from '~/themes/default/components/campaign/QuantityBox'
import ProductStats from '~/themes/default/components/campaign/ProductStats'
import BundleBox from '~/themes/default/components/campaign/BundleBox'
import DescriptionBox from '~/themes/default/components/campaign/DescriptionBox'
import ImagesBox from '~/themes/default/components/campaign/ImagesBox'
import PersonalizeCustomBottomInput from '~/themes/default/components/campaign/PersonalizeCustom/BottomInput'

import ModalBundleProduct from '~/themes/default/components/campaign/modal/ModalBundle'
import ModalBundleCustom from '~/themes/default/components/campaign/modal/ModalBundleCustom'
import ModalEditCustomImage from '~/themes/default/components/campaign/modal/ModalEditCustomImage'
import ModalAddToCart from '~/themes/default/components/campaign/modal/ModalAddToCart'
import ModalSelectSize from '~/themes/default/components/campaign/modal/ModalSelectSize'
import ModalConfirmDesign from '~/themes/default/components/campaign/modal/ModalConfirmDesign'

import CollectionItem from '~/themes/default/components/common/CollectionItem'
import PromotionsList from '~/themes/default/components/common/PromotionsList'
import ShareBox from '~/themes/default/components/common/ShareBox'
import ProductReview from '~/themes/default/components/product-review/ProductReviewShow'
import Countdown from '~/themes/default/components/common/Countdown'

import CarouselCampaign from '~/themes/default/components/products/CarouselCampaign'
import EmbroiderWarning from '~/components/EmbroiderWarning'

export default {
  name: 'CampaignInfo',
  components: {
    GeneralInfo,
    Rating,
    Price,
    ProductList,
    OptionList,
    ImagesBox,
    PersonalizeCustom,
    PersonalizePb,
    PersonalizeCustomOption,
    QuantityBox,
    ProductStats,
    BundleBox,
    DescriptionBox,
    PersonalizeCustomBottomInput,

    ModalAddToCart,
    ModalSelectSize,
    ModalConfirmDesign,
    ModalBundleProduct,
    ModalBundleCustom,
    ModalEditCustomImage,

    PromotionsList,
    ShareBox,
    CollectionItem,
    ProductReview,
    Countdown,
    CarouselCampaign,
    EmbroiderWarning
  },
  mixins: [campaignMixin],
  data () {
    return {
      isHiddenBottomButton: true,
      imageClicked: null,
      color: null,
    }
  },
  mounted () {
    document.addEventListener('scroll', this.checkBottomButton)
  },
  destroyed () {
    document.removeEventListener('scroll', this.checkBottomButton)
  },
  methods: {
    checkBottomButton () {
      if (window.height >= 768) { return }
      const buttonRect = document.getElementById('addToCart')?.getBoundingClientRect()
      this.isHiddenBottomButton = (buttonRect?.top || 0) >= 0
    }
  }
}

</script>
<style lang="scss">
@media (max-width: 767px) {
  .scroll-up #campaignPage {
    margin-top: 1.5rem;
  }

  .sen-text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.viewcart-button {
  border: solid 1px black !important;
}

.size-select-img {
  width: 100%;
  max-width: 300px;
}

.product-add-to-cart-item {
  img {
    width: 80px;
    height: 100px;
  }
}

.title-campaign:first-letter {
  text-transform: capitalize
}

#campaignPage{
  transition: margin 500ms;

  #campaign-images-box {

    .list-images {
      overflow-y: auto;
      min-width: 105px;
      max-height: 500px;

      .campaign-thumb-image {

        img,
        .spinner {
          border: 1px solid transparent;
          width: 80px;
          height: 100px;
        }

        &.active,
        &:hover {
          img,
          .spinner {
            border-color: var(--primary-color)
          }
        }
      }
    }

    .thumb {
      position: relative;
      width: 100%;
      height: 0px;
      padding-top: 125%;
      overflow: hidden;
      background-color: transparent;

        &>picture>img,
        .spinner,
        .zoom-on-hover {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 100%;
          object-fit: contain;
          transition: transform 0.2s;
        }
    }

    #viewBox{
      position: relative;
      width: 100%;

      @media (min-width: 992px) {
        max-width: calc(100% - 105px)!important;
      }
    }

    .thumb-canvas {
      width: 100%;
    }
  }
}

#bottomButton {
  transition: all .5s;
  position: fixed;
  bottom: 0px;

  .form-group > div {
    display: flex;
  }
}

@media only screen and (min-width : 375px) {
  .bodyfixfixed .bottom-button.fixfixed{
    transform: translate(0px, -250px);
  }
}

@media only screen and (min-width : 390px) {
  .bodyfixfixed .bottom-button.fixfixed{
    transform: translate(0px, -250px);
  }
}

@media only screen and (min-width : 410px) {
  .bodyfixfixed .bottom-button.fixfixed{
    transform: translate(0px, -259px);
  }
}

@media only screen and (min-width : 420px) {
  .bodyfixfixed .bottom-button.fixfixed{
    transform: translate(0px, -262px);
  }
}

@media only screen and (min-width : 768px) {
  .bodyfixfixed .bottom-button.fixfixed{
    transform: translate(0px, -400px);
  }
}

@media only screen and (min-width : 1024px) {
  .bodyfixfixed .bottom-button.fixfixed{
    transform: translate(0px, -500px);
  }
}

.next-preview-customtext-btn {
  i {
    line-height: 2.9rem;
    font-size: 1.9rem;
  }
}

.form-control-file-bottom-button {
  position: relative;
  width: 100%;
  height: 3rem;

&.require-input label div{
    border-color: red;
  }

  input {
    display: none;
  }

  label {
    height: 3rem;
    font-size: 0.875rem;
    background: white;
    cursor: pointer;
    margin-bottom: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    div {
      padding: 12px;
      border: solid 1px #ced4da;
      color: #8F8F8F;
    }

    .button-box {
      background-color: #E9ECEF;
      border-left: none;
    }

    &.disabled {
      cursor: not-allowed;
      background-color: rgb(241, 241, 241);
    }
  }
}
.safe-badge img {
  max-width: 100%;
  padding: 10px;
}
.safe-badge .title {
  background: #ffffff;
  font-size: 1rem;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -1.1rem;
  text-align: center;
  width: max-content;
  padding: 5px 23px;
}
.safe-badge {
  border: 2px solid #ced4da;
  position: relative;
}
</style>
