<template>
  <main id="custom-page" class="py-4">
    <div class="container-xl">
      <div class="content-page content-text mt-6 mb-6">
        <div class="row">
          <div class="col-12">
            <h1 class="h3 page-title mb-3">
              {{ customPage.title }}
            </h1>
            <div class="mb-3" v-html="customPage.description" />
            <div class="mb-3" v-html="customPage.content" />
          </div>
        </div>
        <div class="row mb-3">
          <div class="col">
            <div
              class="mt-3 filter-color mt-1 d-flex align-items-center overflow-auto"
            />
            <template v-if="customPage.link_json && customPage.link_json.length">
              <collection-item
                v-for="(collection, index) in customPage.link_json"
                :key="index"
                :value="collection.name"
                :slug="collection.slug"
              />
            </template>
          </div>
        </div>
        <div
          v-if="customPage.data_json && customPage.data_json.length"
          class="row"
        >
          <campaign-item
            v-for="(product, pIdx) in customPage.data_json"
            :key="pIdx"
            :campaign="product"
            class="col-6 col-lg-3 mb-3"
          />
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import customPageMixin from '~/mixins/customPage'
import CampaignItem from '~/themes/default/components/products/CampaignItem'
import CollectionItem from '~/themes/default/components/common/CollectionItem'

export default {
  components: { CampaignItem, CollectionItem },
  mixins: [customPageMixin]
}
</script>
