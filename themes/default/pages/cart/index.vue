<template>
  <main id="cartPage" data-test-id="cart-page" :style="spriteUrl" class="container-xl py-4">
    <h1 class="h4 text-capitalize mt-4 mt-sm-0 mb-3 text-center text-md-left">
      {{ $t('Your shopping bag') }}
    </h1>
    <client-only>
      <div v-if="products&&products.length" class="row">
        <div class="col-12 col-xl-8">
          <div class="d-none d-lg-flex row font-weight-500 text-uppercase text-center">
            <div class="col-7">
              {{ $t('Product') }}
            </div>
            <div class="col-5 row text-center">
              <div class="col-3">
                {{ $t('Price') }}
              </div>
              <div class="col-6">
                {{ $t('Quantity') }}
              </div>
              <div class="col-3">
                {{ $t('Total') }}
              </div>
            </div>
          </div>
          <product-cart-item
            v-for="(cartItem, index) in products"
            :key="index"
            :index="index"
            :cart-item="cartItem"
            @remove-item="handleRemoveItem"
          />
          <div v-if="bundleDiscountProduct && $store.state.storeInfo && !$store.state.storeInfo.disable_promotion && getMoreBundleProduct()" class="d-flex justify-content-center align-items-center mt-3">
            <div class="col px-0">
              <p
                class="bundle-text font-weight-500 text-info text-center"
                style="cursor: pointer;"
                @click="addBundleProduct()"
              >
                + {{ $t('Add a matching') }}
                <span class="text-capitalize">
                  {{ bundleDiscountProduct.campaign_name || bundleDiscountProduct.slug && bundleDiscountProduct.slug.replaceAll('-', ' ') }}
                </span>
              </p>
              <div class="d-flex flex-wrap justify-content-center align-items-center mt-2">
                <span v-if="bundleDiscountProduct.currentVariant && bundleDiscountProduct.currentVariant.out_of_stock" class="text-danger font-weight-500">
                  {{ $t('Out of stock') }}
                </span>
                <span v-else class="text-center">
                  <del v-if="$store.state.storeInfo.store_type !== 'google_ads'" class="px-1 text-gray">
                    {{ $formatPrice((bundleDiscountProduct.currentVariant && bundleDiscountProduct.currentVariant.price) || bundleDiscountProduct.price, bundleDiscountProduct.currency_code) }}
                  </del>
                  <span class="px-0 font-weight-500">{{ $formatPrice(((bundleDiscountProduct.currentVariant && bundleDiscountProduct.currentVariant.price) || bundleDiscountProduct.price) * (100 -bundleDiscount.promotion.discount_percentage) / 100, bundleDiscountProduct.currency_code) }}</span>
                </span>
                <b-button
                  variant="custom"
                  class="px-1 mx-1 min-w-max-content px-3"
                  size="sm"
                  @click="addBundleProduct()"
                >
                  <i class="font-2xl icon-sen-cart-plus mr-1" />
                  {{ $t('Add to cart') }}
                </b-button>
              </div>
            </div>
            <div class="col-auto bundleProduct-img px-0">
              <product-img
                class="cursor-pointer"
                :path="bundleDiscountProduct.thumb_url"
                :alt="bundleDiscountProduct.name"
                :color="bundleDiscountProduct.currentOptions && bundleDiscountProduct.currentOptions.color ? $colorVal(bundleDiscountProduct.currentOptions.color) : ''"
                @click.native="$store.commit('changeViewImagePath', bundleDiscountProduct.thumb_url)"
              />
            </div>
          </div>
          <promotions-list v-if="promotionsList && promotionsList.length && $store.state.storeInfo && !$store.state.storeInfo.disable_promotion" class="px-md-5 mt-4" :promotions-list="promotionsList" :show-item="1" />
        </div>
        <div class="col-12 col-xl-4 mt-5 mt-xl-0">
          <p class="text-uppercase font-weight-500 border-bottom pb-3">
            {{ $t('Subtotal') }} <span class="text-custom float-right">{{ $formatPrice($totalPrice(products, true) - $totalBundleDiscount(products) + $totalDynamicBaseCostIndex(products)) }}</span>
          </p>
          <p>
            <small class="font-weight-600">{{ $t('Total quantity') }}</small> <small class="font-weight-600 float-right">{{ totalQuantity || 0 }} {{ totalQuantity ===1 ? $t('Item') : $t('Items') }}</small>
          </p>
          <div>
            <div class="d-flex align-items-center justify-content-between">
              <div style="flex-shrink: 0;">
                <small class="font-weight-600">{{ $t('Shipping & fees') }}</small>
              </div>
              <div class="ml-2">
                <small class="font-italic">{{ $t('Calculated at checkout') }}</small>
              </div>
            </div>
          </div>
          <b-form @submit.prevent="$store.dispatch('order/createOrder', {email})">
            <b-form-input
              id="email"
              v-model="email"
              size="lg"
              type="email"
              pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
              class="font-italic font-small"
              :placeholder="$t('Enter e-mail for express checkout')"
            />
            <b-button
              block
              size="lg"
              variant="custom"
              type="submit"
              class="d-none d-xl-block mt-3 font-weight-500"
              data-test-id="button-proceed-to-checkout"
              dusk="proceed-to-checkout-button"
              :disabled="loading"
            >
              <loading-dot v-if="loading" variant="light" />
              <span v-else class="d-flex align-items-center justify-content-center" style="gap: 6px;">
                <icon-lock style="width: 22px; height: 22px; margin-bottom: 3px;" />
                {{ $t('Proceed to checkout') }}
              </span>
            </b-button>
          </b-form>

          <payment-gateway-accepted />
        </div>
      </div>
      <div v-else class="text-center">
        <h6 class="py-3">
          {{ $t('Your cart is empty') }} :(
        </h6>
        <nuxt-link :to="localePath('/')">
          <b-button variant="outline-custom" class="border">
            {{ $t('Continue Shopping') }}
          </b-button>
        </nuxt-link>
      </div>
      <div v-if="relatedCart&&relatedCart.length" class="my-5">
        <h3 class="text-center text-uppercase">
          {{ $t('Frequently bought together') }}
        </h3>
        <carousel-campaign :campaigns="relatedCart" />
      </div>
      <div
        v-if="products&&products.length"
        class="w-100 position-fixed bottom-0 left-0 p-2 border bg-white d-xl-none z-index-100"
      >
        <b-button
          block
          size="lg"
          variant="custom"
          class="font-weight-500 py-2-5"
          :disabled="loading"
          data-test-id="button-proceed-to-checkout"
          @click="$store.dispatch('order/createOrder')"
        >
          <loading-dot v-if="loading" variant="light" />
          <span v-else class="d-flex align-items-center justify-content-center" style="gap: 6px;">
            <icon-lock style="width: 22px; height: 22px; margin-bottom: 3px;" />
            {{ $t('Proceed to checkout') }}
          </span>
        </b-button>
      </div>
    </client-only>

    <modal-bundle-custom
      v-if="bundleDiscountProduct"
      ref="modalBundleCustomText"
      :current-product="bundleDiscountProduct"
      :custom-options="currentCustomOptions"
      :common-options="currentCommonOptions"
      @updateBundleProduct="updateBundleProduct"
      @saveBundleCustom="bundleDiscountProduct.personalized === 1 || bundleDiscountProduct.personalized === 2 ? showConfirmDesign(): addBundleProduct(true)"
    />

    <modal-confirm-design
      v-if="bundleDiscountProduct"
      ref="modalConfirmDesign"
      :img-urls="uploadFileDesignUrl"
      :is-loading-add-to-cart="isLoadingAddToCart"
      @addToCart="addBundleProduct(true)"
    />
  </main>
</template>

<script>
import cartMixin from '~/mixins/cart'

import productCartItem from '~/themes/default/components/cart/productCartItem'
import CarouselCampaign from '~/themes/default/components/products/CarouselCampaign'

import PromotionsList from '~/themes/default/components/common/PromotionsList'

import ModalBundleCustom from '~/themes/default/components/campaign/modal/ModalBundleCustom'
import ModalConfirmDesign from '~/themes/default/components/campaign/modal/ModalConfirmDesign'
import paymentGatewayAccepted from '~/components/paymentGatewayAccepted.vue'
export default {
  name: 'Cart',
  components: {
    productCartItem,
    CarouselCampaign,
    PromotionsList,
    ModalBundleCustom,
    ModalConfirmDesign,
    paymentGatewayAccepted
  },
  mixins: [cartMixin],
  computed: {
    spriteUrl () {
      return `--sprite-url: url("${this.$config.publicPath}/images/logo_cart_sprite.webp")`
    },
    loading () {
      return this.isLoadingCheckout || this.isLoadingUploadImage
    }
  }
}
</script>
<style scoped></style>

<style lang="scss">
#cartPage {
  .bundleProduct-img {
    img {
      height: 100px;
      width: 80px;
    }
  }

  @media (min-width: 768px) {
    .bundle-text {
      font-size: 24px;
    }

    .bundleProduct-img {
      img {
        height: 120px;
        width: 96px;
      }
    }
  }
}
</style>
