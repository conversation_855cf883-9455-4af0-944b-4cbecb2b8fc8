<template>
  <main class="container-xl py-4">
    <div class="error-page mt-5 pt-5 text-center">
      <h2 v-if="statusCode === 404">
        {{ $t('Page not found!') }}
      </h2>
      <h2 v-else>
        {{ $t('An error occurred') }}
      </h2>

      <p class="mt-3">
        {{ $t('Looks like you followed a bad link.') }}
        <nuxt-link class="text-custom" :to="localePath('/')">
          {{ $t('Click here to go back.') }}
        </nuxt-link>
      </p>
    </div>
  </main>
</template>

<script>
import pageMixin from '~/mixins/page'

export default {
  mixins: [pageMixin],
  props: {
    statusCode: {
      type: Number,
      default: 404
    }
  }
}
</script>
<style lang="scss">
</style>
