<template>
  <main id="faq" class="py-4">
    <div class="container-xl">
      <div class="content-page content-text mt-6 mb-6">
        <div class="row">
          <div class="col-12 col-md-8">
            <h1 class="h3 page-title mb-5">
              {{ $t('Frequently Asked Questions') }}
            </h1>
            <div v-for="category in faqCategory" :key="category.id" class="mb-5">
              <h5 class="pb-3 border-bottom">
                {{ category.title }}
              </h5>
              <faq-item v-for="faq in category.faqList" :key="faq.id" :faq="faq" />
            </div>
          </div>
          <div class="col-12 col-md-4">
            <h5 class="mb-5">
              {{ $t('Useful links') }}
            </h5>
            <div v-for="(usefulLink, index) in usefulLinkList" :key="index" class="mb-3">
              <nuxt-link :to="localePath(usefulLink.url)" class="text-info">
                {{ $t(usefulLink.title) }}
              </nuxt-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import faqMixin from '~/mixins/faq'

import FaqItem from '~/themes/default/components/page/FaqItem'

export default {
  components: {
    FaqItem
  },
  mixins: [faqMixin]
}
</script>

<style lang="scss">
#faq {
  .collapse {
    font-size: 14px;
  }

  .collapse-icon {
    font-size: 30px;
    border-radius: 50%!important;
    height: 35px;
    width: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10px 20px;
  }

  a {
    color: #17a2b8;

    &:hover {
      color: #3cbdd1;
    }
  }
}
</style>
