<template>
  <main id="page" class="py-4">
    <div class="container-xl">
      <breadcrumbs :bread-crumbs-list="breadCrumbsList" />
      <div v-if="$router.currentRoute.path.includes('contact-us')" class="page-contact">
        <div class="row justify-content-center">
          <div class="col-sm mb-sm-0 content-text" :class="$store.state.storeInfo.enable_contract_form ? 'col-lg-7' : 'col-lg-12'">
            <h1 v-if="title" class="page-title text-custom">
              {{ title }}
            </h1>
            <!-- eslint-disable-next-line vue/no-v-html -->
            <div class="main-content-page" v-html="content" />
          </div>
          <template v-if="$store.state.storeInfo.enable_contract_form">
            <contact-us class="col-sm col-lg-5" />
          </template>
        </div>
      </div>
      <div v-else class="content-page content-text mt-6 mb-6">
        <h1 v-if="title" class="page-title">
          {{ title }}
        </h1>
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div class="main-content-page" v-html="content" />
      </div>
    </div>
  </main>
</template>

<script>
import pageMixin from '~/mixins/page'

import Breadcrumbs from '~/themes/default/components/common/Breadcrumbs'
import ContactUs from '~/themes/default/components/page/ContactUs'

export default {
  components: {
    Breadcrumbs,
    ContactUs
  },
  mixins: [pageMixin]
}
</script>

<style lang="scss">
p {
  margin-bottom: 0;
}

.main-content-page img {
  max-width: 100%;
}
</style>
