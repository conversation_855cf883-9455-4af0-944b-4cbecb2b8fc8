<template>
  <main id="checkoutPage" :style="spriteUrl" class="container">
    <nuxt-link v-if="isNotOrderService" class="d-block ml-auto mb-2" :to="localePath('/cart')">
      <small class="text-muted d-flex align-items-center">
        <i class="icon-sen-arrow-left mr-1" />
        {{ $t('Return to cart') }}
      </small>
    </nuxt-link>
    <b-alert v-if="isNotOrderService && timer > 0 && $store.state.storeInfo && !$store.state.storeInfo.disable_promotion" show variant="warning" class="order-time px-3">
      <div class="d-flex align-items-center">
        <div>
          {{ $t('Your order is reserved for') }}
          <span class="font-weight-500">
            {{ timerText }}
          </span>
          {{ $t('minutes') }}
        </div>
      </div>
    </b-alert>
    <b-button v-else-if="isNotOrderService" variant="outline-dark mb-3" @click="resetOrder">
      {{ $t('Reset order') }}
    </b-button>
    <div class="row">
      <!-- <order-summary-mobile-top/> -->
      <div v-if="orderSummaryPosition!==ORDER_SUMMARY_POSITION.BOTTOM" class="col-12 col-md-5 d-md-none bg-warm py-3">
        <h3 v-b-toggle.orderSummary class="h6 text-custom text-md-black mb-0 d-flex align-items-center justify-content-between" role="tab">
          <span>
            {{ $t('Order Summary') }}
            <span class="d-md-none">
              <i class="icon-sen-chevron-up when-open" />
              <i class="icon-sen-chevron-down when-closed" />
            </span>
          </span>
          <span class="h5 mb-0 d-md-none">{{ $formatPrice(order.total_amount, null , currency) }}</span>
        </h3>
        <b-collapse
          id="orderSummary"
          role="tabpanel"
          :visible="Boolean($store.state.storeInfo && $store.state.storeInfo.always_show_order_summary)"
          @click.prevent.stop
        >
          <div class="list-items">
            <product-item
              v-for="(item, index) in order.products"
              :key="index"
              :item="item"
              :index="index"
              :currency-code="order.currency_code"
              :currency-rate="order.currency_rate"
              page="checkout"
              @removeItem="removeItem"
            />
          </div>
          <caculate-Price
            :order="order"
          />
        </b-collapse>
      </div>

      <!-- form -->
      <div id="formCheckout" class="col-12 col-md-7 mt-3 mt-md-0">
        <b-form action="#" method="post" data-test-id="checkout-form" class="checkout-form" @submit.prevent.stop="handleSubmit">
          <h3 class="mb-3">
            {{ $t('Checkout') }}
          </h3>
          <h6 class="my-3">
            {{ $t('Contact info') }}
          </h6>

          <!-- email -->
          <b-form-group
            label-class="text-overflow-hidden"
            class="inner-label-input"
            :class="{'show':userInfo.email}"
            :label="`${$t('Email address')} *`"
          >
            <b-form-input
              id="email"
              ref="email"
              v-model="userInfo.email"
              type="email"
              pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
              name="email"
              autocomplete="email"
              data-test-id="checkout-form-email"
              :class="{'input-warning': !serverEmailValidate}"
              :state="isTempEmail || hasMistypedEmail ? false : serverEmailValidate ? (submitted ? !$v.userInfo.email.$error : null) : false"
              @input="updateEmail"
            />
            <b-form-invalid-feedback id="email-feedback">
              <span v-if="!$v.userInfo.email.required">{{ $t('Email is required') }}</span>
              <span v-else-if="!$v.userInfo.email.email">{{ $t('Email is invalid') }}</span>
              <span v-else-if="hasMistypedEmail" class="text-warning-email cursor-pointer" @click="() => {userInfo.email = hasMistypedEmail; updateEmail(); }">Did you mean {{ hasMistypedEmail }}? Click to correct</span>
              <span v-else-if="isTempEmail">{{ $t('To ensure timely and reliable delivery of order updates, we ask that you do not use temporary email services.') }}</span>
              <span v-else-if="!serverEmailValidate" class="text-warning-email">{{ $t('Your email may not valid') }}</span>
            </b-form-invalid-feedback>
          </b-form-group>

          <div id="EmailConfirmCheckbox" class="cursor-pointer email-checkbox d-flex align-items-center" @click="userInfo.subscribed = !userInfo.subscribed">
            <span><i class="mr-1" :class="userInfo.subscribed ? 'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank-outline'" /></span>
            <span class="label-text">{{ $t('Email me with news and offers') }}</span>
          </div>

          <h6 class="my-3">
            {{ $t('Shipping info') }}
          </h6>

          <client-only>
            <!-- country -->
            <v-select
              v-show="!currentCountry || isShowCountrySelect"
              class="my-3 country-chooser"
              label="name"
              data-test-id="checkout-country-chooser"
              :options="countryArray"
              :value="userInfo.country"
              :reduce="country => country.code"
              :clearable="false"
              :disabled="!!loading"
              :selectable="country => !countryDisabledCheckout.includes(country.code)"
              @input="updateCheckoutCountry"
            >
              <template #option="{code, name}">
                <span class="d-flex align-items-center" style="font-size: 1.1rem;">
                  <span :class="`mr-2 vti__flag ${code.toLowerCase()}`" /> {{ name }}
                </span>
              </template>
              <template #selected-option="{code, name}">
                <span class="d-flex align-items-center" style="font-size: 1.1rem;">
                  <span :class="`mr-2 vti__flag ${code.toLowerCase()}`" /> {{ name }}
                </span>
              </template>
            </v-select>
            <div v-show="currentCountry && !isShowCountrySelect" class="my-3">
              <div class="d-flex align-items-center">
                {{ $t('Country') }}: <span class="d-inline-flex" style="font-size: 1rem;"><span :class="`vti__flag ${currentCountry.code.toLowerCase()}`" /></span> {{ (currentCountry && currentCountry.name) || '' }} <a class="text-custom pl-1" data-test-id="checkout-change-country" href="#" @click.prevent="isShowCountrySelect = true">({{ $t('Change') }})</a>
              </div>
            </div>
          </client-only>

          <!-- full name -->
          <b-form-group
            label-class="text-overflow-hidden"
            class="inner-label-input"
            :class="{'show':userInfo.name}"
            :label="`${$t(getLabel('fullName') ?? 'Full name')} *`"
          >
            <b-form-input
              id="name"
              ref="name"
              v-model="userInfo.name"
              type="text"
              data-test-id="checkout-name"
              name="name"
              autocomplete="name"
              :state="submitted ? !$v.userInfo.name.$error : null"
            />
            <b-form-invalid-feedback id="name-feedback">
              <span v-if="!$v.userInfo.name.required">{{ $t('Full name is required') }}</span>
              <span v-else-if="!$v.userInfo.name.separateName">{{ $t('Please enter correct first and last name') }}</span>
            </b-form-invalid-feedback>
          </b-form-group>

          <!-- city -->
          <!-- state -->
          <!-- address -->
          <div class="row">
            <div :class="getClass('address_1')">
              <b-form-group
                label-class="text-overflow-hidden"
                class="inner-label-input"
                :class="{'show':userInfo.address}"
                :label="address1Label"
              >
                <b-form-input
                  id="address"
                  ref="address"
                  v-model="userInfo.address"
                  type="text"
                  name="address"
                  autocomplete="address"
                  placeholder=" "
                  data-test-id="checkout-address"
                  :state="submitted ? $v.userInfo.address.required : null"
                />
                <b-form-invalid-feedback id="address-feedback">
                  <span v-if="!$v.userInfo.address.required">{{ $t('Address is required') }}</span>
                </b-form-invalid-feedback>
              </b-form-group>
            </div>
            <div v-if="isHasField('address_2')" :class="getClass('address_2')">
              <b-form-group
                label-class="text-overflow-hidden"
                class="inner-label-input"
                :class="{'show':userInfo.address_2,}"
                :label="address2Label"
              >
                <b-form-input
                  id="apt"
                  ref="address2"
                  v-model="userInfo.address_2"
                  autocomplete="apt"
                  data-test-id="checkout-address-2"
                  type="text"
                  name="apt"
                  @input="warningAddress = false"
                />
                <b-form-invalid-feedback id="address-feedback">
                  <span v-if="warningAddress" class="text-warning-email">{{ $t('Please add your unit/room number if applicable') }}</span>
                </b-form-invalid-feedback>
              </b-form-group>
              <b-form-invalid-feedback id="address-feedback">
                <span v-if="$v.userInfo.address.required && warningAddress" class="text-warning-email">{{ $t('Please add your unit/room number if applicable') }}</span>
              </b-form-invalid-feedback>
            </div>
            <div :class="getClass('city')">
              <b-form-group
                label-class="text-overflow-hidden"
                class="inner-label-input"
                :class="{'show':userInfo.city}"
                :label="cityLabel"
              >
                <b-form-input
                  id="city"
                  ref="city"
                  v-model="userInfo.city"
                  type="text"
                  name="city"
                  autocomplete="city"
                  data-test-id="checkout-city"
                  :state="submitted ? !$v.userInfo.city.$error : null"
                />
                <b-form-invalid-feedback id="city-feedback">
                  <span v-if="!$v.userInfo.city.required">{{ $t('Text is required', {text: ['DE', 'UK'].includes(userInfo.country) ? $t('Town/City') : $t('City') }) }}</span>
                </b-form-invalid-feedback>
              </b-form-group>
            </div>
            <div v-if="isHasField('state')" :class="getClass('state')">
              <b-form-group
                label-class="col-form-label text-overflow-hidden"
                class="inner-label-input"
                :class="{'show':userInfo.state}"
                :label="stateLabel"
                label-for="state"
              >
                <b-form-select
                  v-if="countryState && countryState.length"
                  id="state"
                  ref="state"
                  v-model="userInfo.state"
                  type="text"
                  name="state"
                  data-test-id="checkout-state"
                  :options="countryState"
                  :state="submitted && countryState ? !$v.userInfo.state.$error : null"
                />
                <b-form-input
                  v-else
                  id="state"
                  ref="state"
                  v-model="userInfo.state"
                  type="text"
                  name="state"
                  data-test-id="checkout-state"
                  autocomplete="state"
                  :state="submitted && countryState ? !$v.userInfo.state.$error : null"
                />
                <b-form-invalid-feedback id="state-feedback">
                  <span v-if="!$v.userInfo.state.checkState">{{ $t('Text is required', {text: stateText }) }}</span>
                </b-form-invalid-feedback>
              </b-form-group>
            </div>
            <div :class="getClass('postCode')">
              <b-form-group
                label-class="text-overflow-hidden"
                class="inner-label-input"
                :class="{'show':userInfo.zipcode}"
                :label="postCodeLabel"
              >
                <b-form-input
                  id="zipcode"
                  ref="zipcode"
                  v-model="userInfo.zipcode"
                  pattern="^[a-zA-Z0-9-\s]+$"
                  title="Allowed characters: A-Z, a-z, 0-9, -, space"
                  :type="userInfo.country === 'US' ? 'number' : 'text'"
                  name="zipcode"
                  data-test-id="checkout-zipcode"
                  autocomplete="zipcode"
                  :state="submitted ? !$v.userInfo.zipcode.$error : null"
                />
                <b-form-invalid-feedback id="zipcode-feedback">
                  <span v-if="!$v.userInfo.zipcode.required">{{ $t('Text is required', {text: zipcodeText}) }}</span>
                </b-form-invalid-feedback>
              </b-form-group>
            </div>
            <div v-if="isHasField('houseNumber', false)" :class="getClass('houseNumber')">
              <b-form-group
                label-class="text-overflow-hidden"
                class="inner-label-input"
                :class="{'show':userInfo.house_number}"
                :label="houseNumberLabel"
              >
                <b-form-input
                  id="house-number"
                  ref="houseNumber"
                  v-model="userInfo.house_number"
                  name="houseNumber"
                  data-test-id="checkout-house-number"
                  autocomplete="houseNumber"
                  :state="submitted ? !$v.userInfo.house_number.$error : null"
                  type="text"
                />
                <b-form-invalid-feedback id="house-number-feedback">
                  <span v-if="!$v.userInfo.house_number?.required">{{ $t('Text is required', {text: 'House number' }) }}</span>
                </b-form-invalid-feedback>
              </b-form-group>
            </div>
            <div v-if="isHasField('mailboxNumber', false)" :class="getClass('mailboxNumber')">
              <b-form-group
                label-class="text-overflow-hidden"
                class="inner-label-input"
                :class="{'show':userInfo.mailbox_number}"
                :label="mailboxLabel"
              >
                <b-form-input
                  id="mailboxNumber"
                  ref="mailboxNumber"
                  v-model="userInfo.mailbox_number"
                  name="mailboxNumber"
                  data-test-id="checkout-mailbox-number"
                  autocomplete="mailboxNumber"
                  type="text"
                />
              </b-form-group>
            </div>
          </div>

          <!-- phone -->
          <template v-if="showPhoneInput">
            <div class="position-relative">
              <b-form-group
                label-class="pr-7 text-overflow-hidden"
                class="inner-label-input mb-1"
                :class="{'show':userInfo.phone}"
              >
                <vue-tel-input
                  id="phone"
                  ref="phone"
                  :class="{
                    'input-error': submitted && !isPhoneValid && isPhoneNumberRequired,
                    'input-success': submitted && isPhoneValid && userInfo.phone}"
                  :default-country="userInfo.country"
                  name="phone"
                  autocomplete="phone"
                  class="pr-5 border-radius-none"
                  :dropdown-options="{
                    showDialCodeInSelection: true,
                    showDialCodeInList: true,
                    showFlags:true,
                    showSearchBox: true,
                    width: '400px'
                  }"
                  :value="phoneInput"
                  data-test-id="checkout-phone"
                  :input-options="{
                    placeholder: phoneLabel,
                    type: 'tel',
                  }"
                  @input="checkPhone"
                />
              </b-form-group>
              <div v-if="!submitted" class="phone-tooltip">
                <i class="icon-sen-help-circle-outline" />
                <div class="tooltip-content font-small">
                  <p class="mb-0">
                    {{ $t('In case we need to contact') }}
                    {{ $t('you about your order') }}
                  </p>
                </div>
              </div>
            </div>
            <div id="phone-feedback" class="text-danger font-small">
              <span v-if="submitted && !isPhoneValid && userInfo.phone">{{ $t('Phone number is invalid') }}</span>
              <span v-if="submitted && !userInfo.phone && isPhoneNumberRequired">{{ $t('Text is required', {text: $t('Phone number')}) }}</span>
            </div>
          </template>

          <div v-if="isHasField('deliveryNote', false)">
            <h6 class="mb-0 mt-3">
              {{ $t('Add delivery instructions (optional)') }}
            </h6>
            <div class="mt-2">
              <b-form-textarea
                v-model="orderNote"
                :formatter="limitLengthOrderNote"
                :placeholder="$t('Provide details such as building description, a nearby landmark, or other navigation instructions')"
                rows="3"
                size="xs"
                style="overflow: hidden; resize: none;"
              />
            </div>
          </div>

          <!-- delivery -->
          <div v-if="isNotOrderService && order.fulfill_status === 'no_ship'" class="no-ship-box text-center shadow-2 my-4 py-2">
            <div class="font-weight-500">
              {{ countryDisabledCheckout.includes(userInfo.country) ? $t(`no_ship_text1`, {country: currentCountry.name}) : $t('We are sorry, but there are some product variants in your cart that are not available.') }}
            </div>
            <div>{{ countryDisabledCheckout.includes(userInfo.country) ? $t('no_ship_text2') : $t('Please remove it or edit to continue.') }}</div>
          </div>

          <template v-else-if="isNotOrderService && shippingMethods && shippingMethods.length > 0">
            <template v-if="!userTriggerShippingMethod && order.shipping_method !== 'express'">
              <div class="mt-3 mb-2">
                {{ shippingMethods[0].description }}<br>
                <div v-if="$isShowWarningShipLate()" style="color: rgb(255, 145, 0);">
                  <small>{{ $t('* Order may arrive post-Christmas due to peak season.') }}</small>
                </div>
                <a v-if="shippingMethods.find((item) => item.name === 'express')" href="#" style="color: var(--primary-color);" @click.prevent="loading ? '' :shippingFee = 'express'">
                  <small v-text="$t('Need it fast? Upgrade to express shipping by clicking here')" />
                </a>
              </div>
            </template>
            <template v-else>
              <h6 class="my-3">
                {{ $t('Delivery options') }}
              </h6>
              <b-form-group v-slot="{ ariaDescribedby }" class="border-option">
                <div
                  v-for="(shipping, index) in shippingMethods"
                  :id="`shippingMethod_${shipping.name}`"
                  :key="index"
                  class="delivery-item d-flex py-2 cursor-pointer"
                  @click="loading || isDisabled ? '' :shippingFee = shipping.name"
                >
                  <div class="col-auto pr-2">
                    <custom-radio-input
                      v-model="shippingFee"
                      :input-id="`shipping-fee-${shipping.name}`"
                      :aria-describedby="ariaDescribedby"
                      :compare-value="shipping.name"
                      :disabled="!!loading || isDisabled"
                      name="shipping_fee"
                      data-test-id="checkout-shipping-method"
                    />
                  </div>
                  <div class="col p-0">
                    <div class="text-capitalize font-weight-500">
                      {{ $t(shipping.name) }} <span v-if="shipping.shipping_cost"> - {{ $formatPrice(shipping.shipping_cost, 'USD', currency) }}</span>
                      <b-spinner v-if="loading === `shipping_fee_${shipping.name}`" small variant="gray" class="ml-2" />
                    </div>
                    <div v-if="$store.state.storeInfo.show_checkout_shipping_info !== 0">
                      <small v-html="shipping.description" />
                    </div>
                  </div>
                  <div class="col-2" />
                </div>
              </b-form-group>
            </template>
          </template>

          <div v-if="isNotOrderService && ($store.state.storeInfo && $store.state.storeInfo.enable_insurance_fee) && order.insurance_fee_2" id="ShippingInsuranceCheckbox" class="cursor-pointer shiping-insurance-checkbox d-flex align-items-center" :class="loading || isDisabled ? 'text-gray' : ''" @click="loading || isDisabled ? '' : updateDeliveryInsurance(!order.insurance_fee)">
            <span><i class="mr-1" :class="order.insurance_fee ? 'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank-outline'" /></span>
            <span class="label-text">{{ $t('Add Delivery Insurance and SMS tracking for only') }} {{ $formatPrice(order.insurance_fee_2 , null , currency) }}</span>
          </div>
          <!-- Coupon -->
          <client-only>
            <coupon-form
              v-if="isNotOrderService"
              :order="order"
              :discount-apply="discountApply"
              :check-discount="checkDiscount"
              :promotions-list="promotionsList"
              :is-disabled="isDisabledButtonCoupon"
              :is-loading="loading === 'apply-coupon'"
              :discount-error-type="discountErrorType"
              @updateDiscountApply="(value)=>{
                discountApply = value;
                checkDiscount= false
              }"
              @updateCheckoutDiscount="updateCheckoutDiscount"
            />
          </client-only>
          <!-- tip -->
          <template v-if="isNotOrderService && $store.state.storeInfo && $store.state.storeInfo.show_tipping">
            <a
              href="#"
              data-test-id="checkout-tip-section"
              class="text-custom cursor-pointer"
              @click.prevent="userTriggerTipSection = !userTriggerTipSection"
            >{{ $t('Want to tip our designer?') }}</a>
            <div v-show="order.tip_amount || userTriggerTipSection">
              <p class="font-small text-gray2 mt-2">
                {{ $t('Please consider tipping to appreciate our designer\'s work.') }}
              </p>
              <div class="font-weight-500">
                <div class="d-flex mb-3 border custom-border cursor-pointer">
                  <b-button
                    v-for="(tip, index) in tipList"
                    :key="index"
                    :disabled="!!loading || isDisabled"
                    variant="outline-custom2"
                    class="col-3 tip-item text-center p-2"
                    :class="{
                      'bg-custom text-white': currentTipIndex === index,
                      'border-left': index > 0,
                      'd-flex justify-content-center align-items-center': index === 0,
                    }"
                    :data-test-id="index === 0 ? 'checkout-no-tip-button' : `checkout-tip-button`"
                    @click="currentTipIndex = index; updateTip((order.total_product_amount * tip).toFixed(2))"
                  >
                    <b-spinner v-if="currentTipIndex === index && loading === 'tip'" class="custom-spinner" />
                    <div v-else>
                      <div>
                        {{ index ? `${tip * 100} %` : $t('No tip') }}
                      </div>
                    </div>
                  </b-button>
                </div>
                <div v-if="order.tip_amount" class="d-flex">
                  <b-input-group>
                    <b-form-input
                      v-model="currentTipText"
                      data-test-id="checkout-tip-input"
                      :placeholder="$t('Custom tip')"
                      @blur="onCustomTipInputFocusOut"
                      @focus="isCustomTipFocus = true"
                    />
                    <b-input-group-append>
                      <b-button
                        data-test-id="checkout-tip-update-button"
                        :disabled="!!loading"
                        @click="onUpdateCustomTip"
                      >
                        <span>{{ $t('Update tip') }}</span>
                      </b-button>
                    </b-input-group-append>
                  </b-input-group>
                </div>
              </div>
            </div>
          </template>

          <div v-if="noPaymentGatewayConfig" class="alert alert-danger">
            <strong>Error:</strong> Please set up a payment gateway for processing transactions!
          </div>

          <div
            v-if="isPaypalSmartCheckout"
            id="paypal-button-container"
            ref="paypalButtonContainer"
            :class="(paypalIframeFullScreen) ? 'sc-fullscreen' : 'mx-auto mt-3 position-relative'"
          />
          <template v-else>
            <!-- payment info -->
            <h6 class="mt-3 mb-2 d-flex justify-content-between align-items-center">
              {{ $t('Payment info') }}
              <span v-if="$store.state.storeInfo && $store.state.storeInfo?.enable_payment_ssl_norton" class="d-none d-md-flex" style="height: 27px;">
                <icon-ssl-secure />
                <icon-norton-secured class="ml-3" />
              </span>
            </h6>
            <div id="button-payment" />
            <b-form-group id="payment-info" v-slot="{ ariaDescribedby }" class="border-option" data-test-id="payment-info" dusk="payment-info">
              <div v-for="(gateway, index) in listGateways" :key="index" class="item-gateway">
                <div
                  v-if="(gateway.gateway === 'stripe-card' || gateway.gateway === 'stripe') && countErrorStripe <= 3"
                  class="d-flex flex-wrap justify-content-between align-items-center px-3 py-2"
                  @click="selectedGateway = gateway.gateway"
                >
                  <custom-radio-input
                    v-model="selectedGateway"
                    input-id="payment-card"
                    :aria-describedby="ariaDescribedby"
                    :compare-value="gateway.gateway"
                    :label-input="$t('Card')"
                    :data-test-id="`radio-payment-${gateway.gateway}`"
                  />
                  <div class="my-2 d-flex">
                    <div class="icon-card px-1" :class="{active: brandCard==='visa'}">
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 42;" />
                    </div>
                    <div class="icon-card px-1" :class="{active: brandCard==='amex'}">
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 67;" />
                    </div>
                    <div class="icon-card px-1" :class="{active: brandCard==='mastercard'}">
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 92;" />
                    </div>
                    <div class="icon-card px-1" :class="{active: brandCard==='discover'}">
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 117;" />
                    </div>
                  </div>
                  <div class="flex-wrap w-100" :class="selectedGateway === gateway.gateway ? 'd-flex' : 'd-none'">
                    <div v-if="errorMessage" id="errorMessage" class="w-100 text-danger">
                      <small>{{ errorMessage }}</small>
                    </div>
                    <div
                      v-if="computedStripeGateway.checkout_domain"
                      id="stripe-iframe-container"
                      ref="stripeIframeContainer"
                      class="w-100"
                    />
                    <template v-else>
                      <div class="w-100 py-2 position-relative">
                        <div
                          id="card-payment-number"
                          :data-test-id="`${gateway.gateway}-number`"
                          :class="{'input-error': errorMessage }"
                        />
                        <div v-if="!errorMessage" class="phone-tooltip right-5">
                          <i class="icon-sen-lock" />
                        </div>
                      </div>
                      <div class="w-100 w-md-50 py-2 pr-md-1">
                        <div
                          id="card-payment-expiry"
                          :data-test-id="`${gateway.gateway}-expiry`"
                          :class="{'input-error': errorMessage }"
                        />
                      </div>
                      <div class="w-100 w-md-50 py-2 pl-md-1 position-relative">
                        <div
                          id="card-payment-cvc"
                          :data-test-id="`${gateway.gateway}-cvc`"
                          :class="{'input-error': errorMessage }"
                        />
                        <div v-if="!errorMessage" class="phone-tooltip">
                          <i class="icon-sen-help-circle-outline" />
                          <div class="tooltip-content font-small">
                            <p class="mb-1 text-white">
                              {{ $t('3-digit security code usually found on the back of your card. American Express cards have a 4 digit code located on the front.') }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </template>
                    <!-- <div class="mx-auto">
                        <em class="text-success text-stripe">{{ $t('Your payment is securely processed by') + ' ' }}<strong class="text-secondary">{{ $t('Stripe') }}</strong>.</em>
                      </div> -->
                  </div>
                </div>
                <div
                  v-else-if="gateway.gateway === 'stripe-ewallet' && countErrorStripe <= 3 && countErrorStripeEWallet <= 3"
                  class="d-flex flex-wrap justify-content-between align-items-center px-3 py-2"
                  style="gap: 0.75rem;"
                  @click="selectedGateway = gateway.gateway"
                >
                  <custom-radio-input
                    v-model="selectedGateway"
                    input-id="payment-ewallet"
                    :aria-describedby="ariaDescribedby"
                    :compare-value="gateway.gateway"
                    :label-input="$t('Apple/Google Pay')"
                  />
                  <div class="d-flex" style="transform: scale(1.2); margin-right: 10px; padding: 10px 0;">
                    <i :class="{active: selectedGateway === gateway.gateway}" class="icon-card icons-sprite mr-2" style="--icon-height: 100;--icon-y-pos: 134; height: 16px;" />
                    <i :class="{active: selectedGateway === gateway.gateway}" class="icon-card icons-sprite" style="--icon-height: 90;--icon-y-pos: 167; height: 16px;" />
                  </div>
                  <div class="flex-wrap w-100" :class="selectedGateway === gateway.gateway ? 'd-flex' : 'd-none'">
                    <div class="w-100">
                      <div v-if="errorMessage" id="errorMessage" class="w-100 text-danger">
                        <small>{{ errorMessage }}</small>
                      </div>
                      <div
                        v-if="computedStripeGateway.checkout_domain"
                        id="stripe-iframe-ewallet-container"
                        ref="stripeIframeEWalletContainer"
                      />
                      <div v-else id="payment-element" />
                    </div>
                  </div>
                </div>

                <div
                  v-else-if="gateway.gateway === 'cod'"
                  class="d-flex justify-content-between align-items-center px-3 py-2"
                  @click="selectedGateway = gateway.gateway"
                >
                  <custom-radio-input
                    v-model="selectedGateway"
                    input-id="payment-paypal"
                    :aria-describedby="ariaDescribedby"
                    :compare-value="gateway.gateway"
                    :label-input="$t('Cash on delivery')"
                  />
                  <div>
                    <i :class="{active: selectedGateway === gateway.gateway}" class="icon-card icons-sprite" style="height: 40px;--icon-height: 140;--icon-y-pos: 335;--icon-target-height: 40;" />
                  </div>
                </div>
                <div
                  v-else-if="gateway.gateway === 'paypal'"
                  class="d-flex justify-content-between align-items-center px-3 py-2"
                  @click="selectedGateway = gateway.gateway"
                >
                  <custom-radio-input
                    v-model="selectedGateway"
                    input-id="radio-payment-paypal"
                    :aria-describedby="ariaDescribedby"
                    :compare-value="gateway.gateway"
                    :label-input="$t('PayPal')"
                    data-test-id="radio-payment-paypal"
                  />
                  <div style="padding: 7px 0;">
                    <i :class="{active: selectedGateway === gateway.gateway}" class="icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371;" />
                  </div>
                </div>
                <div
                  v-else-if="gateway.gateway === 'tazapay'"
                  class="d-flex justify-content-between align-items-center px-3 py-2 flex-wrap"
                  @click="selectedGateway = gateway.gateway"
                >
                  <custom-radio-input
                    v-model="selectedGateway"
                    input-id="radio-bank-transfer"
                    :aria-describedby="ariaDescribedby"
                    :compare-value="gateway.gateway"
                    :label-input="$t('Card/Bank Transfer')"
                  />
                  <div>
                    <div class="mt-2 d-flex">
                      <!-- Tazapay icon -->
                      <!-- <i :class="{active: selectedGateway === gateway.gateway}" class="icon-card icons-sprite" style="--icon-height: 40;--icon-y-pos: 843;" /> -->
                      <div class="icon-card px-1" :class="{active: selectedGateway === gateway.gateway}">
                        <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 42;" />
                      </div>
                      <div class="icon-card px-1" :class="{active: selectedGateway === gateway.gateway}">
                        <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 67;" />
                      </div>
                      <div class="icon-card px-1" :class="{active: selectedGateway === gateway.gateway}">
                        <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 92;" />
                      </div>
                      <div class="icon-card px-1" :class="{active: selectedGateway === gateway.gateway}">
                        <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 117;" />
                      </div>
                    </div>
                    <div class="d-flex justify-content-end">
                      <small>{{ $t('and more...') }}</small>
                    </div>
                  </div>
                </div>
                <div
                  v-else-if="$store.state.storeInfo
                    && $store.state.storeInfo.id === 1
                    && gateway.gateway === 'momo'
                    && ($store.state.userCountry === 'VN' || currentCountry.code === 'VN' || currency === 'VND')"
                  class="item-gateway"
                >
                  <div
                    class="d-flex justify-content-between align-items-center px-3 py-2"
                    @click="selectedGateway = 'momo'"
                  >
                    <custom-radio-input
                      v-model="selectedGateway"
                      input-id="payment-momo"
                      :aria-describedby="ariaDescribedby"
                      :compare-value="'momo'"
                      :label-input="$t('Momo')"
                    />
                    <div>
                      <i :class="{active: selectedGateway === gateway.gateway}" class="icon-card icons-sprite" style="height: 40px;--icon-height: 140;--icon-y-pos: 335;--icon-target-height: 40;" />
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="currentCountry.code !== 'US'" class="item-gateway">
                <div
                  v-if="listBanks && listBanks.length"
                  class="px-3 py-2"
                  @click="selectedGateway = 'other'"
                >
                  <div class="d-flex justify-content-between align-items-center">
                    <custom-radio-input
                      v-model="selectedGateway"
                      input-id="payment-other"
                      :aria-describedby="ariaDescribedby"
                      :compare-value="'other'"
                      :label-input="$t('Other')"
                    />
                    <div class="d-flex flex-wrap" style="padding: 7px 0;">
                      <i
                        v-for="(bank, index) in listBanks"
                        :key="index"
                        :class="[
                          'ml-1',
                          'icon-card',
                          'icons-sprite',
                          `icons-sprite-${bank.gateway}`,
                          (currentBank.gateway === bank.gateway) ? 'active' : ''
                        ]"
                        @click="selectedGateway = 'other'; currentBank = bank; selectBank(bank.gateway)"
                      />
                    </div>
                  </div>
                  <div v-if="selectedGateway === 'other'">
                    <div v-if="errorMessage" id="errorMessage" class="w-100 text-danger">
                      <small>{{ errorMessage }}</small>
                    </div>
                    <b-dropdown
                      variant="outline-custom"
                      block
                      lazy
                      :right="true"
                      toggle-class="border text-overflow-hidden pr-3 mt-2"
                      menu-class="w-100"
                      :text="currentBank && currentBank.name || $t('Select method')"
                    >
                      <b-dropdown-item
                        v-for="(bank, index) in listBanks"
                        :key="index"
                        :active="bank.gateway === currentBank.gateway"
                        link-class="d-flex justify-content-between"
                        @click="currentBank = bank; selectBank(bank.gateway)"
                      >
                        <span>{{ bank.name }}</span>
                        <i
                          :class="[
                            'ml-1',
                            'icons-sprite',
                            'icons-sprite-list-banks',
                            `icons-sprite-${bank.name}`,
                          ]"
                        />
                      </b-dropdown-item>
                    </b-dropdown>
                    <div v-if="currentBank && currentBank.required" class="w-100 mt-2 py-1">
                      <!-- country -->
                      <client-only>
                        <v-select
                          v-if="currentBank.required.includes('country')"
                          :id="selectedGateway + '.country'"
                          v-model="billingDetails.country"
                          name="country"
                          label="name"
                          class="country-bank-select"
                          :options="currentBank.countries"
                          :reduce="country => country.code"
                          :clearable="false"
                          :placeholder="$t('Country')"
                        />
                      </client-only>
                    </div>
                    <div class="w-100 mt-2 py-1" :class="currentBank.name_component ? 'd-block' : 'd-none'">
                      <div :id="currentBank.gateway" />
                    </div>
                  </div>
                </div>
              </div>
            </b-form-group>

            <!-- button -->
            <div @click="$tracking.customTracking({ event: 'checkout_place_order_button_click' })">
              <div v-if="isPaypalSmartCheckout" id="paypal-button-container" style="max-width: 400px;" class="mx-auto" />
              <b-button
                v-else
                id="btn-submit"
                dusk="submit-button"
                block
                type="submit"
                size="lg"
                data-test-id="checkout-submit"
                :variant="variantButtonPayment"
                class="my-3 font-weight-500 d-none d-md-block"
                :disabled="
                  order.fulfill_status === 'no_ship'
                    || !!loading
                    || isTempEmail
                    || (!payWithCardAble && (selectedGateway === 'stripe-card' || selectedGateway === 'stripe-ewallet'))
                    || (selectedGateway === 'other' && !currentBank)
                    || countryDisabledCheckout.includes(userInfo.country)
                    || isDisabled
                "
              >
                <b-spinner v-if="loading === 'place_order'" class="float-right" />
                <span v-if="countryDisabledCheckout.includes(userInfo.country)">
                  {{ $t('Cannot ship to', { country: currentCountry.name}) }}
                </span>
                <span v-else-if="['stripe-card','stripe-ewallet', 'cod'].includes(selectedGateway)">
                  <icon-lock style="width: 22px; height: 22px; margin-top: -7px;" />
                  {{ $t('Place Your Order') }}
                </span>
                <div v-else-if="selectedGateway === 'paypal'" class="d-flex align-items-center justify-content-center">
                  <div style="color: #020617;">
                    {{ $t('Check Out with') }}
                  </div>
                  <div
                    style="
                    transform: scale(1.4);
                    margin-left: 18px;
                    width: 62px;
                    overflow: hidden;
                    margin-bottom: 3px;
                  "
                  >
                    <i class="active icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371; transform: translateX(-22px);" />
                  </div>
                </div>
                <span v-else-if="selectedGateway === 'tazapay'">{{ $t('Continue To Payment') }}</span>
                <span v-else-if="selectedGateway === 'momo'">{{ $t('Continue With Momo') }}</span>
                <span v-else-if="selectedGateway === 'other' && currentBank && currentBank.gateway.includes(getStripePaymentName())">
                  {{ $t('Continue With Bank', { bank: (listBanks.find(el => el.gateway === currentBank.gateway) || {}).name }) }}
                </span>
                <span v-else>{{ $t('Select a method') }}</span>
              </b-button>
            </div>

            <b-modal
              id="modal-tazapay"
              v-model="isShowModalTazapay"
              centered
              size="lg"
              hide-footer
              title="Tazapay"
              @hide="onCloseModalTazapay"
            >
              <div id="tz-checkout" class="mt-3">
                <div class="text-center">
                  <h6 class="mt-3">
                    Loading...
                  </h6>
                </div>
              </div>
            </b-modal>
          </template>
        </b-form>
      </div>

      <!-- <order-summary /> -->
      <div class="col-12 col-md-5 d-none d-md-block">
        <h3 class="mb-3">
          {{ $t('Order Summary') }}
        </h3>
        <div class="list-items">
          <!-- Item 1 -->
          <product-item
            v-for="(item, index) in order.products"
            :key="index"
            :item="item"
            :index="index"
            :show-img="isNotOrderService"
            :currency-code="order.currency_code"
            :currency-rate="order.currency_rate"
            page="checkout"
            @removeItem="removeItem"
          />
        </div>
        <caculate-Price
          :order="order"
        />
      </div>
    </div>

    <!-- <order-summary-mobile-bottom/> -->
    <div v-if="orderSummaryPosition===ORDER_SUMMARY_POSITION.BOTTOM" class="col-12 col-md-5 d-md-none bg-warm py-3 mb-2">
      <h3 v-b-toggle.orderSummary class="h6 text-custom text-md-black mb-0 d-flex align-items-center justify-content-between" role="tab">
        <span>
          {{ $t('Order Summary') }}
          <span class="d-md-none">
            <i class="icon-sen-chevron-up when-open" />
            <i class="icon-sen-chevron-down when-closed" />
          </span>
        </span>
        <span class="h5 mb-0 d-md-none">{{ $formatPrice(order.total_amount, null , currency) }}</span>
      </h3>
      <b-collapse
        id="orderSummary"
        role="tabpanel"
        :visible="$store.state.storeInfo && $store.state.storeInfo.always_show_order_summary"
        @click.prevent.stop
      >
        <div class="list-items">
          <product-item
            v-for="(item, index) in order.products"
            :key="index"
            :item="item"
            :index="index"
            :currency-code="order.currency_code"
            :currency-rate="order.currency_rate"
            page="checkout"
            @removeItem="removeItem"
          />
        </div>
        <caculate-Price
          :order="order"
        />
      </b-collapse>
    </div>

    <div v-if="isNotOrderService" class="policies content-text text-blur font-light index-0">
      <small v-if="false">
        <p>{{ `${$t('checkout_text_1')} ${$store.state.storeInfo && $store.state.storeInfo.name || 'store'} ${$t('checkout_text_2')} ${$store.state.storeInfo && $store.state.storeInfo.name || 'store'} ${$t('checkout_text_3')}` }}</p>
      </small>
      <p class="text-policies mb-2">
        <span>
          {{ $t('checkout_text_4') }}
        </span>
        <a
          class="text-info"
          :href="localePath('/page/terms-of-service')"
          @click.prevent="showPolicy('terms-of-service')"
          v-text="$t('terms of service')"
        />.
      </p>
      <div v-if="$store.state.storeInfo && $store.state.storeInfo?.enable_payment_ssl_norton" class="d-md-none" style="overflow: hidden;">
        <span class="d-flex justify-content-center my-3" style="height: 55px;">
          <icon-ssl-secure />
          <icon-norton-secured class="ml-4" />
        </span>
      </div>
    </div>

    <div class="position-fixed bottom-0 left-0 w-100 p-2 bg-white border z-index-100 d-md-none" @click="$tracking.customTracking({ event: 'checkout_place_order_button_click' })">
      <b-button
        v-if="!isPaypalSmartCheckout"
        block
        size="lg"
        :variant="variantButtonPayment"
        class="font-weight-500 py-2-5"
        :disabled="
          order.fulfill_status === 'no_ship'
            || !!loading
            || isTempEmail
            || (!payWithCardAble && (selectedGateway === 'stripe-card' || selectedGateway === 'stripe-ewallet'))
            || (selectedGateway === 'other' && !currentBank)
            || countryDisabledCheckout.includes(userInfo.country)"
        @click="handleSubmit"
      >
        <b-spinner v-if="loading === 'place_order'" class="float-right" />
        <span v-if="countryDisabledCheckout.includes(userInfo.country)">
          {{ $t('Cannot ship to', { country: currentCountry.name}) }}
        </span>
        <span v-else-if="['stripe-card','stripe-ewallet', 'cod'].includes(selectedGateway)">
          <icon-lock style="width: 22px; height: 22px; margin-top: -7px;" />
          {{ $t('Place Your Order') }}
        </span>
        <div v-else-if="selectedGateway === 'paypal'" class="d-flex align-items-center justify-content-center">
          <div style="color: #020617;">
            {{ $t('Check Out with') }}
          </div>
          <div
            style="
                    transform: scale(1.4);
                    margin-left: 18px;
                    width: 62px;
                    overflow: hidden;
                    margin-bottom: 3px;
                  "
          >
            <i class="active icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371; transform: translateX(-22px);" />
          </div>
        </div>
        <span v-else-if="selectedGateway === 'tazapay'">{{ $t('Continue To Payment') }}</span>
        <span v-else-if="selectedGateway === 'momo'">{{ $t('Continue With Momo') }}</span>
        <span v-else-if="selectedGateway === 'other' && currentBank && currentBank.gateway.includes(getStripePaymentName())">
          {{ $t('Continue With Bank', { bank: (listBanks.find(el => el.gateway === currentBank.gateway) || {}).name }) }}
        </span>
        <span v-else>{{ $t('Select a method') }}</span>
      </b-button>
    </div>
    <hr class="w-100">
    <policy-group class="mb-3" @show="showPolicy" />
    <policy-modal v-if="currentPolicySlug" :type="currentPolicySlug" @close="currentPolicySlug = null" />
    <b-modal
      v-model="isShowConfirmProduct"
      centered
      body-class="pb-0"
      hide-footer
      :title="$t('Is this quantity correct?')"
      data-test-id="modal-confirm-items-quantity"
      @close="confirmOrder"
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalConfirmProduct', null)"
      @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalConfirmProduct', null)"
    >
      <product-cart-item-update-quantity
        v-for="(value, index) in productIndexList"
        :key="index"
        :index="value"
        :cart-item="cloneProducts[value]"
        @updateQuantity="updateQuantity"
      />
      <div class="row p-1">
        <b-button
          variant="custom"
          block
          size="lg"
          @click="handleEditProductSubmit"
        >
          <span>
            {{ $t('Save changes') }}
          </span>
          <b-spinner v-if="loadingButton" class="float-right" />
        </b-button>
      </div>
    </b-modal>

    <b-modal
      id="modalConfirmEmail"
      v-model="isShowModalConfirmEmail"
      centered
      size="lg"
      body-class="d-flex flex-wrap justify-content-center"
      hide-footer
      hide-header
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalConfirmEmail', null)"
      @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalConfirmEmail', null)"
    >
      <div class="col-12 py-2 text-center">
        <h4>{{ $t('Payment confirmation with email address', {email: userInfo.email}) }}</h4>
      </div>
      <div class="m-0 col-6">
        <b-button
          block
          variant="secondary"
          class="border-radius-none btn-confirm"
          @click="serverEmailValidate = true, isShowModalConfirmEmail = false, handleSubmit()"
        >
          {{ $t('Continue checkout') }}
        </b-button>
      </div>
      <div class="col-6">
        <b-button
          block
          variant="primary"
          class="border-radius-none btn-confirm"
          @click="isShowModalConfirmEmail = false; focusEmail()"
        >
          {{ $t('Edit email') }}
        </b-button>
      </div>
    </b-modal>

    <b-modal
      v-model="isShowModelCreditCardDiscount"
      centered
      size="lg"
      body-class="d-flex flex-wrap justify-content-center"
      hide-footer
      hide-header
    >
      <div class="col-12 py-2 text-center">
        <h4>{{ $t('Do you want to checkout with credit card for 2% discount?') }}</h4>
      </div>
      <div class="m-0 col-6">
        <b-button
          block
          variant="secondary"
          class="border-radius-none btn-confirm"
          @click="paypalConfirm = true, isShowModelCreditCardDiscount = false, handleSubmit()"
        >
          {{ $t('Checkout with PayPal') }}
        </b-button>
      </div>
      <div class="col-6">
        <b-button
          block
          variant="primary"
          class="border-radius-none btn-confirm"
          @click="isShowModelCreditCardDiscount = false; useCreditCardDiscount()"
        >
          {{ $t('Use credit card') }}
        </b-button>
      </div>
    </b-modal>
  </main>
</template>

<script>
import checkoutMixin from '~/mixins/checkout'
import ProductItem from '~/themes/default/components/order/ProductItem'
import CaculatePrice from '~/themes/default/components/order/CaculatePrice'
import CouponForm from '~/themes/default/components/order/CouponForm'
import ProductCartItemUpdateQuantity from '~/themes/default/components/cart/productCartItemUpdateQuantity'
import { ORDER_SUMMARY_POSITION } from '~/helpers/variableConst'

export default {
  components: {
    CouponForm,
    ProductItem,
    CaculatePrice,
    ProductCartItemUpdateQuantity
  },
  mixins: [checkoutMixin],
  data () {
    return {
      ORDER_SUMMARY_POSITION,
      isShowConfirmProduct: false,
      confirmAble: false,
      loadingButton: false,
      currentPolicySlug: null,
      productIndexList: [],
      cloneProducts: []
    }
  },
  computed: {
    spriteUrl () {
      return `--sprite-url: url("${this.$config.publicPath}/images/logo_checkout_sprite.webp")`
    },
    totalQuantity () {
      return this.$store.getters['cart/getTotalQuantity']
    },
    isSeparateName () {
      return this.$store.state.order.isSeparateName
    },
    variantButtonPayment () {
      if (this.selectedGateway === 'paypal') {
        return 'paypal'
      }
      return ['stripe-card', 'stripe-ewallet', 'cod'].includes(this.selectedGateway) ? 'custom' : 'primary'
    },
    orderSummaryPosition () {
      return this.$store.state.storeInfo?.order_summary_position
    },
    validateCouponForm () {
      return this.order.allow_coupon_form
    }
  },
  mounted () {
    if (this.order.access_token === this.$store.state.order.order.access_token) {
      if (this.totalQuantity >= 2) {
        this.cloneProducts = this.$store.state.cart.products.map(item => JSON.parse(JSON.stringify(item)))
        this.cloneProducts.forEach((item, index) => {
          item.price = this.order.products[index]?.price
          if (item.isCheckQuantity) {
            this.productIndexList.push(index)
            item.isCheckQuantity = false
          }
        })
        if (this.productIndexList.length) {
          this.isShowConfirmProduct = true
        }
      }
    }
  },
  beforeDestroy () {
    clearTimeout(this.trackerUpdateEmail)
    clearTimeout(this.trackerUpdateOrder)
  },
  methods: {
    confirmOrder,
    updateQuantity,
    handleEditProductSubmit,
    focusEmail,
    showPolicy,
    limitLengthOrderNote (value) {
      return String(value).substring(0, 255)
    }
  }
}

function updateQuantity ({ index, quantity }) {
  this.cloneProducts[index].quantity = quantity
  this.confirmAble = true
}

async function handleEditProductSubmit (e) {
  this.confirmOrder()
  if (this.confirmAble) {
    this.loadingButton = true
    const result = await this.$store.dispatch('order/createOrder', {
      products: this.cloneProducts,
      token: this.$store.state.cart.token
    })
    if (result && result.success) {
      this.$nuxt.refresh()
      await this.getIntentOrder()
    } else {
      this.$toast.error(`Error: ${result.message && result.message.content}`)
    }
    this.loadingButton = false
  }
  this.isShowConfirmProduct = false
}

function confirmOrder () {
  this.$store.commit('cart/UPDATE_CART_DATA', { products: this.cloneProducts })
}

function focusEmail () {
  setTimeout(() => {
    this.$refs.email.focus()
  }, 500)
}

function showPolicy (slug) {
  this.currentPolicySlug = slug
}
</script>

<style scoped>
.custom-spinner {
  height: 16px;
  width: 16px;
}

.icons-sprite {
  --icon-target-height: 25;
  --scale-fac: calc(var(--icon-height) / var(--icon-target-height));

  height: calc(var(--icon-target-height) * 1px);
  width: calc(150px / var(--scale-fac));
  background: var(--sprite-url) no-repeat;
  background-size: 100%;
  background-position-y: calc(var(--icon-y-pos) * -1px);
  display: block;
}

.icons-sprite-stripe-iDEAL {
  --icon-height: 120;
  --icon-y-pos: 150;
}

.icons-sprite-stripe-Bancontact {
  --icon-height: 110;
  --icon-y-pos: 193.5;
}

.icons-sprite-stripe-Sofort {
  --icon-height: 55;
  --icon-y-pos: 437;
}

.icons-sprite-stripe-Klarna {
  --icon-height: 148;
  --icon-y-pos: 172;
}

.icons-sprite-list-banks {
  --icon-target-height: 42;
}

.icons-sprite-iDEAL {
  --icon-height: 145;
  --icon-y-pos: 205;
}

.icons-sprite-Bancontact {
  --icon-height: 110;
  --icon-y-pos: 325;
}

.icons-sprite-Sofort {
  --icon-height: 50;
  --icon-y-pos: 810;
}

.icons-sprite-Klarna {
  --icon-height: 145;
  --icon-y-pos: 295;
}
</style>

<style lang="scss">
button.btn.btn-paypal {
  background-color: #ffc439 !important;
  border-color: #ffc439 !important;
}
button.btn.btn-paypal:hover {
  background-color: #ffc439 !important;
  border-color: #ffc439 !important;
}

.custom-control-input:checked ~ .custom-control-label::before {
  border-color: var(--primary-color) !important;
  background-color: var(--primary-color) !important;
}

.classic #checkoutPage #formCheckout .border-option,
.classic .StripeElement {
  border-radius: 0.3rem;
}

.classic .btn-outline-custom2:first-child {
  border-top-left-radius: 0.3rem !important;
  border-bottom-left-radius: 0.3rem !important;
}

.classic .btn-outline-custom2:last-child {
  border-top-right-radius: 0.3rem !important;
  border-bottom-right-radius: 0.3rem !important;
}

.classic .border.custom-border {
  border-radius: 0.35rem;
}

#modal-tazapay___BV_modal_body_ {
  height: 500px;
  padding: 0;
  background-color: #f4f5f7; /* Same color as Tazapay iframe */
}

#checkoutPage {
  #card-payment-number,
  #card-payment-expiry,
  #card-payment-cvc {
    padding: 12px;
    border: 1px solid rgba(50, 50, 93, 0.2);
    height: 44px;
    background-color: white;
  }

  #stripe-iDEAL {
    border: 1px solid rgba(50, 50, 93, 0.2);
  }

  .btn.btn-paypal {
    background-color: rgb(0, 156, 222);
    color: #fff;
  }

  #formCheckout {
    z-index: 1;

    .form-group {
      position: relative;
    }

    .border-option {
      border: 1px solid #ced4da;

      .delivery-item:not(:last-child),
      .item-gateway:not(:last-child) > div {
        border-bottom: 1px solid #ced4da;
      }
    }

    .col-form-label {
      z-index: 100;
      transition: all .2s;
      color: #737373;
      font-weight: normal;
      width: 100%;
      font-size: 16px;
      padding: 11px 13px !important;
      position: absolute;
      height: 45px;

      &.pr-7 {
        padding-right: 4rem !important;
      }

    }

    input.form-control,
    select.custom-select {
      position: relative;
      transition: all .2s;
      height: 45px;
      color: black;
      background-color: transparent;
      z-index: 100;
    }

    .vti__input, .inner-label-input.show {
      .col-form-label {
        color: #737373;
        font-weight: normal;
        width: 100%;
        height: auto;
        font-size: 12px;
        padding: 3px 14px !important;
        position: absolute;
      }

      input.form-control,
      select.custom-select {
        padding-top: 1.2rem;
      }
    }

    .vue-tel-input {
      border-color: #ced4da;
      padding: 5px 0;

      .vti__dropdown {
        .vti__dropdown-arrow {
          right: 18px;
        }

        .vti__country-code {
          font-size: 1rem;
        }

        .vti__dropdown-list {
          width: 100%;
          min-height: 200px !important;
          z-index: 101;
        }
      }

      .vti__input.vti__search_box {
        width: 92%;
        margin-left: 20px;
      }

      &:focus-within {
        border-color: #80bdff;
        box-shadow: none;
      }
    }
  }

  .icon-card {
    height: 25px;
    opacity: 0.5;

    &.active {
      opacity: 1;
    }

    svg {
      height: 100%;
    }
  }

  .phone-tooltip {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 50%;
    height: 15px;
    width: 15px;
    text-align: center;
    line-height: 15px;
    z-index: 100;
    cursor: pointer;

    .tooltip-content {
      position: absolute;
      display: none;
      background-color: rgb(66, 66, 66);
      width: max-content;
      border-radius: 10px;
      padding: 0.5rem;
      top: -100%;
      color: white;
      left: 50%;
      transform: translate(-50%, -100%);
      max-width: 250px;

      &::after {
        content: "";
        position: absolute;
        bottom: -9px;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 10px solid rgb(66, 66, 66);
        left: 50%;
        transform: translateX(-50%);
      }

      @media (max-width: 992px) {
        transform: translate(-80%, -100%);

        &::after {
          left: 80%;
        }
      }
    }

    &:hover {
      .tooltip-content {
        display: inherit;
      }
    }
  }

  .country-chooser {
    .vs__dropdown-toggle {
      border-radius: unset !important;
      height: calc(1.5em + 1rem + 2px);
      padding: 0.5rem 1rem;
      font-size: 1.25rem;
      line-height: 1.5;
      width: 100%;
      font-weight: 400;
      color: #495057;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

      .vs__selected,
      .vs__search {
        margin: 0;
        padding: 0;
      }

      &:hover,
      &:focus-within {
        border-color: var(--primary-color);
      }
    }

    &.vs--disabled{
      .vs__dropdown-toggle {
        background-color: #f8f8f8;
      }
    }
  }

  .email-checkbox,
  .shiping-insurance-checkbox {
    .label-text {
      font-size: .75rem;
    }
  }

  .no-ship-box {
    background-color: #fff6e6;
  }

  span.note {
    color: red;
  }

  .input-warning {
    border-color: rgb(255, 145, 0);
    color: rgb(255, 145, 0);
  }

  .text-warning-email {
    color: rgb(255, 145, 0);
  }
}

.btn-confirm {
  font-size: 1.1rem !important;
}

.country-bank-select {
  .vs__dropdown-toggle {
    border-radius: unset;
  }
}

.input-error {
  border-color: #dc3545 !important;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.input-success {
  border-color: #28a745 !important;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.text-stripe {
  font-size: 86%;
}

.text-gray2 {
  color: #737373;
}

.bg-custom {
  background-color: var(--primary-color) !important;
}

.collapsed  .when-open,
.not-collapsed  .when-closed {
  display: none;
}

.order-time {
  @media (max-width: 767px) {
    height: 60px;
    display: flex;
    align-items: center;
  }
}

#paypal-button-container {
  @media only screen and (min-width: 600px) {
    min-height: 11rem;
  }
}
#paypal-button-container * {
  border: none;
}

.sc-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh !important;
  z-index: 99999;
}
</style>
