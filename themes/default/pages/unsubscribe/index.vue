<template>
  <main id="unsubscribePage" class="container-xl py-4 text-center">
    <div v-if="isLoadingSubmit">
      <loading-dot variant="dark" />
    </div>
    <div v-else-if="isSubmitSuccess">
      <h4>
        {{ $t('Unsubscribe Success') }}
      </h4>
      <h5 class="mt-3">
        {{ email }}
      </h5>
      <img :src="`images/unsubscribe.webp`" alt="unsubscribe" loading="lazy" height="400">
      <h6 class="mt-3">
        <nuxt-link :to="localePath('/')" class="text-info">
          {{ $t('Back to shopping') }}
        </nuxt-link>
      </h6>
    </div>
    <div v-else>
      <h4>
        {{ $t('Unsubscribe') }}
      </h4>
      <h5 class="mt-2">
        {{ email }}
      </h5>
      <h6 class="mt-2">
        {{ $t('We are sorry to see you go!') }}
      </h6>
      <img :src="`images/unsubscribe.webp`" alt="unsubscribe" loading="lazy" height="400">
      <p>{{ $t('Are you sure you wish to unsubscribe from all emails') }}</p>
      <div class="mt-3 ">
        <nuxt-link :to="localePath('/')">
          <b-button variant="secondary" class="mr-4 px-5 text-uppercase">
            {{ $t('No') }}
          </b-button>
        </nuxt-link>
        <b-button variant="primary" class="ml-4 px-5 text-uppercase" @click="handleSubmit">
          {{ $t('Yes') }}
        </b-button>
      </div>
    </div>
  </main>
</template>

<script>
import unsubscribe from '~/mixins/unsubscribe'

export default {
  name: 'Unsubscribe',
  mixins: [unsubscribe]
}
</script>
