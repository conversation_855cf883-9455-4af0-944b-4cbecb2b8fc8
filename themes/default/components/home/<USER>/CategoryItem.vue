<template>
  <div
    v-click-not-drag="goToPage"
    class="category-item text-center border-none shadow-hover-2"
  >
    <div class="thumb">
      <product-img :path="category.thumb_url" type="list" error-path="/images/collection1.jpg" />
    </div>
    <div class="py-4">
      <h5>{{ index }}</h5>
      <h6>{{ category.name }}</h6>
    </div>
  </div>
</template>

<script>
import productImg from '~/components/productImg.vue'

export default {
  components: { productImg },
  // eslint-disable-next-line vue/require-prop-types
  props: ['category', 'index'],
  data () {
    return {
      pictureType: 1
    }
  },
  methods: {
    goToPage,
    handerErrror
  }
}

function goToPage () {
  const query = this.$route.query.sort ? { sort: this.$route.query.sort } : { }
  this.$router.push(this.localePath({ path: `/category/${this.category.slug}`, query }))
}

function handerErrror (e) {
  if (this.pictureType === 1) {
    this.pictureType = 2
  } else if (this.pictureType === 2) {
    this.pictureType = 0
  }
}
</script>

<style lang="scss">
</style>
