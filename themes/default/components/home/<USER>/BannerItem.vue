<template>
  <div v-click-not-drag="goToPage" class="banner-slider d-flex justify-content-center">
    <nuxt-link :to="localePath(banner.banner_link && banner.banner_link !== '#' ? banner.banner_link : '/')">
      <product-img
        :alt="$store.state.storeInfo && $store.state.storeInfo.name"
        :path="banner.banner_url"
        type="full"
        type-mobile="banner_mobile"
        error-path="images/placeholder_1300x360.jpg"
        :hide-loading="true"
      />
    </nuxt-link>
  </div>
</template>

<script>
import productImg from '~/components/productImg.vue'

export default {
  components: { productImg },
  // eslint-disable-next-line vue/require-prop-types
  props: ['banner', 'index'],
  methods: {
    goToPage
  }
}

function goToPage () {
  this.$router.push(this.localePath(this.banner.banner_link && this.banner.banner_link !== '#' ? this.banner.banner_link : '/'))
}
</script>
