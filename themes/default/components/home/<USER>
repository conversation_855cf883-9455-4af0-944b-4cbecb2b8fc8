<template>
  <section id="BestPrice">
    <div class="container-xl h-100 d-flex justify-content-md-end justify-content-center align-items-center px-0">
      <div id="BestPriceContentBox" class="p-3">
        <p class="h5 font-weight-bold">
          From March 11
        </p>
        <p class="display-4 font-weight-bold">
          SALE
        </p>
        <p class="h6 fw-bold">
          Lorem Ipsum is simply dummy text of the printing and typesetting industry. Standard since the 1500s.
        </p>
        <p class="h2 font-weight-bold">
          30% OFF
        </p>
        <b-button variant="outline-danger" class="text-uppercase float-right border-none px-5 border-radius-none">
          {{ $t('Shop now') }}
        </b-button>
      </div>
    </div>
  </section>
</template>

<script>

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['categories']
}
</script>

<style lang="scss">
#BestPrice{
  background-image: url(`${$config.publicPath}/images/background-section2.jpg`);
  min-height: 500px;
  margin-top: -80px;
  padding: 100px 0;
  @media (max-width:1300px) {
    padding: 50px;
    margin-top: 0px;
  }
  #BestPriceContentBox{
    background-color: #FAEDDD;
    color: #D83027;
    width: 100%;
    max-width: 545px;
    button{
      @media (max-width:767px) {
        display: block;
        width: 100%;
      }
      background-color: #D83027;
      color: white;
    }
  }
}
</style>
