<template>
  <div id="CarouselBanner">
    <div v-if="banners.length === 1" class="d-flex justify-content-center single-banner">
      <nuxt-link :to="localePath(banners[0].banner_link && banners[0].banner_link !== '#' ? banners[0].banner_link : '/')">
        <product-img
          :alt="$store.state.storeInfo && $store.state.storeInfo.name"
          :path="banners[0].banner_url"
          type="full"
          type-mobile="banner_mobile"
          error-path="images/placeholder_1300x360.jpg"
          :hide-loading="true"
        />
      </nuxt-link>
    </div>
    <custom-carousel v-else :setting="setting">
      <banner-item v-for="(banner, index) in banners" :key="index" :banner="banner" :index="index" />
    </custom-carousel>
  </div>
</template>

<script>
import CustomCarousel from '../common/CustomCarousel.vue'
import BannerItem from './Element/BannerItem.vue'
import productImg from '~/components/productImg.vue'

export default {
  components: { CustomCarousel, BannerItem, productImg },
  // eslint-disable-next-line vue/require-prop-types
  props: ['banners'],
  data () {
    return {
      setting: {
        dots: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        initialSlide: 0
      }
    }
  }
}
</script>

<style lang="scss">
#CarouselBanner {
  z-index: 0;

  @media (max-width:767px) {
    margin-top: 0px;
  }

  .single-banner,
  .banner-slider {
    img,
    .spinner {
      max-width: 100%;
      object-fit: cover;
      &.loading {
        object-fit: none;
      }
    }
  }

  .slick-dots{
    bottom: 30px;
  }

  .custom-arrow{
    &.prev-arrow{
        left: 10px;
    }
    &.next-arrow{
        right: 10px;
    }
  }
}
</style>
