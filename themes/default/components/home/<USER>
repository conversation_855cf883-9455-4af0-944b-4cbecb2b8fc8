<template>
  <div id="CarouselCollection" class="w-100">
    <custom-carousel :setting="setting">
      <collection-item v-for="(banner, index) in banners" :key="index" :banner="banner" :index="index" />
    </custom-carousel>
  </div>
</template>

<script>
import CustomCarousel from '../common/CustomCarousel.vue'
import CollectionItem from './Element/CollectionItem.vue'

export default {
  components: { CustomCarousel, CollectionItem },
  // eslint-disable-next-line vue/require-prop-types
  props: ['banners'],
  data () {
    return {
      setting: {
        dots: true,
        centerMode: true,
        slidesToShow: 5,
        slidesToScroll: 5,
        rows: 1,
        responsive: [
          {
            breakpoint: 1023,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 3,
              rows: 1
            }
          },
          {
            breakpoint: 767,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 2,
              centerMode: false,
              rows: this.banners && this.banners.length > 4 ? 2 : 1
            }
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss">
#CarouselCollection {
  z-index: 0;

  @media (max-width:767px) {
    margin-top: 0px;
  }

  .banner-collection-item {
    img,
    .spinner {
      object-fit: cover;
      &.loading {
        object-fit: none;
      }
    }
  }

  .custom-arrow{
    &.prev-arrow{
        left: 10px;
    }
    &.next-arrow{
        right: 10px;
    }
  }
}
</style>
