<template>
  <div class="p-3 position-relative">
    <div v-click-not-drag="goToPage">
      <nuxt-link :to="localePath(banner.banner_link)" class="banner-collection-item">
        <product-img
          :path="banner.banner_url"
          :alt="banner.banner_text"
          type="collection_banner"
          :title="banner.banner_text"
        />
        <b-button variant="outline-custom" class="banner-collection-btn">
          {{ banner.banner_text }}
        </b-button>
      </nuxt-link>
    </div>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['banner'],
  methods: {
    goToPage
  }
}

function goToPage () {
  this.$router.push(this.localePath(this.banner.banner_link || '/'))
}
</script>

<style lang="scss">
.banner-collection-item {
  img {
    max-width: 100%;
  }

  a, image {
    user-drag: none;
    -webkit-user-drag: none;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    pointer-events: none;
  }

  .banner-collection-btn {
    background-color: rgba($color: #ffffff, $alpha: .6);
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 7px!important;
    font-weight: 600;
    max-width: 100%;
    width: fit-content;

    @media (max-width: 767px) {
      font-size: 12px;
      font-weight: 500;
      padding: 2px 10px;
    }
  }
}
</style>
