
<template>
  <header id="PageHeader" class="site-header">
    <b-navbar toggleable="md" :sticky="!isCampaignPage&&(!isHomePage||isSticky)" class="py-0 py-md-2">
      <div class="container-xl p-md-0">
        <div class="d-flex">
          <b-navbar-toggle target="nav-collapse" class="p-0 border-none">
            <b-nav-item is="div">
              <i class="icon-sen-menu" />
            </b-nav-item>
          </b-navbar-toggle>
          <b-navbar-nav class="d-md-none">
            <b-nav-item :to="localePath('/page/faq')">
              <i class="icon-sen-help-circle-outline" />
            </b-nav-item>
          </b-navbar-nav>
        </div>
        <b-navbar-brand class="m-0 order-md-1 mr-md-3">
          <nuxt-link :to="localePath('/')" class="site-logo">
            <img
              v-if="storeInfo && storeInfo.logo_url"
              :src="$imgUrl(storeInfo.logo_url,'logo')"
              :onerror="`this.onerror=null;this.src='${$config.publicPath}/images/logo-placeholder.png';`"
              :alt="storeInfo.name"
              loading="lazy"
            >
            <span v-else>
              {{ storeInfo.name }}
            </span>
          </nuxt-link>
        </b-navbar-brand>
        <b-collapse id="nav-collapse" is-nav class="order-md-2">
          <b-navbar-nav>
            <li
              v-for="(menu, index) in storeInfo.headerMenu"
              :key="index"
              class="nav-item"
              :class="{'has-sub-menu': menu.submenu && menu.submenu.length}"
            >
              <nuxt-link
                v-if="menu.url.includes('/' + currentDomain) || menu.url.startsWith('/')"
                exact
                :to="localePath(menu.url)"
                class="nav-link nuxt-link-active"
                target="_self"
              >
                {{ menu.title }}
              </nuxt-link>
              <span v-else-if="menu.url === '#'" class="nav-link nuxt-link-active">
                {{ menu.title }}
              </span>
              <a v-else :href="menu.url" class="nav-link nuxt-link-active" target="_blank">
                {{ menu.title }}
              </a>
              <div v-if="menu.submenu && menu.submenu.length" class="d-md-none toggle-icon" @click="tabShowLv1===index ? tabShowLv1 = false : tabShowLv1 = index; tabShowLv2 = false">
                <i :class="tabShowLv1===index? 'icon-sen-chevron-down': 'icon-sen-chevron-right'" />
              </div>
              <ul class="submenu menu-lv1 d-md-block" :class="{'d-none': tabShowLv1!==index}">
                <li
                  v-for="(menu2, menu2_index) in menu.submenu"
                  :key="menu2_index"
                  class="nav-item"
                  :class="{'has-sub-menu': menu2.submenu && menu2.submenu.length}"
                >
                  <nuxt-link
                    v-if="menu2.url.includes('/' + currentDomain) || menu2.url.startsWith('/')"
                    exact
                    :to="localePath(menu2.url)"
                    class="nav-link nuxt-link-active text-overflow-hidden"
                    target="_self"
                  >
                    {{ menu2.title }}
                  </nuxt-link>
                  <span v-else-if="menu2.url === '#'" class="nav-link nuxt-link-active">
                    {{ menu2.title }}
                  </span>
                  <a v-else :href="menu2.url" class="nav-link nuxt-link-active text-overflow-hidden" target="_blank">
                    {{ menu2.title }}
                  </a>
                  <div v-if="menu2.submenu && menu2.submenu.length" class="d-md-none toggle-icon" @click="tabShowLv2 === menu2_index ? tabShowLv2 = false: tabShowLv2= menu2_index">
                    <i :class="tabShowLv2===menu2_index? 'icon-sen-chevron-down': 'icon-sen-chevron-right'" />
                  </div>
                  <ul class="submenu menu-lv2 d-md-block" :class="{'d-none': tabShowLv2!==menu2_index}">
                    <li
                      v-for="(menu3, menu3_index) in menu2.submenu"
                      :key="menu3_index"
                      class="nav-item"
                    >
                      <nuxt-link
                        v-if="menu3.url.includes('/' + currentDomain) || menu3.url.startsWith('/')"
                        exact
                        :to="localePath(menu3.url)"
                        class="nav-link nuxt-link-active text-overflow-hidden"
                        target="_self"
                      >
                        {{ menu3.title }}
                      </nuxt-link>
                      <span v-else-if="menu3.url === '#'" class="nav-link nuxt-link-active">
                        {{ menu3.title }}
                      </span>
                      <a v-else :href="menu3.url" class="nav-link nuxt-link-active text-overflow-hidden" target="_blank">
                        {{ menu3.title }}
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </b-navbar-nav>
        </b-collapse>
        <b-navbar-nav class="right-menu order-md-3">
          <b-nav-text v-click-outside="closeSearchModal" class="d-none d-md-block nav-item p-0 position-relative btn btn-custom align-self-center">
            <b-button
              size="sm"
              variant="outline-custom"
              class="nav-link d-lg-none"
            >
              <i class="icon-sen-search" @click="openSearchModal" />
            </b-button>
            <b-form
              v-show="isOpenSearchBox"
              class="searchInput position-absolute d-lg-flex mr-1"
              @submit.prevent.stop="searchAction"
            >
              <b-form-input
                id="search-keyword"
                ref="searchInput"
                v-model="searchKeyword"
                name="s"
                type="search"
                :placeholder="`${$t('Search')} (Ctrl + /)`"
                class="p-2 border-none"
                @mouseover="$refs.searchInput.focus()"
              />
              <b-button
                size="sm"
                variant="outline-custom"
                type="submit"
                class="font-weight-bold border-none icon-sen-search d-flex justify-content-center align-items-center bg-transparent"
              />
            </b-form>
          </b-nav-text>
          <b-nav-item class="faq-icon font-weight-unset pl-0 py-1 btn btn-custom d-none d-md-block" :to="localePath('/page/faq')">
            <i class="icon-sen-help-circle-outline" />
          </b-nav-item>
          <b-nav-item link-classes="pl-1" class="font-weight-unset pl-0 py-0  btn btn-custom" :to="localePath('/cart')">
            <i class="icon-sen-cart" />
            <span class="small-badge bg-danger">{{ cartTotalQuantity }}</span>
          </b-nav-item>
        </b-navbar-nav>
      </div>
    </b-navbar>
    <div class="container-xl p-md-0 px-0">
      <b-form
        class="searchInputMobile d-md-none border-top border-bottom"
        @submit.prevent.stop="searchAction"
      >
        <b-button
          size="sm"
          variant="outline-custom"
          type="submit"
          class="pl-3 pr-0 font-weight-bold border-none icon-sen-search d-flex justify-content-center align-items-center bg-transparent"
        />
        <b-form-input
          id="search-mobile"
          ref="searchInput"
          v-model="searchKeyword"
          name="s"
          type="search"
          :placeholder="`${$t('Search')} (Ctrl + /)`"
          class="p-2 border-none"
          @mouseover="$refs.searchInput.focus()"
        />
      </b-form>
    </div>
  </header>
</template>

<script>
import headerMixin from '~/mixins/header'

export default {
  mixins: [headerMixin],
  data () {
    return {
      tabShowLv1: false,
      tabShowLv2: false
    }
  }
}

</script>

<style lang="scss">
#PageHeader {
  z-index: 100;
  transition: all 0.5s ease;
  min-height: 80px;

  &.sticky-top{
    background-color: white;
  }

  .site-logo {
    font-family: 'Keep Calm Regular';
    img {
      max-height: 30px;
      margin-bottom: 5px;
    }
  }

  .nav-link,
  .navbar-text {
    font-weight: 500;
    color: black;

    &:hover, &.active{
      color: var(--primary-color);
    }
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .right-menu{
    flex-direction: row;

    .faq-icon i{
      font-size: 20px;
    }
  }

  .navbar-toggler{
    i{
      color: black;
    }
  }

  @media (max-width:767px) {
    min-height: 65px;

    #nav-collapse{
      background-color: white;
      width: 100%;
      position: absolute;
      top: 50px;
      left: 0;
    }

    .navbar-brand {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      img {
        height: 22px;
        margin-bottom: 7px;
      }
    }
  }

  .searchInput {
    // position: absolute;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    display: flex;
    right: -50px;
    background-color: white;

    @media (min-width: 992px) {
      right: 0;
      background-color: transparent;
      box-shadow: none;
      position: relative !important;
    }

    &:focus-within{
      box-shadow: 0 0 2px 1px var(--primary-color);
      backdrop-filter: blur(10px);
    }

    input {
      background-color: transparent;
      min-width: 130px;

      &:focus {
        box-shadow: none;
      }
    }

    i {
      position: absolute;
      right: 10px;
      font-size: 0.875rem !important;
      margin-top: 13px;
    }
  }

  .searchInputMobile {
    // position: absolute;
    display: flex;
    background-color: white;

    input {
      background-color: transparent;
      &:focus {
        box-shadow: none;
      }
    }

    i {
      position: absolute;
      right: 10px;
      font-size: 0.875rem !important;
      margin-top: 13px;
    }
  }

  .icon-sen-menu, .icon-sen-search, .icon-sen-cart {
    font-size: 1.5rem;
    font-weight: bold;
  }

  @media (min-width: 768px) {
    .submenu {
      position: absolute;
      visibility: hidden;
      opacity: 0;
      list-style: none;
      padding-left: 0px;
      width: max-content;
      background: white;
      padding: .5rem 0;
      transition: visibility 0s, opacity 0.3s linear;
      min-width: 150px;
      -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    }

    .menu-lv1 {
      left: 0;
    }

    .menu-lv2 {
      top: -0.5rem;
      left: 100%;
    }

    .nav-item {
      position: relative;
      padding: .25rem .75rem;
      &.has-sub-menu:hover > .submenu{
        visibility: visible;
        opacity: 1;
      }
    }
  }

  .submenu {
    list-style: none;
  }

  .nav-item {
    position: relative;
    .toggle-icon {
      width: 100%;
      position: absolute;
      text-align: right;
      font-size: 1.5rem;
      cursor: pointer;
      z-index: 100;
      top: 0;
      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

</style>
