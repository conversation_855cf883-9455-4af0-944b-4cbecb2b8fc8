<template>
  <div id="CarouselBannerGoogleShopping">
    <custom-carousel :size="setting">
      <div v-for="(banner, index) in banners" :key="index" class="banner-slider">
        <product-img
          alt="store_banner"
          :path="banner.banner_url"
          type="banner_hd"
          type-mobile="banner_mobile"
          error-path="/images/placeholder_1300x360.jpg"
          :hide-loading="true"
        />
      </div>
    </custom-carousel>
    <div v-if="true" id="PersonalBox">
      <div class="h1 text-uppercase font-weight-400 mb-0">
        {{ $t('Make it') }}
      </div>
      <div class="h1 text-uppercase font-weight-600">
        {{ $t('Personal') }}
      </div>
      <div>
        {{ $t('Find a shirt that’s just for you!') }}
      </div>
      <b-form>
        <b-form-group
          :label="$t('Your name')"
          label-for="banner-input-name"
          label-class="text-gray"
        >
          <b-form-input
            id="banner-input-name"
            v-model="form.name"
            type="text"
            :placeholder="$t('Enter your name')"
            required
          />
        </b-form-group>
        <b-form-group
          :label="$t('Birth year')"
          label-for="banner-input-birth-year"
          label-class="text-gray"
        >
          <b-form-input
            id="banner-input-birth-year"
            v-model="form.birthYear"
            type="text"
            :placeholder="$t('What year were you born?')"
            required
          />
        </b-form-group>
        <b-form-group
          :label="$t('Home town')"
          label-for="banner-input-home-town"
          label-class="text-gray"
        >
          <b-form-input
            id="banner-input-home-town"
            v-model="form.homeTown"
            type="text"
            :placeholder="$t('Where do you live?')"
            required
          />
        </b-form-group>
        <b-form-group
          :label="$t('Job title')"
          label-for="banner-input-job-title"
          label-class="text-gray"
        >
          <b-form-input
            id="banner-input-job-title"
            v-model="form.jobTitle"
            type="text"
            :placeholder="$t(`What's your job title?`)"
            required
          />
        </b-form-group>
        <b-form-group
          :label="$t('Interest')"
          label-for="banner-input-interest"
          label-class="text-gray"
        >
          <b-form-input
            id="banner-input-interest"
            v-model="form.interest"
            type="text"
            :placeholder="$t('Pet?Motocycles?...')"
            required
          />
        </b-form-group>

        <b-button type="submit" block variant="custom" class="text-uppercase font-weight-500 my-4 d-flex align-items-center justify-content-center">
          <i class="icon-sen-search" />
          <span class="px-2">{{ $t('Find my shirt') }}</span>
        </b-button>
      </b-form>
    </div>
  </div>
</template>

<script>
import CustomCarousel from '../common/CustomCarousel.vue'

export default {
  components: { CustomCarousel },
  // eslint-disable-next-line vue/require-prop-types
  props: ['banners'],
  data () {
    return {
      setting: {
        dots: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        initialSlide: 0
      },
      form: {
        name: '',
        birthYear: '',
        homeTown: '',
        jobTitle: '',
        interest: ''
      }
    }
  }
}
</script>

<style lang="scss">
#CarouselBannerGoogleShopping {
  z-index: 0;

  .banner-slider {
    height: 100vh;
    max-height: calc(100vw / 16 * 9);

    img,
    .spinner {
      height: 100%;
      width: 100%;
      object-fit: contain;
      &.loading {
        object-fit: none;
      }
    }
  }

  .slick-dots{
    bottom: 30px;
  }

  .custom-arrow{
    &.prev-arrow{
        left: 10px;
    }
    &.next-arrow{
        right: 10px;
    }
  }
  &:hover{
    .custom-arrow{
      background-color: rgba(255, 255, 255, 0.25);
      color: white;
    }
  }

  #PersonalBox {
    background-color: #333333;
    color: #fff;
    padding: 20px;
    border-radius: 10px;

    @media (min-width: 768px) {
      min-width: 310px;
      position: absolute;
      right: 10%;
      top: 10%;
    }

    .form-group {
      label {
        margin-bottom: 0;
      }

      input {
        border-radius: 3px!important;

        &::placeholder{
          color:#909090;
        }
      }
    }

    button {
      border-radius: 3px!important;

      i {
        font-size: 16px;
      }
    }
  }
}
</style>
