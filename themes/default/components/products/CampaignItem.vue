<template>
  <div
    class="campaign-item text-center border-none shadow-hover-2 personalize"
  >
    <div v-click-not-drag="goToPage">
      <nuxt-link :to="localePath(campaignUrl)" class="d-block thumb">
        <product-img :path="campaign.thumb_url" type="list" :alt="campaign.name" :color="color ? $colorVal(color) : ''" :title="campaignTitle" />
      </nuxt-link>
    </div>
    <div class="pt-4">
      <h5 v-click-not-drag="goToPage" class="text-hover-color-primary px-2 text-overflow-hidden mb-0" :title="campaignTitle">
        <nuxt-link :to="localePath(campaignUrl)">
          {{ campaignTitle }}
        </nuxt-link>
      </h5>
      <h6 class="d-flex flex-wrap w-100 h-50px justify-content-center align-items-center mb-0 gap-2">
        <span class="main-price text-custom">{{ $formatPrice(correctedCampaign.price + setDynamicBaseCostIndex(correctedCampaign), campaign.currency_code) }}</span>
        <del v-if="$store.state.storeInfo.store_type !== 'google_ads' && !$store.state.storeInfo.disable_pre_discount && correctedCampaign.old_price && correctedCampaign.old_price > correctedCampaign.price" class="old-price">{{ $formatPrice(correctedCampaign.old_price, campaign.currency_code) }}</del>
      </h6>
      <div class="p-1 p-sm-2">
        <b-button
          v-if="itemEnableAddToCart"
          size="lg"
          variant="custom"
          block
          class="p-1 p-sm-2 add-to-cart-btn font-weight-600 text-uppercase border-radius-none"
          @click.prevent.stop="openCampaignModal"
        >
          <loading-dot v-if="isLoadingData" variant="light" />
          <span v-else> {{ $t('Add to cart') }}  </span>
        </b-button>
      </div>
    </div>
    <personalize-tag v-if="campaign.personalized" />
  </div>
</template>

<script>
import PersonalizeTag from '~/components/PersonalizeTag'

export default {
  components: {
    PersonalizeTag
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['campaign', 'isCarouselItem', 'color'],
  data () {
    return {
      isLoadingData: false,
      itemEnableAddToCart: this.$store.state.storeInfo.enable_add_to_cart ?? true
    }
  },
  computed: {
    campaignTitle,
    campaignUrl,
    correctedCampaign () {
      return this.$correctTestPriceCampaignAtListing({ ...this.campaign })
    }
  },
  methods: {
    goToPage,
    openCampaignModal,
    setDynamicBaseCostIndex
  }
}

function campaignTitle () {
  if (this.campaign.campaign_name) {
    return `${this.campaign.campaign_name} - ${this.campaign.name}`
  } else {
    return this.campaign.name
  }
}

function campaignUrl () {
  let link = `/${this.campaign.slug}`

  if (this.campaign.product_type === 'product' && this.campaign.campaign_id) {
    link = `/${this.campaign.slug}?product=${this.$toSlug(this.campaign.name)}${this.color ? '&color=' + this.color.replace(/ /g, '-') : ''}`
  }

  return link
}

function goToPage () {
  this.$router.push(this.localePath(this.campaignUrl))
}

async function openCampaignModal () {
  const params = new URLSearchParams(this.campaignUrl)
  const query = Object.fromEntries(params.entries())
  if (this.campaign.product_type === 'shortcut') {
    return this.$router.push(this.localePath(this.campaignUrl))
  }
  this.$updateAdsSource(query)
  this.isLoadingData = true
  await this.$store.dispatch('campaign/openCampaignModal', this.campaign.slug)
  this.isLoadingData = false
}

function setDynamicBaseCostIndex (campaign) {
  try {
    if (!campaign.default_variants && campaign.default_variants === undefined && campaign.default_variants.length === 0 && campaign.default_variants.variant_options) {
      return 0
    }
    const dynamicBaseCostIndex = this.$getDynamicBaseCostIndex({
      currentCampaign: campaign,
      currentOption: campaign.variant_options,
      optionList: campaign.product_options,
      variants: campaign.default_variants,
      variantsCurrency: campaign.currency_code
    })

    return dynamicBaseCostIndex
  } catch (e) {
    return 0
  }
}
</script>

<style lang="scss">
.campaign-item {
  position: relative;
  a {
    user-drag: none;
    -webkit-user-drag: none;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
  }

  h5 {
    font-size: 16px;
    color: black;

    &::first-letter {
      text-transform: capitalize;
    }
  }

  .add-to-cart-btn {
    font-size: 16px !important;

    @media (max-width: 424px) {
      min-height: 40px;
      font-size: 14px !important;
    }
  }
}
</style>
