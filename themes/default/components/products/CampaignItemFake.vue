<template>
  <div
    class="p-0 col-6 col-md-4 col-lg-3 campaign-item text-center border-none shadow-hover-2"
  >
    <div class="thumb">
      <div class="spinner d-flex justify-content-center align-items-center bg-white">
        <b-spinner variant="gray" />
      </div>
    </div>
    <div class="pt-4">
      <div class="p-4" />
      <h6 class="py-2">
        <span class="main-price">{{ $formatPrice(0) }}</span>
      </h6>
      <div class="p-1 p-sm-2">
        <b-button size="lg" variant="outline-custom3" block class="p-1 p-sm-2 add-to-cart-btn font-weight-600 text-uppercase">
          {{ $t('Add to cart') }}
        </b-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CampaignItemFake'
}
</script>

<style lang="scss">
.campaign-item{

  .add-to-cart-btn {
    min-height: 50px;
    font-size: 16px !important;

    @media (max-width: 424px) {
      min-height: 40px;
      font-size: 14px !important;
    }
  }
}
</style>
