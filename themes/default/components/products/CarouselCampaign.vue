<template>
  <custom-carousel
    class="carousel-product w-100"
    :setting="setting"
  >
    <div
      v-for="(campaign, index) in campaigns"
      :key="index"
      class="p-1 p-sm-3"
    >
      <campaign-item :campaign="campaign" :is-carousel-item="true" />
    </div>
  </custom-carousel>
</template>

<script>
import CustomCarousel from '../common/CustomCarousel'
import CampaignItem from './CampaignItem'

export default {
  components: {
    CustomCarousel,
    CampaignItem
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['campaigns'],
  data () {
    return {
      setting: {
        dots: true,
        slidesToShow: 4,
        slidesToScroll: 4,
        initialSlide: 0,
        rows: 1,
        responsive: [
          {
            breakpoint: 991,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 3,
              rows: 1
            }
          },
          {
            breakpoint: 767,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 2,
              rows: this.campaigns && this.campaigns.length > 2 ? 2 : 1
            }
          }
        ]
      }
    }
  }
}
</script>
