<template>
  <div class="product-cart-item row py-3 border-bottom" data-test-id="product-cart-item">
    <div class="col-12 col-lg-7">
      <b-media no-body>
        <b-media-aside>
          <div class="shadow-hover-1">
            <product-img
              :path="cartItem.thumb_url"
              :alt="cartItem.campaign_title"
              type="listing"
              :color="cartItem.options.color?$colorVal(cartItem.options.color):''"
              @click.native="$store.commit('changeViewImagePath', cartItem.thumb_url)"
            />
          </div>
        </b-media-aside>
        <!-- size md -->
        <b-media-body class="d-none d-md-flex row m-1">
          <div class="col-11 col-lg-12 pl-1 ">
            <h5 class="text-overflow-hidden title" :title="cartItem.campaign_title">
              <nuxt-link :to="localePath(cartItem.product_url)">
                {{ cartItem.campaign_title }}
              </nuxt-link>
            </h5>
          </div>
          <div class="col-1 d-lg-none p-0">
            <b-button variant="outline-custom" data-test-id="remove-item" class="remove-item-button float-right" @click="isShowConfirmDeleteCartItem = true">
              <i class="icon-sen-delete" />
            </b-button>
          </div>
          <div v-if="campaign.products && campaign.products.length > 1" class="option-box col-12 col-sm-6 mt-0 mt-sm-2 px-1 text-center text-sm-left text-capitalize">
            <span v-if="cartItem.designs">
              {{ cartItem.product_name }}
            </span>
            <b-dropdown
              v-else
              variant="outline-custom"
              size="sm"
              block
              lazy
              toggle-class="border text-overflow-hidden text-capitalize pr-3"
              :text="cartItem.product_name"
            >
              <b-dropdown-item
                v-for="(productItem, productIndex) in campaign.products"
                :key="productIndex"
                :active="cartItem.product_id === productItem.id"
                @click="selectProduct(productItem)"
              >
                {{ productItem.name }}
              </b-dropdown-item>
            </b-dropdown>
          </div>
          <template v-for="(item, optionIndex) in Object.keys(optionsList)">
            <div
              v-if="optionsList[item].length > 1"
              :key="optionIndex"
              class="option-box col-12 col-sm-3 mt-2 text-center text-capitalize px-1"
            >
              <span v-if="cartItem.designs">
                {{ cartItem.options[item] }}
              </span>
              <b-dropdown
                v-else-if="optionsList[item].length > 1"
                variant="outline-custom"
                size="sm"
                block
                lazy
                :text="cartItem.options[item]"
                data-test-id="item-option-list-toggle"
                toggle-class="border text-overflow-hidden text-capitalize pr-3"
              >
                <b-dropdown-item
                  v-for="(optionItem, indexOption) in optionsList[item]"
                  :key="indexOption"
                  :active="optionItem===cartItem.options[item]"
                  data-test-id="item-option"
                  @click="updateOption(item, optionItem)"
                >
                  <i v-if="item === 'color'" class="icon-color" :style="`background-color: ${$colorVal(optionItem)}`" />
                  <span>
                    {{ optionItem }}
                  </span>
                </b-dropdown-item>
              </b-dropdown>
            </div>
          </template>
          <div v-if="cartItem.custom_options" class="col-12">
            <ul class="pl-2">
              <template v-for="(value, key) in cartItem.custom_options">
                <li :key="key" class="text-capitalize" style="font-size: 13px;">
                  <div v-if="key === 'customImage'">
                    Your image: <a :href="$imgUrl(value, 'full')" target="_blank" @click.prevent="$store.commit('changeViewImagePath', value)">View image</a>
                  </div>
                  <div v-else class="text-overflow-hidden">
                    {{ key.replace(/_/g, ' ') }}: {{ value }}
                  </div>
                </li>
              </template>
            </ul>
          </div>
          <div v-if="cartItem.customer_custom_options" class="col-12">
            <ul v-for="(v, i) in cartItem.customer_custom_options" :key="i" class="pl-2">
              <template v-for="(vC, iC) in v">
                <li v-if="vC" :key="iC" style="font-size: 13px">
                  <div :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">
                    {{ $t(vC.label) }}:
                    <a v-if="vC.type === 'image' && vC.imagePath" :href="$imgUrl(vC.imagePath, 'full')" target="_blank" @click.prevent="$store.commit('changeViewImagePath', vC.imagePath)">View image</a>
                    <span v-else :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">{{ vC.value }}</span>
                  </div>
                </li>
              </template>
            </ul>
          </div>
          <div class="d-lg-none quantity-box d-flex col-4 mt-2 px-1">
            <b-button
              variant="outline-custom"
              size="sm"
              class="border bg-transparent"
              data-test-id="item-qty-decre"
              @click="updateQuantity( cartItem.quantity - 1 )"
            >
              <i class="icon-sen-minus" />
            </b-button>
            <b-form-input
              :value="cartItem.quantity"
              size="sm"
              type="number"
              class="mx-2 text-center"
              @input="value=>updateQuantity(value)"
            />
            <b-button
              variant="outline-custom"
              size="sm"
              class="border bg-transparent"
              data-test-id="item-qty-incre"
              @click="updateQuantity( cartItem.quantity + 1 )"
            >
              <i class="icon-sen-plus" />
            </b-button>
          </div>
          <div class="d-lg-none col-4 mt-2 d-flex justify-content-center align-items-center">
            <span class="font-weight-500">
              <span v-if="variant && variant.out_of_stock" class="text-danger">
                {{ $t('Out of stock') }}
              </span>
              <div v-else-if="totalPriceBundle" class="d-flex flex-wrap gap-2">
                <span>
                  {{ $formatPrice(totalPriceBundle, cartItem.currency_code) }}
                </span>
                <del class="text-secondary">
                  {{ $formatPrice(totalPrice, cartItem.currency_code) }}
                </del>
              </div>
              <span v-else-if="totalPrice">
                {{ $formatPrice(totalPrice, cartItem.currency_code) }}
              </span>
            </span>
          </div>
          <div class="d-lg-none col-4 mt-2 px-1">
            <b-button size="sm" data-test-id="add-more-item" variant="outline-danger" block @click="duplicateCartItem">
              {{ $t('Add more item') }}
            </b-button>
          </div>
        </b-media-body>
        <!-- size sm -->
        <b-media-body class="d-flex d-md-none row m-1">
          <div class="col-11 pl-1 d-grid">
            <h6 class="text-overflow-hidden" :title="cartItem.product_name">
              <nuxt-link :to="localePath(cartItem.product_url)">
                {{ cartItem.product_name }}
              </nuxt-link>
            </h6>
          </div>
          <div class="col-1 p-0">
            <b-button variant="outline-custom" data-test-id="remove-item" class="remove-item-button float-right" @click="isShowConfirmDeleteCartItem = true">
              <i class="icon-sen-delete" />
            </b-button>
          </div>
          <!-- on edit -->
          <template v-if="isEdit">
            <div v-if="campaign.products" class="option-box col-12 col-sm-6 mt-0 mt-sm-2 px-1 text-center text-capitalize">
              <span v-if="campaign.products.length===1 || cartItem.designs">
                {{ cartItem.product_name }}
              </span>
              <b-dropdown
                v-else
                variant="outline-custom"
                size="sm"
                block
                lazy
                toggle-class="border text-overflow-hidden text-capitalize pr-3"
                :text="cartItem.product_name"
              >
                <b-dropdown-item
                  v-for="(productItem, productIndex) in campaign.products"
                  :key="productIndex"
                  :active="cartItem.product_id===productItem.id"
                  @click="selectProduct(productItem)"
                >
                  {{ productItem.name }}
                </b-dropdown-item>
              </b-dropdown>
            </div>
            <template
              v-for="(item, optionIndex) in Object.keys(optionsList)"
            >
              <div
                v-if="optionsList[item].length > 1"
                :key="optionIndex"
                class="option-box col-12 col-sm-3 mt-2 text-center text-capitalize px-1"
              >
                <span v-if="cartItem.designs">
                  {{ cartItem.options[item] }}
                </span>
                <b-dropdown
                  v-if="optionsList[item].length > 1"
                  variant="outline-custom"
                  size="sm"
                  block
                  lazy
                  :text="cartItem.options[item]"
                  data-test-id="item-option-list-toggle-mobile"
                  toggle-class="border text-overflow-hidden text-capitalize pr-3"
                >
                  <b-dropdown-item
                    v-for="(optionItem, indexOption) in optionsList[item]"
                    :key="indexOption"
                    :active="optionItem===cartItem.options[item]"
                    data-test-id="item-option"
                    @click="updateOption(item, optionItem)"
                  >
                    <i v-if="item === 'color'" class="icon-color" :style="`background-color: ${$colorVal(optionItem)}`" />
                    <span>
                      {{ optionItem }}
                    </span>
                  </b-dropdown-item>
                </b-dropdown>
              </div>
            </template>
            <div class="quantity-box d-flex col-12 col-sm-4 mt-2 px-1">
              <b-button
                variant="outline-custom"
                size="sm"
                class="border bg-transparent"
                data-test-id="item-qty-decre"
                @click="updateQuantity( cartItem.quantity - 1 )"
              >
                <i class="icon-sen-minus" />
              </b-button>
              <b-form-input
                :value="cartItem.quantity"
                size="sm"
                type="number"
                class="mx-2 text-center"
                @input="value=>updateQuantity(value)"
              />
              <b-button
                variant="outline-custom"
                size="sm"
                class="border bg-transparent"
                data-test-id="item-qty-incre"
                @click="updateQuantity( cartItem.quantity + 1 )"
              >
                <i class="icon-sen-plus" />
              </b-button>
            </div>
            <div class="d-lg-none col-12 col-sm-4 mt-2 d-flex justify-content-center align-items-center">
              <span class="font-weight-500">
                <span v-if="variant && variant.out_of_stock" class="text-danger">
                  {{ $t('Out of stock') }}
                </span>
                <div v-else-if="totalPriceBundle" class="d-flex flex-wrap gap-2">
                  <span>
                    {{ $formatPrice(totalPriceBundle, cartItem.currency_code) }}
                  </span>
                  <del class="text-secondary">
                    {{ $formatPrice(totalPrice, cartItem.currency_code) }}
                  </del>
                </div>
                <span v-else-if="totalPrice">
                  {{ $formatPrice(totalPrice, cartItem.currency_code) }}
                </span>
              </span>
            </div>
            <div class="col-12 col-sm-4 mt-2 px-1">
              <b-button size="sm" variant="custom " block @click="isEdit = false">
                {{ $t('Done') }}
              </b-button>
            </div>
          </template>
          <!-- on view -->
          <template v-else>
            <div class="col-12 px-1 text-capitalize">
              <template v-for="(item, optionIndex) in Object.keys(optionsList)">
                <span
                  v-if="!cartItem.options[item].startsWith('__')"
                  :key="optionIndex"
                  class="text-uppercase"
                >
                  {{ cartItem.options[item] }}
                  <span v-if="optionIndex !== (Object.keys(optionsList).length - 1)">&nbsp;/&nbsp;</span>
                </span>
              </template>
            </div>
            <div v-if="cartItem.custom_options" class="col-12">
              <ul class="pl-2">
                <template v-for="(value, key) in cartItem.custom_options">
                  <li :key="key" class="text-capitalize" style="font-size: 13px;">
                    <div v-if="key === 'customImage'">
                      Your image: <a :href="$imgUrl(value, 'full')" target="_blank" @click.prevent="$store.commit('changeViewImagePath', value)">View image</a>
                    </div>
                    <div v-else class="text-overflow-hidden">
                      {{ key.replace(/_/g, ' ') }}: {{ value }}
                    </div>
                  </li>
                </template>
              </ul>
            </div>
            <div v-if="cartItem.customer_custom_options" class="col-12">
              <ul v-for="(v, i) in cartItem.customer_custom_options" :key="i" class="pl-2">
                <template v-for="(vC, iC) in v">
                  <li v-if="vC" :key="iC" style="font-size: 13px">
                    <div :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">
                      {{ $t(vC.label) }}:
                      <a v-if="vC.type === 'image' && vC.imagePath" :href="$imgUrl(vC.imagePath, 'full')" target="_blank" @click.prevent="$store.commit('changeViewImagePath', vC.imagePath)">View image</a>
                      <span v-else :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">{{ vC.value }}</span>
                    </div>
                  </li>
                </template>
              </ul>
            </div>
            <div class="col-12 px-1 mb-1 font-weight-500">
              <span>
                x{{ cartItem.quantity }}
              </span>
              <span class="ml-3 font-weight-500">
                <span v-if="variant && variant.out_of_stock" class="text-danger">
                  {{ $t('Out of stock') }}
                </span>
                <div v-else-if="totalPriceBundle" class="d-flex flex-wrap gap-2">
                  <span>
                    {{ $formatPrice(totalPriceBundle, cartItem.currency_code) }}
                  </span>
                  <del class="text-secondary">
                    {{ $formatPrice(totalPrice, cartItem.currency_code) }}
                  </del>
                </div>
                <span v-else-if="totalPrice">
                  {{ $formatPrice(totalPrice, cartItem.currency_code) }}
                </span>
              </span>
            </div>
            <div class="col-9 col-sm-4 px-1">
              <b-button size="sm" data-test-id="add-more-item" variant="outline-danger" block @click="duplicateCartItem">
                {{ $t('Add more item') }}
              </b-button>
            </div>
            <div class="col-3 col-sm-4 px-1 d-flex justify-content-center align-items-center">
              <b-button size="sm" variant="outline-custom" class="text-danger" data-test-id="item-edit-attribute" @click="isEdit = true">
                <u>{{ $t('Edit') }}</u>
              </b-button>
            </div>
          </template>
        </b-media-body>
      </b-media>
    </div>
    <div class="d-none d-lg-flex row col-5 mt-2">
      <div class="col-3 text-center font-weight-500">
        <div v-if="totalPriceBundle" class="d-flex flex-wrap gap-2">
          <span>
            {{ $formatPrice(priceBundle, cartItem.currency_code) }}
          </span>
          <del class="text-secondary">
            {{ $formatPrice(price, cartItem.currency_code) }}
          </del>
        </div>
        <span v-else-if="price" data-test-id="item-price">
          {{ $formatPrice(price, cartItem.currency_code) }}
        </span>
      </div>
      <div class="col-6">
        <div class="quantity-box d-flex justify-content-center">
          <b-button
            variant="outline-custom"
            size="sm"
            class="border bg-transparent"
            data-test-id="item-qty-decre"
            @click="updateQuantity(cartItem.quantity - 1)"
          >
            <i class="icon-sen-minus" />
          </b-button>
          <b-form-input
            :value="cartItem.quantity"
            size="sm"
            type="number"
            data-test-id="item-qty-input"
            class="mx-2 quantity-input text-center"
            @input="value=>updateQuantity(value)"
          />
          <b-button
            variant="outline-custom"
            size="sm"
            class="border bg-transparent"
            data-test-id="item-qty-incre"
            @click="updateQuantity(cartItem.quantity + 1)"
          >
            <i class="icon-sen-plus" />
          </b-button>
        </div>
        <b-button
          variant="outline-custom"
          size="sm"
          block
          class="mt-3 border bg-transparent"
          data-test-id="add-more-item"
          @click="duplicateCartItem"
        >
          <small>
            {{ $t('Add more item') }}
          </small>
        </b-button>
      </div>
      <div class="col-3 text-center font-weight-500">
        <span v-if="variant && variant.out_of_stock" data-test-id="item-oos" class="text-danger">
          {{ $t('Out of stock') }}
        </span>
        <div v-else-if="totalPriceBundle" class="d-flex flex-wrap gap-2">
          <span>
            {{ $formatPrice(totalPriceBundle, cartItem.currency_code) }}
          </span>
          <del class="text-secondary">
            {{ $formatPrice(totalPrice, cartItem.currency_code) }}
          </del>
        </div>
        <span v-else-if="totalPrice" data-test-id="item-total-price">
          {{ $formatPrice(totalPrice, cartItem.currency_code) }}
        </span>
        <b-button variant="outline-custom" data-test-id="remove-item" class="remove-item-button mt-2" @click="isShowConfirmDeleteCartItem = true">
          <i class="icon-sen-delete" />
        </b-button>
      </div>
    </div>
    <b-modal
      v-model="isShowConfirmDeleteCartItem"
      centered
      size="lg"
      body-class="d-flex flex-wrap justify-content-center"
      hide-header
      hide-footer
    >
      <div class="col-12 py-2 text-center">
        <h4>{{ $t('Are you sure you want to remove this item?') }}</h4>
      </div>
      <div class="col-4">
        <b-button
          block
          variant="secondary"
          class="border-radius-none"
          @click="isShowConfirmDeleteCartItem=false"
        >
          {{ $t('No') }}
        </b-button>
      </div>
      <div class="m-0 col-4">
        <b-button
          block
          variant="danger"
          class="border-radius-none"
          data-test-id="button-confirm-remove-item"
          @click="removeCartItem(); isShowConfirmDeleteCartItem = false"
        >
          {{ $t('Yes') }}
        </b-button>
      </div>
    </b-modal>
  </div>
</template>

<script>
import productCartItemMixin from '~/mixins/productCartItem'

export default {
  name: 'ProductCartItem',
  mixins: [productCartItemMixin]
}
</script>

<style lang="scss">
.product-cart-item {
  .title::first-letter {
    text-transform: capitalize;
  }

  .media-aside {
    position: relative;

    img,
    .spinner {
      cursor: pointer;
      width: 80px;
      height: 100px;
      object-fit: contain;
    }

    .hover-image {
      background-color: white;
      display: none;
      position: absolute;
      z-index: 1000;
      left: 0;
      top: 0;
      img {
        width: 300px;
        height: 375px;
      }
      @media (min-width: 768px) {
        left: 100%;

        img {
          width: 500px;
          height: 625px;
        }
      }
    }

    &:hover{
      .hover-image {
        display: block;
      }
    }
  }

  .media-body {
    /*overflow-x: clip;*/
    max-width: calc(100% - 100px);
  }

  .option-box {
    span {
      line-height: 33px;
    }
  }

  .quantity-box {
    margin-top: -5px;
  }

  .remove-item-button {
    margin-top: -8px;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    i.icon-color {
      margin-right: 1rem;
      height: 20px;
      width: 20px;
      border-radius: 50%;
      border: solid 1px rgb(182, 181, 181)
    }
  }
}
</style>
