<template>
  <div class="product-cart-item row py-3 border-bottom">
    <div class="col-12">
      <b-media no-body>
        <b-media-aside>
          <product-img :path="cartItem.thumb_url" :color="cartItem.options.color?$colorVal(cartItem.options.color):''" />
        </b-media-aside>
        <b-media-body class="d-flex row m-1">
          <div class="col-12 pl-1 d-grid">
            <h6 class="text-overflow-hidden">
              {{ cartItem.product_name }}
            </h6>
          </div>
          <div class="col-12 px-1 text-capitalize">
            <template
              v-for="(value, key, optionIndex) in cartItem.options"
            >
              <span
                v-if="!value.startsWith('__')"
                :key="optionIndex"
                class="text-uppercase"
              >
                {{ value }}
                <span v-if="optionIndex !== (Object.keys(cartItem.options).length - 1)">&nbsp;/&nbsp;</span>
              </span>
            </template>
          </div>
          <div v-if="cartItem.custom_options" class="col-12 px-1">
            <div v-for="(value, key) in cartItem.custom_options" :key="key" class="text-capitalize">
              <span>{{ key.replace(/_/g, ' ') }}</span>: <span>{{ value }}</span>
            </div>
          </div>
          <div v-if="cartItem.customer_custom_options" class="col-12">
            <ul v-for="(v, i) in cartItem.customer_custom_options" :key="i" class="pl-2">
              <template v-for="(vC, iC) in v">
                <li v-if="vC" :key="iC" style="white-space: break-spaces; font-size: 13px">
                  <div :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">
                    {{ $t(vC.label) }}:
                    <a v-if="vC.type === 'image' && vC.imagePath" :href="$imgUrl(vC.imagePath, 'full')" target="_blank" @click.prevent="$store.commit('changeViewImagePath', vC.imagePath)">View image</a>
                    <span v-else :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">{{ vC.value }}</span>
                  </div>
                </li>
              </template>
            </ul>
          </div>
          <div class="quantity-box d-flex col-12 col-sm-6 mt-2 px-1">
            <b-button
              variant="outline-custom"
              size="sm"
              class="border bg-transparent"
              @click="updateQuantity( cartItem.quantity - 1 )"
            >
              <i class="icon-sen-minus" />
            </b-button>
            <b-form-input
              size="sm"
              type="number"
              :value="currentQuantity"
              class="mx-2 text-center"
              @input="value=>updateQuantity(value)"
            />
            <b-button
              variant="outline-custom"
              size="sm"
              class="border bg-transparent"
              @click="updateQuantity( cartItem.quantity + 1 )"
            >
              <i class="icon-sen-plus" />
            </b-button>
          </div>
          <div class="d-flex col-12 col-sm-6 align-items-center mt-2">
            <span class="ml-3 font-weight-500">
              <span>
                {{ $formatPrice(totalPrice, cartItem.currency_code) }}
              </span>
            </span>
          </div>
        </b-media-body>
      </b-media>
    </div>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['index', 'cartItem'],
  data () {
    return {
      currentQuantity: this.cartItem.quantity
    }
  },
  computed: {
    totalPrice
  },
  methods: {
    updateQuantity
  }
}

function totalPrice () {
  // const dynamicBaseCostIndex = 0
  // if (this.cartItem && this.cartItem.dynamic_base_index !== undefined) {
  //   dynamicBaseCostIndex = parseFloat(this.$formatPriceNoUnit(this.cartItem.dynamic_base_index, this.cartItem.currency_code, USD_CODE))
  // }
  const totalPrice = this.$formatPriceNoUnit(this.cartItem.price * this.cartItem.quantity, null, this.cartItem.currency_code)
  return totalPrice
}

function updateQuantity (quantity) {
  quantity = Number(quantity)
  if (!quantity || quantity < 1) {
    quantity = 1
  }
  this.currentQuantity = quantity
  this.$emit('updateQuantity', { index: this.index, quantity })
}

</script>

<style lang="scss">
.product-cart-item {

  .media-aside {

    img,
    .spinner {
      width: 80px;
      height: 100px;
      object-fit: cover;
    }
  }

  .media-body {
    /*overflow-x: clip;*/
    max-width: calc(100% - 100px);
  }

  .option-box {
    span {
      line-height: 33px;
    }
  }

  .quantity-box{
    margin-top: -5px;
  }

  .remove-item-button {
    margin-top: -8px;
  }
}
</style>
