<template>
  <div>
    <b-modal
      id="modalCampaign"
      v-model="isShowModal"
      centered
      hide-footer
      size="lg"
      title-class="h5 text-overflow-hidden"
      header-class="py-2 border-none"
      :title="title"
      data-test-id="modal-campaign"
      @shown="isShown = true; $tracking.newCustomTracking('modal-shown', null, 'modalCampaign', title)"
      @hidden="isShown = false; $tracking.newCustomTracking('modal-hiden', null, 'modalCampaign', title)"
    >
      <template #modal-header-close>
        <span class="close-icon"><i class="icon-sen-close-circle" /></span>
      </template>
      <div class="row overflow-auto h-100 pb-5 mb-lg-0">
        <div class="campaign-images-box-modal col-12 col-lg-6">
          <div class="d-lg-none">
            <general-info
              :is-show-author="true"
              :campaign="campaignData"
              :current-product="currentProduct"
              :option-list="optionList"
              :current-options="currentOptions"
            />
            <rating
              :review-display="reviewDisplay"
              :average-rating="reviewSummary.summary.average_rating"
              :review-count="reviewSummary.summary.review_count"
            />
            <price
              :current-product="currentProduct"
              :current-price="currentPrice"
              :current-old-price="currentOldPrice"
            />
          </div>
          <images-box
            v-if="isShown"
            ref="imageBox"
            :is-modal="true"

            :filter-images="filterImages"
            :current-product="currentProduct"
            :campaign="campaignData"
            :option-list="optionList"

            :current-options="currentOptions"
            :current-design="currentDesign"
            :filter-designs="filterDesigns"
            :is-show-design="isShowDesign"

            :custom-image="customImage"

            @updateShowDesign="updateShowDesign"
          />
          <embroider-warning v-if="currentProduct && currentProduct.full_printed === 4" />
        </div>
        <div class="col-12 col-lg-6 pb-5 info-box">
          <div class="d-none d-lg-block">
            <general-info
              :is-show-author="true"
              :campaign="campaignData"
              :current-product="currentProduct"
              :option-list="optionList"
              :current-options="currentOptions"
            />
            <rating
              :review-display="reviewDisplay"
              :average-rating="reviewSummary.summary.average_rating"
              :review-count="reviewSummary.summary.review_count"
            />
            <price
              :current-product="currentProduct"
              :current-price="currentPrice"
              :current-old-price="currentOldPrice"
            />
          </div>
          <product-list
            v-if="productList && productList.length"
            :products="productList"
            :current-product="currentProduct"
            :is-dropdown-type="$store.state.storeInfo.product_select_type === 'dropdown'
              || campaignData.system_type === 'mockup'
              || $store.state.storeInfo.store_type === 'express_listing'"
            @updateOption="updateOption"
          />

          <option-list
            v-if="optionList"
            :option-list="optionList"
            :current-options="currentOptions"
            :current-product="currentProduct"
            @updateOption="updateOption"
            @openSizeGuild="openSizeGuild(currentProduct)"
          />

          <personalize-custom
            v-if="campaignData && campaignData.personalized === 1"
            class="d-none d-lg-block"
            :is-modal="true"
            :option-error="optionError"

            :custom-text-list="customTextList"
            :custom-image-list="customImageList"
            :file-upload="fileUpload"
            :is-upload-file="isUploadFile"

            :custom-design-array="customDesignArray"
            :custom-design-type="customDesignType"
            :custom-design="customDesign"
            :loading-change-custom-design="loadingChangeCustomDesign"

            @click.native="$tracking.customTracking({event: 'interact', action: 'personalize_click'})"
            @changeCurrentDesign="changeCurrentDesign"
            @updateCustomDesign="updateCustomDesign"
            @updateCanvasData="updateCanvasData"
            @updateDesignQuery="updateDesignQuery"
            @editDesign="$refs.modalEditCustomImage.isShowModal = true"
          />

          <personalize-pb
            v-if="campaignData && campaignData.personalized === 2"
            :campaign="campaignData"
            :current-design="currentDesign"
            :is-modal="true"
            @click.native="$tracking.customTracking({event: 'interact', action: 'personalize_click'})"
            @scrollToImage="scrollToImage"
            @changeCurrentDesign="changeCurrentDesign"
          />
          <personalize-custom-option
            v-if="campaignData && campaignData.personalized === 0 && currentProduct && currentProduct.template_custom_options"
            :key="currentProduct.id"
            :campaign="campaignData"
            :current-product="currentProduct"
            :custom-options="currentProduct.template_custom_options"
            :common-options="currentProduct.common_options"
            :is-loaded-custom-option="isLoadedCustomOption"
            :is-modal="true"
            @updateCustomizeOptions="value => customerCustomOptions = value"
            @isLoadingAddToCart="value => isLoadingAddToCart = value"
            @updateCustomOptionGroupNumbers="value => customOptionGroupNumbers = value"
            @updateCustomOptionGroupFee="updateCustomOptionGroupFee"
            @updateAllowCustomCampaign="value => allowCustomCampaignAddToCart = value"
          />
          <personalize-custom-option
            v-if="campaignData && campaignData.personalized === 3 && campaignData.options"
            :campaign="campaignData"
            :is-modal="true"
            :custom-options="campaignData.options"
            :common-options="campaignData.common_options"
            :is-loaded-custom-option="true"
            @click.native="$tracking.customTracking({event: 'interact', action: 'personalize_click'})"
            @updateCustomizeOptions="value => customerCustomOptions = value"
            @isLoadingAddToCart="value => isLoadingAddToCart = value"
            @updateCustomOptionGroupFee="updateCustomOptionGroupFee"
            @updateCustomOptionGroupNumbers="value => customOptionGroupNumbers = value"
          />

          <quantity-box
            :quantity="quantity"
            :is-modal="isModal"
            :current-variant="currentVariant"
            :is-loading-add-to-cart="isLoadingAddToCart"
            @updateQuantity="updateQuantity"
            @addToCart="addToCart"
          />
        </div>
        <div v-if="campaignData" ref="bottomButton" class="bottom-button position-absolute bottom-0 left-0 w-100 bg-white p-1 d-flex flex-wrap">
          <div class="col-12 col-lg-6 d-none d-lg-block px-0">
            <b-button
              :disabled="!!(currentVariant && currentVariant.out_of_stock) || !allowCustomCampaignAddToCart"
              block
              size="lg"
              variant="outline-custom"
              class="border text-uppercase font-weight-600"
              @click="addToCart(2)"
            >
              {{ $t('Buy it now') }}
            </b-button>
          </div>
          <div class="d-lg-none col-12 px-0 mx-0">
            <personalize-custom-bottom-input
              v-if="campaignData.personalized === 1 && selectedCustom"
              :is-modal="true"
              :option-error="optionError"
              :selected-custom="selectedCustom"
              :custom-design-type="customDesignType"
              :custom-item-list="customItemList"
              :custom-design-array="customDesignArray"
              :custom-design="customDesign"
              :loading-change-custom-design="loadingChangeCustomDesign"
              :is-upload-file="isUploadFile"
              :file-upload="fileUpload"

              @updateSelectedCustom="value=> selectedCustom = value"
              @changeCurrentDesign="changeCurrentDesign"
              @updateDesignQuery="updateDesignQuery"
              @updateCustomDesign="updateCustomDesign"
              @updateCanvasData="updateCanvasData"
              @editDesign="$refs.modalEditCustomImage.isShowModal = true"
            />
          </div>
          <div class="col-12 col-lg-6 px-0">
            <b-button
              block
              size="lg"
              variant="custom"
              class="font-weight-500 text-uppercase"
              :disabled="isLoadingAddToCart || loadingChangeCustomDesign || !!(currentVariant && currentVariant.out_of_stock) || !allowCustomCampaignAddToCart"
              data-test-id="moda-campaign-button-add-to-cart"
              @click="addToCart(1)"
            >
              <loading-dot v-if="isLoadingAddToCart || loadingChangeCustomDesign" variant="light" />
              <span v-else-if="(currentVariant && currentVariant.out_of_stock)">{{ $t('Out of stock') }}</span>
              <span v-else><i class="font-2xl icon-sen-cart-plus mr-1" />{{ $t('Add to cart') }}</span>
            </b-button>
          </div>
        </div>
      </div>
    </b-modal>
    <modal-add-to-cart
      ref="modalAddToCart"
      :filter-images="filterImages"
      :current-product="currentProduct"
      :quantity="quantity"
      :cart-total-quantity="cartTotalQuantity"
      :current-options="currentOptions"
      :related-cart="relatedCart"
      :option-list="optionList"
      :title="title"
      :upload-file-design-url="uploadFileDesignUrl"
      :is-loading-upload-image="isLoadingUploadImage"
      :campaign="campaignData"
    />

    <modal-select-size
      ref="modalSelectSize"
      :size-guide-list="sizeGuideList"
      :option-list="optionList"
      @openSizeGuild="openSizeGuild(currentProduct)"
      @selectSize="value=>updateOption({size: value}, true)"
    />

    <modal-confirm-design
      ref="modalConfirmDesign"
      :img-urls="uploadFileDesignUrl"
      :is-loading-add-to-cart="isLoadingAddToCart"
      @addToCart="addToCart"
    />

    <modal-edit-custom-image
      ref="modalEditCustomImage"
      :design="currentDesign"
      :custom-image="customImage"
      :is-image-low-quanlity="isImageLowQuanlity"
      @openConfirmQuantityImage="isConfirmQuantityImage? '' : isShowModalConfirmQuantityImage = true"
    />

    <b-modal
      v-model="isShowModalConfirmQuantityImage"
      centered
      size="lg"
      body-class="d-flex flex-wrap justify-content-center"
      hide-header
      hide-footer
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalConfirmQuantityImage', null)"
      @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalConfirmQuantityImage', null)"
    >
      <div class="col-12 py-2 text-center">
        <h4>{{ $t('Image resolution is low that cause bad print quality.') }}</h4>
        <p>{{ $t('Do you want to upload higher resolution image (minimum size is heightxwidth px)?', {height: Math.round(customImage && customImage.height * customImage.scaleY / 2) , width: Math.round(customImage && customImage.width * customImage.scaleX / 2)}) }}</p>
      </div>
      <div class="col-12 col-md-6">
        <b-button
          block
          variant="primary"
          class="border-radius-none mb-3"
          @click="confirmQuantityImage(true)"
        >
          {{ $t('Upload new image') }}
        </b-button>
      </div>
      <div class="m-0 col-12 col-md-6">
        <b-button
          block
          variant="secondary"
          class="border-radius-none mb-3"
          @click="confirmQuantityImage(false)"
        >
          {{ $t('Keep old image') }}
        </b-button>
      </div>
    </b-modal>
  </div>
</template>

<script>
import ModalCampaignMixin from '~/mixins/modalCampaign'

import GeneralInfo from '~/themes/default/components/campaign/GeneralInfo'
import Rating from '~/themes/default/components/campaign/Rating'
import Price from '~/themes/default/components/campaign/Price'
import ProductList from '~/themes/default/components/campaign/ProductList'
import OptionList from '~/themes/default/components/campaign/OptionList'
import ImagesBox from '~/themes/default/components/campaign/ImagesBox'
import PersonalizeCustom from '~/themes/default/components/campaign/PersonalizeCustom'
import PersonalizePb from '~/themes/default/components/campaign/PersonalizePb'
import PersonalizeCustomOption from '~/themes/default/components/campaign/PersonalizeCustomOption'
import QuantityBox from '~/themes/default/components/campaign/QuantityBox'
import PersonalizeCustomBottomInput from '~/themes/default/components/campaign/PersonalizeCustom/BottomInput'

import ModalAddToCart from '~/themes/default/components/campaign/modal/ModalAddToCart'
import ModalSelectSize from '~/themes/default/components/campaign/modal/ModalSelectSize'
import ModalConfirmDesign from '~/themes/default/components/campaign/modal/ModalConfirmDesign'
import ModalEditCustomImage from '~/themes/default/components/campaign/modal/ModalEditCustomImage'

export default {
  name: 'ModalCampaignDefault',
  components: {
    GeneralInfo,
    Rating,
    Price,
    ProductList,
    OptionList,
    ImagesBox,
    PersonalizeCustom,
    PersonalizePb,
    PersonalizeCustomOption,
    QuantityBox,
    PersonalizeCustomBottomInput,

    ModalAddToCart,
    ModalSelectSize,
    ModalConfirmDesign,
    ModalEditCustomImage
  },
  mixins: [ModalCampaignMixin],
  data () {
    return {
      allowCustomCampaignAddToCart: true
    }
  },
  mounted () {
    this.$nextTick(() => {
      setTimeout(this.calculateVh, 200)
      window.addEventListener('resize', this.calculateVh)
    })

    if (this.campaignData && this.campaignData?.personalized === 0 && this.currentProduct?.full_printed === 5) {
      this.allowCustomCampaignAddToCart = false
    }
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.calculateVh)
  },
  methods: {
    calculateVh () {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }
  }
}
</script>

<style lang="scss">
#modalCampaign {
  @media (max-width: 1023px) {
    .modal-content {
      max-height: calc(100 * var(--vh));
      height: 100vh;
    }

    .modal-dialog{
      margin: 0 auto;
    }
  }

  @media (min-width: 1024px) {
    .info-box{
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .close-icon {
    font-size: 2rem;
  }

  .modal-body {
    overflow: auto;
  }

  .viewcart-button {
      border: solid 1px black !important;
  }

  .addToCart-button {
    position: absolute;
    bottom: 0;
  }

  .size-select-img {
    width: 100%;
    max-width: 300px;
  }

  .product-add-to-cart-item {
    img {
      width: 80px;
      height: 100px;
    }
  }

  .campaign-images-box-modal {

    .thumb-canvas {
      width: 100%;
    }

    #viewBox{
      width: 100%;
    }

    .thumb {
      position: relative;
      width: 100%;
      padding-top: 125%;
      overflow: hidden;
      background-color: transparent;

      &>picture>img,
      .spinner,
      .zoom-on-hover {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        object-fit: contain;
        transition: transform 0.2s;
      }
    }

  }

  .bottom-button {
    z-index: 100;
  }
}

.next-preview-customtext-btn {
  i {
    line-height: 2.9rem;
    font-size: 1.9rem;
  }
}

.form-control-file-bottom-button {
  position: relative;
  width: 100%;
  height: 3rem;

&.require-input label div{
    border-color: red;
  }

  input {
    display: none;
  }

  label {
    height: 3rem;
    font-size: 0.875rem;
    background: white;
    cursor: pointer;
    margin-bottom: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    div {
      padding: 12px;
      border: solid 1px #ced4da;
      color: #8F8F8F;
    }

    .button-box {
      background-color: #E9ECEF;
      border-left: none;
    }

    &.disabled {
      cursor: not-allowed;
      background-color: rgb(241, 241, 241);
    }
  }
}

#modalCampaign .bottom-button .form-group > div{
    display: flex;
}

.classic #modalCampaign .bottom-button div:first-child button {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

@media (min-width: 768px) {
  .classic #modalCampaign .bottom-button div:last-child button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
