<template>
  <div @mouseover="$refs.dropdownLanguage.show()" @mouseleave="$refs.dropdownLanguage.hide()">
    <b-dropdown
      ref="dropdownLanguage"
      data-test-id="language-dropdown"
      class="footer-dropdown m-2 shadow-1"
      variant="light"
      size="sm"
      no-caret
    >
      <template #button-content>
        <div data-test-id="current-lang" class="d-flex align-items-center">
          <span v-if="lang" class="d-inline-flex" style="font-size: 1rem;"><span :class="`vti__flag ${lang.countryCode[0].toLowerCase()}`" /></span>
          <span data-test-id="current-lang-name"> {{ lang.name || $t('Language') }}</span>
        </div>
      </template>
      <b-dropdown-item
        v-for="(locale, index) in langs"
        :key="index"
        :data-test-id="`locale-${locale.code}-item`"
        :active="$i18n.locale === locale.code"
        @click="$i18n.setLocale(locale.code)"
      >
        <div class="d-flex align-items-center">
          <span class="d-inline-flex" style="font-size: 1rem;"><span :class="`vti__flag ${locale.countryCode[0].toLowerCase()}`" /></span>
          <span>{{ locale.name }}</span>
        </div>
      </b-dropdown-item>
    </b-dropdown>
  </div>
</template>

<script>
const taiwanCountryCode = ['TW', 'TWN']

export default {
  props: {
    lang: {
      type: Object,
      default: null
    }
  },
  computed: {
    langs () {
      const userCountry = this.$store.state.userCountry
      const result = []
      const raws = [...this.$i18n.locales]
      if (userCountry) {
        if (taiwanCountryCode.includes(userCountry)) {
          const index = raws.map(lang => lang.code).indexOf('zh')
          raws[index].countryCode = taiwanCountryCode
        }
        this.$i18n.locales.forEach((item) => {
          if (item.countryCode.includes(userCountry)) {
            result.push(item)
            const removeIndex = raws.map(lang => lang.code).indexOf(item.code)
            if (removeIndex) {
              raws.splice(removeIndex, 1)
            }
          }
        })
      }
      return [...result, ...raws]
    }
  },
  methods: {
    changeLanguage
  }
}
function changeLanguage (event) {
  this.$i18n.setLocale(event)
}
</script>
