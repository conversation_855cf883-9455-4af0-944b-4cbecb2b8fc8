<template>
  <div class="row breadcrumbs">
    <b-breadcrumb
      :items="items"
      class="font-weight-bold"
    />
  </div>
</template>

<script>

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['breadCrumbsList'],
  computed: {
    items () {
      return [{
        text: 'Home',
        to: this.localePath('/')
      },
      ...this.breadCrumbsList
      ]
    }
  }
}
</script>

<style lang="scss">
.breadcrumb{
  background-color: transparent;
  .breadcrumb-item {
    font-size: small;
    &.active{
      color: var(--primary-color);
    }
  }
}
</style>
