<template>
  <b-navbar
    id="PageHeader"
    class="d-block p-0"
    toggleable="md"
    :sticky="true"
    :class="{'show-header': isShowHeader}"
  >
    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
      <defs>
        <g id="delivery-truck">
          <path d="m42.98,46.43c-1.27,0-2.43.52-3.26,1.35h0c-.84.83-1.35,1.99-1.35,3.26s.52,2.42,1.35,3.26h0s0,0,0,0c.84.84,1.99,1.35,3.26,1.35s2.43-.52,3.26-1.35h0c.84-.84,1.35-1.99,1.35-3.26s-.52-2.43-1.35-3.26c-.84-.84-1.99-1.35-3.26-1.35h0Zm1.86,2.76c.48.48.77,1.13.77,1.86,0,.73-.29,1.39-.77,1.86h0c-.47.48-1.13.77-1.86.77s-1.39-.29-1.86-.77h0c-.47-.48-.77-1.13-.77-1.86,0-.73.29-1.38.77-1.86h0c.47-.48,1.13-.77,1.86-.77s1.38.29,1.86.77h0Z" />
          <path d="m98.94,46.43c-1.27,0-2.43.52-3.26,1.35h0c-.83.84-1.35,1.99-1.35,3.26s.52,2.43,1.35,3.26h0c.84.84,1.99,1.36,3.26,1.36s2.43-.52,3.26-1.35h0c.84-.84,1.35-1.99,1.35-3.26s-.52-2.43-1.35-3.26h0c-.84-.84-1.99-1.36-3.26-1.36h0Zm1.86,2.76c.47.48.77,1.13.77,1.86s-.29,1.38-.77,1.86c-.48.47-1.13.77-1.86.77s-1.39-.29-1.86-.77c-.47-.48-.77-1.13-.77-1.86s.29-1.38.77-1.86c.47-.47,1.13-.77,1.86-.77s1.38.29,1.86.77h0Z" />
          <path d="m89.41,36.73c0,.8-.65,1.45-1.45,1.45s-1.45-.65-1.45-1.45V8.58c0-1.19-.38-2.28-1.05-3.14-.66-.86-1.62-1.51-2.76-1.82l-2.87-.77c-.77-.21-1.23-1-1.03-1.78.21-.77,1-1.23,1.78-1.03l2.87.77c1.78.48,3.27,1.5,4.31,2.86,1.05,1.36,1.65,3.06,1.65,4.9v28.14h0Z" />
          <path d="m27.74,15.79H1.45c-.8,0-1.45.65-1.45,1.45h0c0,.8.65,1.45,1.45,1.45h26.29c.8,0,1.45-.65,1.45-1.45h0c0-.8-.65-1.45-1.45-1.45h0Z" style="fill-rule: evenodd;" />
          <path d="m45.44,21.6h-26.29c-.8,0-1.45.65-1.45,1.45h0c0,.8.65,1.45,1.45,1.45h26.29c.8,0,1.45-.65,1.45-1.45h0c0-.8-.65-1.45-1.45-1.45h0Z" style="fill-rule: evenodd;" />
          <path d="m36.45,27.41H10.17c-.8,0-1.45.65-1.45,1.45h0c0,.8.65,1.45,1.45,1.45h26.29c.8,0,1.45-.65,1.45-1.45h0c0-.8-.65-1.45-1.45-1.45h0Z" style="fill-rule: evenodd;" />
          <path d="m42.82,33.22h-26.29c-.8,0-1.45.65-1.45,1.45h0c0,.8.65,1.45,1.45,1.45h26.29c.8,0,1.45-.65,1.45-1.45h0c0-.8-.65-1.45-1.45-1.45h0Z" style="fill-rule: evenodd;" />
          <g>
            <rect x="19.42" y="33.22" width="2.9" height="2.9" />
            <rect x="19.42" y="27.41" width="2.9" height="2.9" />
            <rect x="19.42" y="21.6" width="2.9" height="2.9" />
            <path d="m119.95,39.17l-.02-.08c-.1-.35-.24-.7-.45-1.05l-9.64-16.44-.43-.74-1.32-2.26c-.4-.68-.93-1.21-1.57-1.57-.64-.37-1.37-.56-2.15-.56h-20.57v-8.04c0-.8-.65-1.45-1.45-1.45H20.87c-.8,0-1.45.65-1.45,1.45v10.28h2.9v-8.83h58.57v19.11c0,.06,0,.11,0,.17v15.6s0,.04,0,.04v.04s0,0,0,0v.04s0,.04,0,.04v.04s0,.04,0,.04h0s0,.03,0,.03h0s0,.04,0,.04h0s0,.03,0,.03h0s0,.03,0,.03v.03s-.01.03-.01.03h0s0,.03,0,.03h0s0,.03,0,.03h0s0,.03,0,.03h0s0,.03,0,.03h0s0,.03,0,.03h0s0,.03,0,.03h0s0,.03,0,.03h0s-.02.09-.04.13h0s-.01.03-.01.03h0s-.03.09-.05.13h0s-.01.03-.01.03h0s-.01.03-.01.03l-.03.06h0c-.03.06-.06.12-.09.18h0c-.13.25-.3.48-.5.68-.2.2-.43.37-.67.5h0c-.06.03-.12.06-.18.09h0l-.06.03h-.03s0,.01,0,.01h-.03s0,.01,0,.01c-.04.02-.09.03-.13.05h-.03s0,.01,0,.01c-.04.01-.09.03-.13.04h-.03s0,0,0,0h-.03s0,0,0,0h-.03s-.03.01-.03.01h-.03s0,0,0,0h-.03s0,0,0,0h-.03s0,0,0,0h-.03s0,0,0,0h-.03s-.03,0-.03,0h-.03s-.04,0-.04,0h-.04s0,0,0,0h-.04s-.04,0-.04,0h-.04s-.04,0-.04,0h-.04s-.04,0-.04,0h-.04s-22.11,0-22.11,0c-.82,0-1.49.67-1.49,1.49s.67,1.49,1.49,1.49h22.18s0,0,0,0h.07s.07,0,.07,0h.07s.07,0,.07,0h.07s.07-.01.07-.01h.07s0,0,0,0h.07s.07-.02.07-.02h0s.07,0,.07,0h.07s.07-.02.07-.02h.07s.07-.03.07-.03h.07s0-.01,0-.01l.07-.02h0l.07-.02h0l.07-.02.07-.02h0l.07-.02h0l.07-.02h0l.07-.02.14-.04.07-.02h0s.07-.02.07-.02l.07-.02h0l.07-.02h0l.13-.05h0c.07-.03.13-.05.19-.08h0l.13-.06h0l.12-.06h0c.06-.03.12-.06.18-.1h0c.06-.03.12-.07.18-.11h0l.06-.04h0c.2-.12.39-.26.57-.4.16-.13.32-.27.47-.42h0c.15-.15.29-.31.42-.47h0c.15-.18.28-.37.4-.57h0s.04-.06.04-.06c.04-.06.07-.12.11-.18h0c.03-.06.07-.12.1-.19h0s.06-.12.06-.12h0s.06-.13.06-.13h0c.03-.06.06-.13.08-.19h0s.05-.13.05-.13h0s.02-.07.02-.07h0s.02-.07.02-.07l.02-.07h0l.02-.07h0s.04-.14.04-.14h0s.02-.07.02-.07h0s.02-.07.02-.07h0s.02-.07.02-.07h0s.02-.07.02-.07l.02-.07h0s.02-.07.02-.07v-.07s.01,0,.01,0v-.07h.01v-.07s.03-.07.03-.07v-.07s.02-.07.02-.07v-.07h0v-.07s.02-.07.02-.07h0s0-.07,0-.07v-.07s.01-.07.01-.07v-.07s0-.07,0-.07v-.07s0-.07,0-.07h0s0-.07,0-.07v-25.27h20.48c.25,0,.48.06.67.17.19.11.36.28.48.49l.44.76h-8.92c-1.07,0-2.04.44-2.75,1.14h0s0,0,0,0c-.71.71-1.14,1.68-1.14,2.75v9.53c0,1.07.44,2.04,1.14,2.75h0s0,0,0,0c.71.71,1.68,1.14,2.75,1.14h16.56c.62,0,1.2-.16,1.71-.46.13-.08.26-.16.38-.26l1.22,2.07c.06.1.1.21.14.32v.04c.03.09.04.2.04.31v12.04c0,.21-.08.4-.22.54h0c-.13.14-.33.23-.54.23h-7.31c.12-.64.19-1.3.19-1.97,0-2.83-1.15-5.4-3.01-7.26-1.86-1.86-4.42-3.01-7.26-3.01s-5.4,1.15-7.26,3.01c-1.86,1.86-3.01,4.42-3.01,7.26,0,.67.07,1.33.19,1.97h-35.81c.12-.64.19-1.3.19-1.97,0-2.83-1.15-5.4-3.01-7.26-1.86-1.86-4.42-3.01-7.26-3.01s-5.4,1.15-7.26,3.01c-1.86,1.86-3.01,4.42-3.01,7.26,0,.67.07,1.33.19,1.97h-9.54c-.21,0-.4-.09-.54-.23-.14-.14-.22-.33-.22-.54v-.99c0-.21.08-.4.22-.54h0s0,0,0,0c.14-.14.33-.23.54-.23h6.24c.82,0,1.49-.67,1.49-1.49s-.67-1.49-1.49-1.49h-7.28v-8.48h-2.9v13.22c0,.34.12.65.31.9.17.67.52,1.28.99,1.75.68.68,1.62,1.1,2.65,1.1h10.63c.47.85,1.05,1.62,1.73,2.3,1.86,1.86,4.42,3.01,7.26,3.01s5.4-1.15,7.26-3.01c.68-.68,1.26-1.46,1.73-2.3h37.98c.47.85,1.05,1.62,1.73,2.3,1.86,1.86,4.42,3.01,7.26,3.01s5.4-1.15,7.26-3.01c.68-.68,1.26-1.46,1.73-2.3h8.4c1.03,0,1.96-.42,2.64-1.1h0s0,0,0,0h0c.68-.68,1.1-1.62,1.1-2.65v-12.04c0-.36-.04-.71-.13-1.05Zm-5.97-4.19c-.04.08-.1.13-.15.17-.06.03-.13.05-.22.05h-16.56c-.25,0-.48-.1-.64-.26h0c-.16-.17-.26-.4-.26-.65v-9.53c0-.25.1-.48.26-.64h0c.16-.17.39-.27.64-.27h10.67l6.27,10.7c.04.08.07.15.07.22,0,.06-.02.14-.07.22Zm-65.85,21.22c-1.32,1.32-3.14,2.13-5.15,2.13s-3.83-.82-5.15-2.13c-1.32-1.32-2.13-3.14-2.13-5.15s.81-3.83,2.13-5.15c1.32-1.32,3.14-2.13,5.15-2.13s3.83.81,5.15,2.13c1.32,1.32,2.13,3.14,2.13,5.15s-.81,3.83-2.13,5.15Zm55.96,0c-1.32,1.32-3.14,2.13-5.15,2.13s-3.83-.82-5.15-2.13c-1.32-1.32-2.13-3.14-2.13-5.15s.81-3.83,2.13-5.15c1.32-1.32,3.14-2.13,5.15-2.13s3.83.81,5.15,2.13c1.32,1.32,2.13,3.14,2.13,5.15s-.81,3.83-2.13,5.15Z" />
          </g>
          <path d="m32.01,39.03H15.22c-.8,0-1.45.65-1.45,1.45h0c0,.8.65,1.45,1.45,1.45h16.79c.8,0,1.45-.65,1.45-1.45h0c0-.8-.65-1.45-1.45-1.45h0Z" style="fill-rule: evenodd;" />
        </g>
      </defs>
    </svg>
    <div class="position-relative container-xl px-3 py-1 z-index-100 bg-white">
      <div class="d-flex d-md-none align-items-center">
        <div
          class="p-0 border-none"
          @click="isShowHeader = !isShowHeader"
        >
          <b-nav-item is="div">
            <i class="icon-sen-menu" />
          </b-nav-item>
        </div>
        <b-navbar-nav style="flex-direction: row;" class="d-md-none ml-3 d-flex align-items-center">
          <b-nav-item :to="localePath('/page/faq')">
            <i class="icon-sen-help-circle-outline" />
          </b-nav-item>
        </b-navbar-nav>
      </div>
      <b-navbar-brand class="m-0 order-md-1 mr-md-3">
        <nuxt-link :to="localePath('/')" class="site-logo">
          <img
            v-if="storeInfo && storeInfo.logo_url"
            :src="$imgUrl(storeInfo.logo_url,'logo')"
            :onerror="`this.onerror=null;this.src='${$config.publicPath}/images/logo-placeholder.png';`"
            :alt="storeInfo.name"
            loading="lazy"
          >
          <span v-else>
            {{ storeInfo.name }}
          </span>
        </nuxt-link>
      </b-navbar-brand>
      <client-only>
        <div
          v-if="userCountry"
          class="delivery ml-4 order-md-2 d-none d-lg-flex align-self-end align-items-center justify-content-end user-select-none"
          :class="{
            'w-100': storeInfo.enable_search === 0,
            'd-md-flex': $store.state.storeInfo && !$store.state.storeInfo?.enable_deliver_to
          }"
          @click="$refs.headerLanguage.$refs.el.show()"
        >
          <template v-if="$store.state.storeInfo && $store.state.storeInfo?.enable_deliver_to">
            <svg viewBox="0 0 120.08 61.31" style="width: 50px;">
              <use xlink:href="#delivery-truck" />
            </svg>
            <span class="text-nowrap ml-2" style="font-size: 0.85rem; line-height: 1.2;">{{ $t('Deliver to') }}<br><strong>{{ userCountry }}</strong></span>
          </template>
          <header-lang-select ref="headerLanguage" />
        </div>
      </client-only>
      <b-navbar-nav class="col-auto col-md order-md-2 nav-item px-md-4 d-none ml-auto" :class="storeInfo.enable_search === 0 ? '' : 'd-md-flex'">
        <b-form
          class="searchInput position-absolute d-md-flex mr-1 border-radius-none"
          @submit.prevent.stop="searchAction"
        >
          <b-form-input
            id="search-keyword"
            ref="searchInput"
            v-model="searchKeyword"
            name="s"
            type="search"
            :placeholder="`${$t('What are you looking for?')} (Ctrl + /)`"
            class="p-2 border-none"
            @mouseover="$refs.searchInput.focus()"
          />
          <b-button
            size="sm"
            variant="outline-custom"
            type="submit"
            class="font-weight-bold border-none d-flex justify-content-center align-items-center bg-transparent"
          >
            <div v-if="!isSearching" class="icon-sen-search" />
            <div v-else class="icon-sen-loading" />
          </b-button>
        </b-form>
      </b-navbar-nav>
      <b-navbar-nav class="right-menu order-md-3">
        <b-nav-item v-if="$store.state.storeInfo.id === 1" class="d-none d-md-flex mr-1" target="_blank" :href="cta_link">
          <span>
            {{ $t('Seller login') }}
          </span>
        </b-nav-item>
        <b-nav-item class="faq-icon d-none d-md-flex" :class="{'ml-3': isRtl}" :to="localePath('/page/faq')">
          <i class="icon-sen-help-circle-outline" />
        </b-nav-item>
        <!-- <b-nav-item
          class="mobile-search-icon d-md-none mr-2"
          @click="trigerScrollDown"
        >
          <i class="icon-sen-search" />
        </b-nav-item> -->
        <div v-if="$store.state.storeInfo.enable_search" class="d-flex align-items-center mr-2 d-md-none" style="cursor: pointer;" @click="onSearchButtonClick">
          <i class="icon-sen-search" />
        </div>
        <b-nav-item link-classes="position-relative" :to="localePath('/cart')">
          <i class="icon-sen-cart" />
          <span class="small-badge bg-danger">{{ cartTotalQuantity }}</span>
        </b-nav-item>
      </b-navbar-nav>
    </div>
    <b-collapse
      id="nav-collapse"
      is-nav
      :visible="isShowHeader"
      class="container-xl px-3 py-md-2"
    >
      <b-navbar-nav>
        <li
          v-for="(menu, index) in storeInfo.headerMenu"
          :key="index"
          class="nav-item"
          :class="{
            'has-sub-menu': menu.submenu && menu.submenu.length,
            'mr-3': isRtl && index === storeInfo.headerMenu.length - 1
          }"
          @click="isShowHeader = false"
        >
          <nuxt-link
            v-if="menu.url.includes('/' + currentDomain) || menu.url.startsWith('/')"
            :to="localePath(menu.url)"
            class="nav-link nuxt-link-active"
          >
            <div>{{ menu.title }}</div>
          </nuxt-link>
          <span
            v-else-if="menu.url === '#'"
            class="nav-link nuxt-link-active"
          >
            <div>{{ menu.title }}</div>
          </span>
          <a
            v-else
            :href="menu.url"
            class="nav-link nuxt-link-active"
            target="_blank"
          >
            <div>{{ menu.title }}</div>
          </a>
          <div
            v-if="menu.submenu && menu.submenu.length"
            class="toggle-icon"
            @click.prevent.stop="tabShowLv1===index ? tabShowLv1 = false : tabShowLv1 = index; tabShowLv2 = false"
          >
            <i class="d-md-none" :class="tabShowLv1===index ? 'icon-sen-chevron-down': 'icon-sen-chevron-right'" />
            <i class="d-none d-md-block icon-sen-chevron-down" />
          </div>
          <ul class="submenu menu-lv1 d-md-block z-index-1" :class="{'d-none': tabShowLv1!==index}">
            <li
              v-for="(menu2, menu2_index) in menu.submenu"
              :key="menu2_index"
              class="nav-item"
              :class="{'has-sub-menu': menu2.submenu && menu2.submenu.length}"
              @click="isShowHeader = false"
            >
              <nuxt-link
                v-if="menu2.url.includes('/' + currentDomain) || menu2.url.startsWith('/')"
                exact
                :to="localePath(menu2.url)"
                class="nav-link nuxt-link-active text-overflow-hidden"
                target="_self"
              >
                <div>
                  {{ menu2.title }}
                </div>
              </nuxt-link>
              <span
                v-else-if="menu2.url === '#'"
                class="nav-link nuxt-link-active"
              >
                <div>
                  {{ menu2.title }}
                </div>
              </span>
              <a
                v-else
                :href="menu2.url"
                class="nav-link nuxt-link-active text-overflow-hidden"
                target="_blank"
              >
                <div>
                  {{ menu2.title }}
                </div>
              </a>
              <div
                v-if="menu2.submenu && menu2.submenu.length"
                class="toggle-icon"
                @click.prevent.stop="tabShowLv2 === menu2_index ? tabShowLv2 = false : tabShowLv2 = menu2_index"
              >
                <i :class="(tabShowLv2===menu2_index) ? 'icon-sen-chevron-down': 'icon-sen-chevron-right'" />
              </div>
              <ul class="submenu menu-lv2 d-md-block z-index-1" :class="{'d-none': tabShowLv2!==menu2_index}">
                <li
                  v-for="(menu3, menu3_index) in menu2.submenu"
                  :key="menu3_index"
                  class="nav-item"
                  @click="isShowHeader = false"
                >
                  <nuxt-link
                    v-if="menu3.url.includes('/' + currentDomain) || menu3.url.startsWith('/')"
                    exact
                    :to="localePath(menu3.url)"
                    class="nav-link nuxt-link-active text-overflow-hidden"
                    target="_self"
                  >
                    {{ menu3.title }}
                  </nuxt-link>
                  <span
                    v-else-if="menu3.url === '#'"
                    class="nav-link nuxt-link-active"
                  >
                    {{ menu3.title }}
                  </span>
                  <a
                    v-else
                    :href="menu3.url"
                    class="nav-link nuxt-link-active text-overflow-hidden"
                    target="_blank"
                  >
                    {{ menu3.title }}
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </li>
      </b-navbar-nav>
      <b-navbar-nav v-if="$store.state.storeInfo.id === 1" class="d-md-none mr-1">
        <b-nav-item target="_blank" :href="cta_link">
          <span>
            {{ $t('Seller login') }}
          </span>
        </b-nav-item>
      </b-navbar-nav>
    </b-collapse>
    <b-form
      class="searchInputMobile d-md-none border-top border-bottom z-index-1"
      :class="storeInfo.enable_search === 0 ? 'd-none' : ''"
      @submit.prevent.stop="searchAction"
    >
      <b-form-input
        id="search-mobile"
        ref="searchInput"
        v-model="searchKeyword"
        name="s"
        type="search"
        :placeholder="`${$t('What are you looking for?')}`"
        class="p-2 border-none"
        :enterkeyhint="`${$t('Search')}`"
        @mouseover="$refs.searchInput.focus()"
      />
      <div
        class="pl-2 pr-2 font-weight-bold border-none d-flex justify-content-center align-items-center bg-transparent"
        @click="searchAction"
      >
        <div v-if="!isSearching" class="icon-sen-search" />
        <div v-else class="icon-sen-loading" />
      </div>
    </b-form>
    <client-only>
      <div
        class="deliverToMobile d-none align-items-center justify-content-between d-lg-none z-index-1 py-2"
        :class="{
          'd-md-flex': $store.state.storeInfo && $store.state.storeInfo?.enable_deliver_to
        }"
        style="background: #2f3237; color: #FFF; fill: #FFF;"
      >
        <div v-if="userCountry && $store.state.storeInfo && $store.state.storeInfo?.enable_deliver_to" class="mx-4">
          <svg viewBox="0 0 120.08 61.31" style="width: 40px;">
            <use xlink:href="#delivery-truck" />
          </svg>
          <span class="text-nowrap ml-2" style="font-size: 0.85rem;">{{ $t('Deliver to') }} <strong>{{ userCountry }}</strong></span>
        </div>
        <div>
          <header-lang-select ref="headerLanguage" :is-small="true" />
        </div>
      </div>
    </client-only>
  </b-navbar>
</template>

<script>
import headerMixin from '~/mixins/header'
import rtl from '~/mixins/rtl'
import HeaderLangSelect from '~/themes/default/components/common/HeaderLangSelect.vue'
export default {
  components: {
    HeaderLangSelect
  },
  mixins: [headerMixin, rtl],
  data () {
    return {
      tabShowLv1: false,
      tabShowLv2: false,
      cta_link: 'https://sen.lc/regnewseller',
    }
  },
  methods: {
    hideNavbar () {
      if (this.isShowHeader) {
        this.isShowHeader = !this.isShowHeader
      }
    },
    trigerScrollDown () {
      this.hideNavbar()
      document.body.classList.remove('scroll-down')
      document.body.classList.add('scroll-up')
    },
    onSearchButtonClick () {
      const hasScrolledUp = document.body.classList.toggle('scroll-up')
      this.isShowHeader = false
      if (hasScrolledUp) {
        document.body.classList.remove('scroll-down')
        setTimeout(() => {
          this.$refs.searchInput.focus()
        }, 200)
      }
    }
  }
}

</script>

<style lang="scss">
.delivery {
  cursor: pointer;
}
#PageHeader{
  z-index: 100;
  transition: all .5s ease;

  &.sticky-top{
    background-color: white;
  }

  .site-logo {
    font-family: 'Keep Calm Regular';
    img {
      max-height: 30px;
    }
  }

  .nav-link,
  .navbar-text {
    min-width: max-content;
    text-overflow: ellipsis;
    font-size: 18px;
    color: black;

    &:hover, &.active{
      color: var(--primary-color);
    }
    padding: .25rem 0;
  }

  .right-menu{
    flex-direction: row;

    .nav-link,
    .navbar-text {
      text-transform: uppercase;
      font-size: 13px;
      font-weight: 600;
    }

    .faq-icon i{
      font-size: 20px;
    }
  }

  .navbar-toggler{
    i{
      color: black;
    }
  }

  .searchInput {
    // position: absolute;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    display: flex;
    right: -50px;
    top: 100%;
    background-color: #e8e8e8;

    @media (min-width: 768px) {
      width: 100%;
      right: 0;
      background-color: #e8e8e8;
      box-shadow: none;
      position: relative !important;
    }

    &:focus-within{
      box-shadow: 0 0 2px 1px var(--primary-color);
      backdrop-filter: blur(10px);
    }

    input {
      background-color: #e8e8e8;
      min-width: 220px;
      height: 42px;

      &:focus {
        box-shadow: none;
      }
    }

    i {
      position: absolute;
      right: 10px;
      font-size: 0.875rem !important;
      margin-top: 13px;
    }

    .btn-sm {
      line-height: 1.4;
    }
  }

  .searchButton {
    position: relative;
    width: 40px;
    right: 0;

  }

  .icon-sen-menu, .icon-sen-search, .icon-sen-cart {
    font-size: 1.5rem;
    font-weight: bold;
  }

  @media (min-width: 768px) {
    .submenu {
      max-width: 250px;
      position: absolute;
      visibility: hidden;
      opacity: 0;
      list-style: none;
      padding-left: 0px;
      width: max-content;
      background: white;
      padding: .5rem 0;
      transition: visibility 0s, opacity 0.3s linear;
      min-width: 150px;
      -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

      .nav-item, .nav-link {
        padding: .1rem .5rem;
        justify-content: start;
      }

      &:not(:has(> .has-sub-menu)) {
        max-height: 50vh;
        overflow-y: scroll;
        overflow-x: hidden;
      }
    }

    .menu-lv1 {
      top: 100%;
    }

    .menu-lv2 {
      left: 100%;
      top: -0.5rem;
    }

    .nav-item {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      &.has-sub-menu:hover > .submenu{
        visibility: visible;
        opacity: 1;
      }
    }

    .deliverToMobile {
      transform: translateY(0%);
      transition: all 0.5s ease;
    }
  }

  .submenu {
    list-style: none;
  }

  .nav-item {
    position: relative;
    @media (max-width: 767px) {
      width: 100%;
      .toggle-icon {
        width: 100%;
        position: absolute;
        text-align: right;
        font-size: 1.5rem;
        cursor: pointer;
        z-index: 100;
        top: 0;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }
}

.deliverToMobile {
  transform: translateY(-100%);
  transition: all 0.5s ease;
}

.searchInputMobile {
  position: absolute;
  display: flex;
  background-color: white;
  width: 100%;
  transition: all 0.5s ease;
  transform: translateY(-100%);
  input {
    background-color: transparent;
    &:focus {
      box-shadow: none;
    }
  }

  i {
    position: absolute;
    right: 10px;
    font-size: 0.875rem !important;
    margin-top: 13px;
  }
}

@media (min-width:768px) {
  #nav-collapse{
    .navbar-nav{
      width: 100%;
      display: flex;
      gap: 0 1.25rem;
      flex-wrap: wrap;
    }
  }
}

@media (max-width:767px) {
  #nav-collapse{
    max-height: 70vh;
    overflow-y: auto;
    background-color: rgb(245 245 245);
    width: 100%;
    position: absolute;
    transition: all 0.5s ease;
    transform: translateY(0);

    .navbar-nav{
      width: 100%;
    }
    .nav-item, .nav-link {
      width: 100%;
      display: block;
      justify-content: start;
    }
  }

  .navbar-brand {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    img {
      height: 22px;
      margin-bottom: 7px;
    }
  }

  .scroll-up {
    #PageHeader:not(.show-header) {
      .searchInputMobile {
        transform: translateY(0%)
      }

      .deliverToMobile {
        transform: translateY(70%);
      }

      #nav-collapse {
        transform: translateY(38px);
      }
    }
    .mobile-search-icon {
      display: none;
    }
  }

}
.scroll-down {
  #PageHeader:not(.show-header) {
    &.sticky-top {
      top: -142px;
    }
  }
}
.header-lang-mobile .country-flag {
  height: 1.2rem;
  margin-bottom: 0.5rem;
}
.header-lang-mobile .dropdown-menu {
  position: absolute !important;
}
.mobile-icon-search {
  font-size: 1.3rem !important;
}
@media (min-width:768px) {
  .mobile-icon-search{
    font-size: 1.5rem !important;
  }
}

.icon-sen-loading {
  animation: .75s linear infinite spinner-border;
  line-height: 0;
  font-size: 1.5rem;
}
</style>
