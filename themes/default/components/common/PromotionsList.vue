<template>
  <div v-if="$store.state.storeInfo && !$store.state.storeInfo.disable_promotion" class="promotion-list border-radius-none">
    <promotion-item
      v-for="(promotion, index) in promotionsList"
      :key="index"
      :class="{'d-none': index > (showItem || 2) && !isShowALl}"
      :is-checkout-page="isCheckoutPage"
      :promotion="promotion"
      @showModalPromotion="currentPromotion = promotion ; isShowModal = true"
      @action="value=>{$emit('updateDiscountApply', value)}"
    />
    <div v-if="promotionsList.length > ((showItem + 1) || 3)" class="cursor-pointer text-center font-weight-500 py-3" @click="isShowALl = !isShowALl">
      <span>
        {{ isShowALl ? $t('Show less') : $t('Show more') }}
      </span>
      <span><i :class="isShowALl?'icon-sen-chevron-up':'icon-sen-chevron-down'" /></span>
    </div>
    <b-modal
      v-model="isShowModal"
      centered
      size="lg"
      hide-footer
      :title="`${$t('Discount code')}: ${currentPromotion.discount_code}`"
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalShowPromotionCode', null)"
      @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalShowPromotionCode', null)"
    >
      <ul>
        <li v-if="type">
          <span>{{ $t('Type') }}:</span> <span>
            {{ type }}
          </span>
        </li>
        <li v-if="condition">
          <span>{{ $t('Condition') }}:</span> <span>
            {{ condition }}
          </span>
        </li>
        <li v-if="rules.countries">
          <span>{{ $t('Location') }}:</span> <span>
            {{ countries }}
          </span>
        </li>
        <li v-if="currentPromotion.end_time">
          <span>{{ $t('Expired time') }}:</span> <span>
            {{ currentPromotion.end_time.slice(0, -3) }}
          </span>
        </li>
        <template v-if="rules.tiers && rules.tiers.length">
          <li v-for="(tier, index) in rules.tiers" :key="index">
            <span>{{ $t('Buy') }}</span> <span>
              {{ tier.qty }}
            </span>
            <span>{{ $t('Get') }}</span> <span>
              {{ tier.discount }} %
            </span>
            <span>{{ $t('Off') }}</span>
          </li>
        </template>
      </ul>
      <b-button
        v-if="isCheckoutPage"
        size="sm"
        variant="custom"
        @click.stop="
          $emit('updateDiscountApply', currentPromotion.discount_code);
          isShowModal = false"
      >
        <span>
          {{ $t('Apply Code') }}
        </span>
      </b-button>
      <!-- <b-button v-else size="sm" variant="custom" @click="copyCode">
          <span>
            {{ isCoppy ? $t('Copied') : $t('Copy code') }} <i class="icon-sen-text-box-multiple" />
          </span>
        </b-button> -->
    </b-modal>
  </div>
</template>

<script>
import PromotionItem from './PromotionItem'

export default {
  name: 'PromotionsList',
  components: {
    PromotionItem
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['promotionsList', 'isCheckoutPage', 'showItem'],
  data () {
    return {
      isShowModal: false,
      isShowALl: false,
      isCoppy: false,
      currentPromotion: {}
    }
  },
  computed: {
    rules () {
      if (this.currentPromotion?.rules) {
        return JSON.parse(this.currentPromotion?.rules)
      }
      return {}
    },
    type,
    condition,
    countries
  },
  methods: {
    copyCode
  }
}

function type () {
  if (this.rules.type === 'TD') {
    return this.$t('Tiered Discount')
  }
  if (this.rules.type === 'FS') {
    const discountRules = this.rules
    const fsMaxAmount = parseInt(discountRules?.fs_max_amount)
    const fsPercent = parseInt(discountRules?.fs_percent)
    let stringRule = ''
    if (fsPercent && fsPercent < 100 && fsPercent > 0) {
      stringRule = `${this.$t('Discount')} ${fsPercent}% ${this.$t('for shipping')}`
    } else {
      stringRule = `${this.$t('Free shipping')}`
    }
    if (fsMaxAmount && fsMaxAmount > 0) {
      stringRule += ` ${this.$t('with maximum')} ${this.$formatPrice(fsMaxAmount)}`
    }
    return stringRule
  }

  if (this.rules.type === 'BXGY') {
    const discountString = (this.rules?.get?.discount_percentage)
      ? (this.rules.get.discount_percentage === 100)
        ? this.$t('free')
        : `${this.$t('with')} ${this.rules.get.discount_percentage}% ${this.$t('discount')}`
      : ''

    const bxgyMin = (this.rules?.buy?.minimum_quantity)
      ? `${this.rules.buy.minimum_quantity}`
      : (this.rules?.buy?.minimum_amount) ? `${this.$t('a minimum of')} ${this.$formatPrice(this.rules.buy.minimum_amount)}` : ''

    return `${this.$t('Buy')} ${bxgyMin} ${this.$t('get')} ${this.rules?.get?.quantity || ''} ${discountString}`
  }

  let stringRule = ''

  if (this.rules.type === 'PD' || this.rules.type === 'FD') {
    stringRule = this.$t('Discount')

    if (this.rules.discount_percentage) {
      stringRule += ` ${this.rules.discount_percentage}%`
    }

    if (this.rules.discount_amount) {
      stringRule += ` ${this.$formatPrice(this.rules.discount_amount)}`
    }
  }

  return stringRule
}

function condition () {
  if (this.rules.type === 'TD') {
    return ''
  }
  if (this.rules.type === 'BXGY') {
    return ''
  }

  let stringRule = ''

  if (this.rules.minimum_amount) {
    stringRule += `${this.$t('For order of')} ${this.$formatPrice(this.rules.minimum_amount)}`
  } else if (this.rules.minimum_quantity) {
    stringRule += `${this.$t('For order of')} ${this.rules.minimum_quantity} ${this.$t('items')}`
  } else {
    stringRule += `${this.$t('For any product')}`
  }

  return stringRule
}

function countries () {
  if (!this.rules.countries) {
    return false
  }

  if (this.rules.countries === '*') {
    return this.$t('Worldwide')
  }

  const contryList = this.$store.state?.generalInfo?.countries || []
  const countryCodeList = this.rules.countries.split(',')
  const contryDiscountList = countryCodeList.map((countrycode) => {
    const country = contryList.find(country => country.code === countrycode)
    return country?.name
  })
  return contryDiscountList.filter(item => item).join(', ')
}

function copyCode () {
  this.$copyToClipBoard(this.currentPromotion.discount_code)
  this.isCoppy = true
  setTimeout(() => {
    this.isCoppy = false
  }, 1000)
}
</script>

<style lang="scss">
.promotion-list {
  border: 2px dashed var(--primary-color);
}
</style>
