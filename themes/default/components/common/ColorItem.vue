<template>
  <div
    class="m-1 color-filter-item d-flex"
    :class="{
      active,
      'size-xl': size==='xl'
    }"
  >
    <span class="d-flex align-items-center justify-content-center" :class="textColorClass" sp-action="change_color" :style="`background: ${$colorVal(color) || '#FFF'}`">
      <client-only>
        <IconCheck v-if="active" class="color-icon" />
      </client-only>
    </span>
  </div>
</template>
<script>
export default {
  name: 'ColorFilterItem',
  // eslint-disable-next-line vue/require-prop-types
  props: ['color', 'active', 'size'],
  computed: {
    textColorClass () {
      return this.isLightColor(this.$colorVal(this.color) || '#FFF') ? 'text-black' : 'text-white'
    }
  },
  methods: {
    isLightColor
  }
}
function isLightColor (color) {
  const hex = color.replace('#', '')
  const cR = parseInt(hex.substring(0, 0 + 2), 16)
  const cG = parseInt(hex.substring(2, 2 + 2), 16)
  const cB = parseInt(hex.substring(4, 4 + 2), 16)
  const brightness = ((cR * 299) + (cG * 587) + (cB * 114)) / 1000
  return brightness > 155
}
</script>

<style lang="scss">
.color-icon {
  width: 1rem;
  height: 1rem;
  fill: currentColor;
}
.color-filter-item {
  border-radius: 50%;
  border: 2px solid transparent;

  &.size-xl {
    span {
      height: 30px;
      width: 30px;
    }
  }

  span {
    height: 25px;
    width: 25px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 8px 1px rgb(201, 201, 201)
  }

  &:hover,
  &.active {
    -webkit-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
    box-shadow: 0 0 0 2px var(--primary-color);
  }
}
</style>
