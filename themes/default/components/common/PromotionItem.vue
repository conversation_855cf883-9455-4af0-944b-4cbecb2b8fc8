<template>
  <div class="promotion-item cursor-pointer align-items-center" @click="$emit('showModalPromotion')">
    <div class="p-3 promotion-icon text-white">
      <span :class="promotion.type==='FS' ? 'bg-primary' : 'bg-danger'" class="p-2">
        <i :class="promotion.type==='FS' ? 'icon-sen-bike-fast' : 'icon-sen-tag'" />
      </span>
    </div>
    <div class="col py-3 px-0 promotion-info border-bottom">
      <div class="d-flex justify-content-between">
        <span class="font-weight-600">{{ promotion.discount_code }}</span>
        <small v-if="isCheckoutPage" class="col-auto promotion-copy" @click.stop="$emit('action', promotion.discount_code)">
          <span class="font-weight-600">
            {{ $t('Apply Code') }}
          </span>
        </small>
        <small v-else class="col-auto promotion-copy" @click.stop="copyCode">
          <span class="font-weight-600">
            {{ isCoppy ? $t('Copied') : $t('Copy code') }}
          </span>
          <i class="icon-sen-text-box-multiple" />
        </small>
      </div>
      <div>{{ stringRule }}</div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'PromotionItem',
  // eslint-disable-next-line vue/require-prop-types
  props: ['promotion', 'isCheckoutPage'],
  data () {
    return {
      isCoppy: false
    }
  },
  computed: {
    rules () {
      return JSON.parse(this.promotion.rules)
    },
    stringRule
  },
  methods: {
    copyCode
  }
}

function stringRule () {
  const currentType = this.promotion.type
  if (currentType === 'TD') {
    return this.$t('Tiered Discount')
  }

  if (this.rules.type === 'BXGY') {
    const discountString = (this.rules?.get?.discount_percentage)
      ? (this.rules.get.discount_percentage === 100)
        ? this.$t('free')
        : `${this.$t('with')} ${this.rules.get.discount_percentage}% ${this.$t('discount')}`
      : ''

    const bxgyMin = (this.rules?.buy?.minimum_quantity)
      ? `${this.rules.buy.minimum_quantity}`
      : (this.rules?.buy?.minimum_amount) ? `${this.$t('a minimum of')} ${this.$formatPrice(this.rules.buy.minimum_amount)}` : ''

    return `${this.$t('Buy')} ${bxgyMin} ${this.$t('get')} ${this.rules?.get?.quantity || ''} ${discountString}`
  }

  let stringRule = ''
  let fsMaxAmountStringRule = ''

  if (currentType === 'FS') {
    const discountRules = this.rules
    const fsPercent = parseInt(discountRules?.fs_percent)
    const fsMaxAmount = parseInt(discountRules?.fs_max_amount)
    if (fsPercent && fsPercent < 100 && fsPercent > 0) {
      stringRule = `${this.$t('Discount')} ${fsPercent}% ${this.$t('of shipping')}`
    } else {
      stringRule = this.$t('Free shipping')
    }

    if (fsMaxAmount && fsMaxAmount > 0) {
      fsMaxAmountStringRule = ` ${this.$t('with maximum')} ${this.$formatPrice(fsMaxAmount)}`
    }
  } else if (currentType === 'PD' || currentType === 'FD') {
    stringRule = this.$t('Discount')

    if (this.rules.discount_percentage) {
      stringRule += ` ${this.rules.discount_percentage}%`
    }
    if (this.rules.discount_amount) {
      stringRule += ` ${this.$formatPrice(this.rules.discount_amount)}`
    }
  }

  if (this.rules.minimum_amount) {
    stringRule += ` ${this.$t('for order of')} ${this.$formatPrice(this.rules.minimum_amount)}`
  } else if (this.rules.minimum_quantity) {
    stringRule += ` ${this.$t('for order of')} ${this.rules.minimum_quantity} ${this.$t('items')}`
  } else {
    stringRule += ` ${this.$t('for any product')}`
  }

  if (fsMaxAmountStringRule.length > 0) {
    stringRule += fsMaxAmountStringRule
  }

  return stringRule
}

function copyCode () {
  this.$tracking.newCustomTracking('click', 'copy_code', this.promotion.discount_code)
  this.$copyToClipBoard(this.promotion.discount_code)
  this.isCoppy = true
  setTimeout(() => {
    this.isCoppy = false
  }, 1000)
}
</script>

<style scoped lang="scss">
  .promotion-icon {

    span {
      border-radius: 0.25rem;
    }
  }

  .promotion-item {
    display: flex;
    &:hover {
      background-color: rgb(233, 233, 233);
    }
  }

  .promotion-copy {
    &:hover {
      color: var(--primary-color);
    }
  }
</style>
