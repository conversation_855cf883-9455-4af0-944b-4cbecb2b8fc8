<template>
  <div v-if="changeTime > 0" class="my-3 px-2 gap-3 pt-4 promotion-box d-flex border-2 text-center font-weight-500">
    <div
      v-for="(time, index) in times"
      :key="index"
      class="col w-auto"
      :class="{'d-none': index === 0 && time.time <= 0}"
    >
      <div class="py-2 bg-dark text-white">
        {{ time.time }}
      </div>
      <div class="py-2 text-secondary">
        <small>
          {{ $t(time.text) }}
        </small>
      </div>
    </div>
    <div class="min-w-max-content promotion-text">
      <span class="text-capitalize font-weight-bold">{{ $t('Hurry up!') }}</span>
      <span> - {{ $t('Sale end in') }}</span>
    </div>
  </div>
</template>

<script>

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['endTime', 'showCountdown'],
  data () {
    return {
      changeTime: 0,
      times: [
        { id: 0, text: 'Days', time: 0 },
        { id: 1, text: 'Hours', time: 0 },
        { id: 2, text: 'Minutes', time: 0 },
        { id: 3, text: 'Seconds', time: 0 }
      ]
    }
  },
  created () {
    this.updateTimer()
    setInterval(this.updateTimer, 1000)
  },
  methods: {
    updateTimer () {
      this.getTimeRemaining()
    },
    getTimeRemaining () {
      this.changeTime = Date.parse(new Date(this.endTime2())) - Date.parse(new Date())

      this.times[3].time = Math.floor(this.changeTime / 1000 % 60)
      this.times[2].time = Math.floor(this.changeTime / 1000 / 60 % 60)
      this.times[1].time = Math.floor(this.changeTime / (1000 * 60 * 60) % 24)
      this.times[0].time = Math.floor(this.changeTime / (1000 * 60 * 60 * 24))
    },
    endTime2 () {
      if (this.endTime && this.showCountdown === 1) {
        return this.endTime
      }

      const da = new Date()
      const next15 = (Math.floor((da.getMinutes() + 15 - 1) / 15)) * 15
      const distance = (da.getMinutes() === next15) ? (da.getMinutes() + 15) : next15
      switch (this.showCountdown) {
        case 2:
          // 2 'Repeat every 15 minutes'
          da.setMinutes(distance - 1, 59)
          break
        case 3:
          da.setMinutes(59, 59)
          break
        case 4:
          da.setHours(23, 59, 59)
          break
        case 5:
          // magic number: 0: sunday
          da.setDate(da.getDate() + (0 + (7 - da.getDay())) % 7)
          break
      }
      return da.toISOString()
    }
  }
}

</script>

<style lang="scss">
.promotion-box {
  position: relative;
  border: solid 2px #ced4da;
  .promotion-text {
    width: fit-content;
    background-color: white;
    position: absolute;
    top: 0%;
    transform: translate(-50%, -50%);
    left: 50%;
    padding: 0 20px;
  }
}
</style>
