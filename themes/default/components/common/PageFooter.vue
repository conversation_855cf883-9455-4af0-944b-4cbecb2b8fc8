<template>
  <!-- Site Footer -->
  <footer class="site-footer" :style="spriteUrl">
    <html-head-tag :for-position="'footer_start'" />

    <div class="container-xl d-flex flex-wrap text-center text-lg-left mb-4">
      <div class="row order-lg-2 col-12" :class="isBlockedStore ? 'ml-auto' : 'mx-0'">
        <div class="col-12 col-md-6 pt-5">
          <p class="text-uppercase font-weight-bold mb-0">
            {{ $t(menu1.title) }}
          </p>
          <div
            v-for="(item, index) in menu1.menuData"
            :key="index"
            class="pt-3 w-100 w-md-auto site-link font-weight-500 text-capitalize"
          >
            <nuxt-link exact :to="localePath(item.url)">
              {{ $t(item.name) }}
            </nuxt-link>
          </div>
          <div
            class="d-none pt-3 w-100 w-md-auto site-link font-weight-500 text-capitalize"
          >
            <nuxt-link exact :to="localePath('/bot')">
              {{ $t('Bot') }}
            </nuxt-link>
          </div>
        </div>
        <div v-if="menu2.menuData.length > 0" class="col-12 col-md-6 pt-5">
          <p class="text-uppercase font-weight-bold mb-0">
            {{ $t(menu2.title) }}
          </p>
          <div
            v-for="(item, index) in menu2.menuData"
            :key="index"
            class="pt-3 w-100 w-md-auto site-link font-weight-500 text-capitalize"
          >
            <nuxt-link exact :to="localePath(item.url)">
              {{ $t(item.name) }}
            </nuxt-link>
          </div>
        </div>
      </div>

      <!-- dropdown button -->
      <div v-if="!isBlockedStore" class="col-12 mt-4 d-md-none d-flex justify-content-center flex-wrap">
        <language-select :lang="currentLanguage" />
        <currency-select :currency="currentCurrency" />
      </div>

      <div class="order-lg-1 col-12 col-md-6 pt-5">
        <!-- logo-->
        <nuxt-link :to="localePath('/')" class="site-logo">
          <img
            v-if="storeInfo && storeInfo.logo_url"
            :src="$imgUrl(storeInfo.logo_url,'logo')"
            :onerror="`this.onerror=null;this.src='${$config.publicPath}/images/logo-placeholder.png';`"
            :alt="storeInfo.name"
            loading="lazy"
          >
          <span v-else>
            {{ storeInfo.name }}
          </span>
        </nuxt-link>
        <!-- contact -->
        <div class="contact-text pt-3">
          <p v-if="isShowExtraFooterInfo && storeContact.phone" class="font-weight-500 mb-1">
            {{ storeInfo.name }}
          </p>
          <p v-if="isShowExtraFooterInfo && storeContact.email_info" class="mb-0">
            <span class="font-weight-500"><span>{{ $t('Email') }}: </span><span>{{ storeContact.email_info }}</span></span>
          </p>
          <p v-if="storeContact.address && !isBlockedStore" class="mb-0">
            <span class="font-weight-500"><span>{{ $t('Address') }}: </span><span>{{ storeContact.address }}</span></span>
          </p>
          <div v-if="isShowExtraFooterInfo && storeContact.phone">
            <div class="d-flex align-items-center justify-content-center" :class="[isRtl ? 'justify-content-lg-end' : 'justify-content-lg-start']">
              <span class="font-weight-500">{{ $t('Phone number') }}: </span>
              <span class="font-weight-500 ml-2">
                {{ decode(encode(storeContact.phone)) }}
              </span>
            </div>
          </div>
          <client-only v-else>
            <div class="d-flex align-items-center justify-content-center" :class="[isRtl ? 'justify-content-lg-end' : 'justify-content-lg-start']">
              <span class="font-weight-500">{{ $t('Phone number') }}: </span>
              <b-dropdown
                v-if="storeInfo.status === 'verified'"
                variant="outline-custom"
                toggle-class="pl-1 pr-4"
                size="sm"
              >
                <span slot="button-content" class="font-weight-500">
                  <span>
                    <img
                      v-for="(flag, flagIndex) in currenctContactPhoneData.flags"
                      :key="flagIndex"
                      :src="flag"
                      alt="flag"
                      class="mr-1"
                      loading="lazy"
                    >
                  </span>
                  {{ decode(encode(currenctContactPhoneData.phone)) }}
                </span>
                <b-dropdown-item
                  v-for="(item, index) in contactPhoneData"
                  :key="index"
                  :active="currentContactPhone===index"
                  @click="currentContactPhone = index"
                >
                  <span class="font-weight-500">
                    <span>
                      <img
                        v-for="(flag, flagIndex) in item.flags"
                        :key="flagIndex"
                        :src="flag"
                        alt="flag"
                        class="mr-1"
                        loading="lazy"
                      >
                    </span>
                    {{ decode(encode(item.phone)) }}
                  </span>
                </b-dropdown-item>
              </b-dropdown>
              <b-dropdown
                v-else
                variant="outline-custom"
                toggle-class="pl-1 pr-4"
              >
                <span slot="button-content" class="font-weight-500">
                  <img
                    v-for="(flag, flagIndex) in currenctContactPhoneData.flags"
                    :key="flagIndex"
                    :src="flag"
                    alt="flag"
                    class="mr-1"
                    loading="lazy"
                  >
                  {{ decode(encode(currenctContactPhoneData.phone)) }}
                </span>
                <b-dropdown-item
                  v-for="(item, index) in contactPhoneData2"
                  :key="index"
                  :active="currentContactPhone===index"
                  @click="currentContactPhone = index"
                >
                  <span class="font-weight-500">
                    <img
                      v-for="(flag, flagIndex) in item.flags"
                      :key="flagIndex"
                      :src="flag"
                      alt="flag"
                      class="mr-1"
                      loading="lazy"
                    >
                    {{ decode(encode(item.phone)) }}
                  </span>
                </b-dropdown-item>
              </b-dropdown>
            </div>
            <p v-if="$store.state.storeInfo && $store.state.storeInfo.enable_contract_form" class="mt-1">
              <span class="font-weight-500">{{ $t('Need support?') }}
                <nuxt-link :to="localePath('/page/contact-us')" class="text-primary">{{ $t('Submit a ticket') }}</nuxt-link>
              </span>
            </p>
          </client-only>
          <p v-if="isShowExtraFooterInfo && storeContact.phone" class="mb-0">
            <span v-if="$store.state.storeInfo.enable_contract_form" class="font-weight-500">{{ $t('Need support?') }}
              <nuxt-link :to="localePath('/page/contact-us')" class="text-primary">{{ $t('Submit a ticket') }}</nuxt-link>
            </span>
          </p>
        </div>
      </div>
      <div v-if="!isBlockedStore" class="order-lg-3 col-12 col-md-6 col-lg-4 pt-5">
        <template v-if="$store.state.storeInfo && !$store.state.storeInfo.disable_promotion">
          <!-- Subscribe Form -->
          <p class="font-weight-bold text-uppercase">
            {{ $t('Subscribe to our newsletter') }}
          </p>
          <p>
            {{ $t('For sales, exclusive content, and more!') }}
          </p>
          <form class="mt-2 mb-4" @submit.prevent="subscribeEmail">
            <b-input-group class="d-flex justify-content-center justify-content-lg-start">
              <b-form-input
                id="newsletter_email"
                v-model="emailNewsletter"
                type="email"
                name="newsletter_email"
                required
                size="sm"
                :placeholder="$t('Enter your email')"
              />
              <b-input-group-append>
                <b-button
                  size="sm"
                  variant="dark"
                  type="submit"
                  block
                  class="border-radius-none"
                >
                  {{ $t('Send') }}
                </b-button>
              </b-input-group-append>
            </b-input-group>
          </form>
        </template>

        <!-- social -->
        <p v-if="socialsLink.facebook || socialsLink.instagram || socialsLink.skype || socialsLink.pinterest || socialsLink.twitter || socialsLink.youtube || socialsLink.google" class="font-weight-bold mb-1">
          {{ $t('Follow us') }}
        </p>
        <div v-if="socialsLink.facebook || socialsLink.instagram || socialsLink.skype || socialsLink.pinterest || socialsLink.twitter || socialsLink.youtube || socialsLink.google" class="d-flex contact-icon">
          <a
            v-if="socialsLink.facebook"
            :href="getSocialLink(socialsLink.facebook)"
            target="_blank"
            class="mr-2"
            rel="noopener noreferrer nofollow"
          >
            <span><i class="icon-sen-facebook" /></span>
          </a>
          <a
            v-if="socialsLink.instagram"
            :href="getSocialLink(socialsLink.instagram)"
            target="_blank"
            class="mr-2"
            rel="noopener noreferrer nofollow"
          >
            <span><i class="icon-sen-instagram" /></span>
          </a>
          <a
            v-if="socialsLink.skype"
            :href="getSocialLink(socialsLink.skype)"
            target="_blank"
            class="mr-2"
            rel="noopener noreferrer nofollow"
          >
            <span><i class="icon-sen-skype-business" /></span>
          </a>
          <a
            v-if="socialsLink.pinterest"
            :href="getSocialLink(socialsLink.pinterest)"
            target="_blank"
            class="mr-2"
            rel="noopener noreferrer nofollow"
          >
            <span><i class="icon-sen-pinterest" /></span>
          </a>
          <a
            v-if="socialsLink.twitter"
            :href="getSocialLink(socialsLink.twitter)"
            target="_blank"
            class="mr-2"
            rel="noopener noreferrer nofollow"
          >
            <span><i class="icon-sen-twitter" /></span>
          </a>
          <a
            v-if="socialsLink.google"
            :href="getSocialLink(socialsLink.google)"
            target="_blank"
            class="mr-2"
            rel="noopener noreferrer nofollow"
          >
            <span><i class="icon-sen-google" /></span>
          </a>
          <a
            v-if="socialsLink.youtube"
            :href="getSocialLink(socialsLink.youtube)"
            target="_blank"
            class="mr-2"
            rel="noopener noreferrer nofollow"
          >
            <span><i class="icon-sen-youtube" /></span>
          </a>
        </div>

        <payment-gateway-accepted />
      </div>
    </div>

    <html-head-tag :for-position="'footer_middle'" />

    <div v-if="!isBlockedStore" class="copyright border-top mb-5 mb-md-0 pb-5 pb-md-0 pt-3 pt-md-0">
      <div class="container-xl d-flex justify-content-center justify-content-md-between align-items-center">
        <small class="font-weight-500" v-html="copyrightText" />
        <!-- dropdown button -->
        <div data-test-id="language-currency" class="d-none d-md-flex align-items-center">
          <span class="front-weight-500">{{ isOrderPage ? $t('Language:') : $t('Language/currency:') }}</span>
          <language-select :lang="currentLanguage" data-test-id="language-select" />
          <currency-select v-if="!isOrderPage" :currency="currentCurrency" data-test-id="currency-select" />
        </div>
      </div>
    </div>

    <html-head-tag :for-position="'footer_end'" />
  </footer>
</template>

<script>
import footerMixin from '~/mixins/footer'
import rtl from '~/mixins/rtl'
import paymentGatewayAccepted from '~/components/paymentGatewayAccepted.vue'
export default {
  components: {
    paymentGatewayAccepted
  },
  mixins: [footerMixin, rtl],
  computed: {
    spriteUrl () {
      return `--sprite-url: url("${this.$config.publicPath}/images/logo_cart_sprite.webp")`
    },
    copyrightText () {
      const { storeInfo } = this

      if (!storeInfo || !storeInfo.foot_line) {
        return `Copyright &copy; ${storeInfo.currentDomain} - All rights reserved.`
      }

      // render template variables
      // input: {copyright} {year} SenPrints
      // output: &copy; <current-year> SenPrints

      let footline = storeInfo.foot_line

      if (footline.includes('{year}')) {
        footline = footline.replace('{year}', `2020-${new Date().getFullYear()}`)
      }

      if (footline.includes('{copyright}')) {
        footline = footline.replace('{copyright}', '&copy;')
      }

      return footline
    },
    isShowExtraFooterInfo () {
      if (!this.$store.state.storeInfo) {
        return false
      }
      if (this.$store.state.storeInfo.enable_custom_phone) {
        return true
      }
      if (!this.$store.state.storeInfo.tags) {
        return false
      }
      return this.$store.state.storeInfo.tags.includes('custom phone') || this.$store.state.storeInfo.tags.includes('custom contact info')
    }
  }
}
</script>

<style lang="scss">

.site-footer{

  background-color: #F6F7F9;
  font-size: 14px;

  .dropdown-item,
  .dropdown-toggle {
    font-size: 14px;
  }

  .site-logo {
    font-family: 'Keep Calm Regular',serif;
    img {
      max-height: 35px;
    }
  }

  .contact-text {
    img {
      width: 30px;
    }
  }

  .contact-icon {
    span{
      cursor: pointer;
      display: inline-block;
      position: relative;
      height: 36px;
      width: 36px;
      background-color: black;
      color: white;
      border-radius: 50%;
      i {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 1.5rem;
      }
      &:hover{
        background-color: var(--primary-color);
      }
    }
  }

  @media (min-width: 992px) {
    .order-lg-1, .order-lg-3 {
      flex: 0 0 30%;
      max-width: 30%;
    }

    .order-lg-2 {
      flex: 0 0 40%;
      max-width: 40%;
    }
  }

  .footer-dropdown{
    min-width: 135px;
  }
}
</style>
