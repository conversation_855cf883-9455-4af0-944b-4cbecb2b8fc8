<template>
  <vue-slick-carousel
    v-bind="setting"
    ref="carousel"
    class="custom-carousel w-100"
    :class="carouselClasses"
    @beforeChange="(oldValue, newValue)=>{
      $emit('beforeChange', newValue)
    }"
  >
    <slot />
    <div slot="prevArrow" class="border-radius-none custom-arrow prev-arrow transition-fade-1">
      <i class="icon-sen-chevron-left absolute-center" />
    </div>
    <div slot="nextArrow" class="border-radius-none custom-arrow next-arrow transition-fade-1">
      <i class="icon-sen-chevron-right absolute-center" />
    </div>
    <div slot="customPaging" class="custom-dot transition-fade-1" />
  </vue-slick-carousel>
</template>

<script>
export default {
  name: 'CustomCarousel',
  // eslint-disable-next-line vue/require-prop-types
  props: ['setting', 'carouselClasses'],
  methods: {
    goTo
  }
}

function goTo (index) {
  this.$refs.carousel.goTo(index)
}
</script>

<style lang="scss">
.custom-carousel {
  position: relative;
  height: fit-content;

  .custom-arrow {
    background-color: transparent;
    color: transparent;
    position: absolute;
    cursor: pointer;
    z-index: 1;
    height: 40px;
    width: 40px;
    top: 50%;
    font-size: 2rem;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;

    &.prev-arrow {
        left: -5px;
    }

    &.next-arrow {
        right: -5px;
    }
  }

  &:hover {
      .custom-arrow {
        background-color: rgba(0, 0, 0, 0.2);
        color: white;
      }
  }

  .slick-dots {
    display: flex !important;
    justify-content: center;
    list-style-type: none;
    position: absolute;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    padding: 10px 0;

    & > li {
        padding: 5px;
        cursor: pointer;
    }

    .custom-dot {
        height: 3px;
        width: 25px;
        background-color: rgb(219, 219, 219);
    }

    .slick-active {
      .custom-dot {
          background-color: rgb(194, 194, 194);
          width: 35px;
      }
    }
  }
}

.campaign-carousel-images{
  .custom-arrow {
    color: rgba(0, 0, 0, 0.5);
  }
}
</style>
