<template>
  <div class="share-box">
    <div>
      <div class="text-info mr-2 font-weight-500">
        {{ $t('Share') }}
      </div>
      <div class="mt-md-2 d-flex">
        <div class="share-item">
          <a :href="`https://twitter.com/share?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
            <img :src="getIcon('twitter')" alt="Share on Twitter" loading="lazy">
          </a>
        </div>
        <div class="share-item">
          <a :href="`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
            <img :src="getIcon('facebook')" alt="Share on Facebook" loading="lazy">
          </a>
        </div>
        <div v-if="false" class="share-item">
          <a :href="`https://www.instagram.com/?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
            <img :src="getIcon('instagram')" alt="Share on Instagram" loading="lazy">
          </a>
        </div>
        <div class="share-item">
          <a :href="`https://t.me/share/url?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
            <img :src="getIcon('telegram')" alt="Share on Telegram" loading="lazy">
          </a>
        </div>
        <div class="share-item">
          <a :href="`https://pinterest.com/pin/create/button/?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
            <img :src="getIcon('pinterest')" alt="Share on Pinterest" loading="lazy">
          </a>
        </div>
      </div>
    </div>
    <div v-if="campaign && campaign.seller && $store.state.storeInfo.id === 1" class="mt-2 mt-md-0">
      <span style="color: #979797">{{ $t('Designed and Sold by') }} </span>
      <nuxt-link v-if="campaign.seller.slug" :to="localePath(`/artist/${campaign.seller.slug}`)" class="text-color-primary">
        <strong>{{ campaign.seller.nickname }}</strong>
      </nuxt-link>
      <strong v-else>{{ campaign.seller.nickname }}</strong>
    </div>
  </div>
</template>
<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['campaign'],
  data () {
    return {
      baseUrl: ''
    }
  },
  computed: {
    encodedUrl () {
      return encodeURIComponent(this.baseUrl + this.$route.fullPath)
    }
  },
  mounted () {
    this.baseUrl = window.location.origin
  },
  methods: {
    getIcon (name) {
      return `${this.$config.publicPath}/images/share/${name}.svg`
    }
  }
}
</script>

<style lang="scss">
.share-item {
  margin-right: 10px;

  img {
    height: 30px;
    width: 30px;
  }
}
</style>
