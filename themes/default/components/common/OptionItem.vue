<template>
  <b-button
    sp-action="change_size"
    variant="outline-custom3"
    class="border m-1 py-1 px-1 option-filter-item text-center font-weight-500"
    data-test-id="option-filter-item"
    dusk="option-filter-item"
    :class="{active}"
  >
    {{ value }}
  </b-button>
</template>
<script>
export default {
  name: 'OptionFilterItem',
  // eslint-disable-next-line vue/require-prop-types
  props: ['value', 'active']

}
</script>

<style lang="scss">
.option-filter-item {
  min-width: fit-content;
  width: 55px;
  text-transform: uppercase;

  &.active {
    border-color: var(--primary-color) !important;
    background-color: var(--primary-color);
    color: white !important;
  }
}
</style>
