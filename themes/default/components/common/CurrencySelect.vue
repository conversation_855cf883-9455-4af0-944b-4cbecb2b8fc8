<template>
  <div @mouseover="$refs.dropdownCurrency.show()" @mouseleave="$refs.dropdownCurrency.hide()">
    <b-dropdown
      ref="dropdownCurrency"
      data-test-id="currency-dropdown"
      class="footer-dropdown m-2 shadow-1"
      variant="light"
      size="sm"
      no-caret
    >
      <template #button-content>
        <div data-test-id="current-currency" class="d-flex align-items-center">
          <span class="d-inline-flex" style="font-size: 1rem;"><span :class="`vti__flag ${currency.locale==='en'?'us':currency.locale.slice(-2).toLowerCase()}`" /></span>
          <span data-test-id="current-currency-name">{{ currency.name }}</span>
          <span class="ml-1">{{ getCurrencySymbol(currency.locale==='en'?'en-US':currency.locale, currency.code) }}</span>
        </div>
      </template>
      <b-dropdown-item
        v-for="(item, index) in currencyList"
        :key="index"
        :data-test-id="`currency-${item.code.toLowerCase()}-item`"
        :active="item.code===currency.code"
        @click="changeCurrency(item)"
      >
        <div class="d-flex align-items-center">
          <span class="d-inline-flex" style="font-size: 1rem;"><span :class="`vti__flag ${item.locale.slice(-2).toLowerCase()}`" /></span>
          <span>{{ item.name }}</span>
          <span class="ml-1">{{ getCurrencySymbol(item.locale, item.code) }}</span>
        </div>
      </b-dropdown-item>
    </b-dropdown>
  </div>
</template>

<script>
import lscache from 'lscache'
import { getCurrencySymbol } from '~/helpers/function'

export default {
  props: {
    currency: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      currencyList: this.$store.state.generalInfo?.currencies || []
    }
  },
  methods: {
    getCurrencySymbol,
    changeCurrency
  }
}
function changeCurrency (currency) {
  lscache.remove('is_fallback_currency')
  this.$store.commit('changeCurrency', currency)
}
</script>
