<template>
  <nuxt-link :to="localePath({path: `${slug}`})">
    <b-button
      variant="outline-secondary"
      class="m-1 px-3 py-0 collection-filter-item text-center"
      :class="{active}"
      :title="value"
    >
      <small class="font-weight-600">
        {{ value }}
      </small>
    </b-button>
  </nuxt-link>
</template>
<script>
export default {
  name: 'CollectionFilterItem',
  // eslint-disable-next-line vue/require-prop-types
  props: ['value', 'active', 'slug']

}
</script>

<style lang="scss">
button.btn.collection-filter-item {
  min-width: 60px;
  text-transform: uppercase;
  border-radius: 50px !important;
  min-width: max-content;
  width: max-content;
}
</style>
