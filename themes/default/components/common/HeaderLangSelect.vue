<template>
  <b-dropdown
    v-if="userCountryCode"
    ref="el"
    variant="link"
    no-caret
    :right="isSmall"
    @hide="isShowOtherLang = false"
  >
    <template #button-content>
      <img class="country-flag" :src="`images/country-flag/${userCountryCode.toLowerCase()}.svg`">
    </template>
    <b-dropdown-item
      v-for="locale in headerLangs"
      :key="locale.code"
      :active="$i18n.locale === locale.code"
      sp-action="change_header_lang"
      @click.stop.prevent="$i18n.setLocale(locale.code)"
    >
      <div class="d-flex align-items-center">
        <span class="d-inline-flex" style="font-size: 1rem;"><span :class="`vti__flag ${locale.countryCode[0].toLowerCase()}`" /></span>
        <span>{{ locale.name }}</span>
      </div>
    </b-dropdown-item>
    <div v-if="!isShowOtherLang" class="dropdown-item" style="text-align: center; font-weight: 600; cursor: pointer;" @click.stop="isShowOtherLang = true">
      . . .
    </div>
    <template v-else>
      <b-dropdown-item
        v-for="locale in otherHeaderLangs"
        :key="locale.code"
        :active="$i18n.locale === locale.code"
        sp-action="change_header_lang"
        @click.stop="$i18n.setLocale(locale.code)"
      >
        <div class="d-flex align-items-center">
          <span class="d-inline-flex" style="font-size: 1rem;"><span :class="`vti__flag ${locale.countryCode[0].toLowerCase()}`" /></span>
          <span>{{ locale.name }}</span>
        </div>
      </b-dropdown-item>
    </template>
  </b-dropdown>
</template>

<script>
import lscache from 'lscache'

export default {
  name: 'HeaderLangSelect',
  props: {
    isSmall: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isShowOtherLang: false
    }
  },
  computed: {
    userLocaleCode,
    headerLangs,
    otherHeaderLangs,
    browserLocaleCode,
    userCountryCode
  }
}
function userCountryCode () {
  const userInfo = lscache.get('userInfo')
  const userPreferCountry = userInfo?.country || ''
  if (userPreferCountry !== '') {
    return userPreferCountry
  }

  return this.$store.state.userCountry
}
function userLocaleCode () {
  return this.$i18n.locales.find(locale => locale.countryCode.includes(this.userCountryCode))?.code || 'en'
}
function browserLocaleCode () {
  return this.$i18n.getBrowserLocale().slice(0, 2).toLowerCase() || 'en'
}
function headerLangs () {
  const primary = ['en', 'de', 'es', 'fr', 'zh']
  const codes = [this.browserLocaleCode]
  if (!codes.includes(this.userLocaleCode)) {
    codes.push(this.userLocaleCode)
  }
  primary.forEach((code) => {
    if (!codes.includes(code)) {
      codes.push(code)
    }
  })
  const locales = []
  codes.forEach((code) => {
    locales.push(this.$i18n.locales.find(locale => locale.code === code))
  })
  return locales
}
function otherHeaderLangs () {
  return this.$i18n.locales.filter(locale => !this.headerLangs.map(_locale => _locale.code).includes(locale.code))
}
</script>
<style>
.country-flag {
  height: 1.5rem;
  border: 1px solid #e9e9e9;
  margin-left: 1rem;
}
.delivery:hover .country-flag {
  opacity: 0.9;
}
</style>
