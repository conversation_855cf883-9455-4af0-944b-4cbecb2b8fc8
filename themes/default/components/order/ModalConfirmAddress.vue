<template>
  <b-modal
    id="modalConfirmAddress"
    v-model="isShowModal"
    centered
    hide-footer
    size="lg"
    body-class="d-flex flex-wrap justify-content-center"
    header-class="py-2 border-none"
    :no-close-on-backdrop="true"
    :no-close-on-esc="true"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalConfirmAddress', null)"
    @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalConfirmAddress', null)"
  >
    <template #modal-header>
      <h5 id="modalConfirmAddress___BV_modal_title_" class="modal-title">
        {{ $t('Confirm Address') }}
      </h5>
    </template>
    <div class="col-12 py-2 text-center">
      <h5>
        <div class="text-danger" v-text="$t('Our system has detected that your address may be invalid. Please confirm the shipping address within 12 hours; otherwise, you will be liable in case of non-delivery.')" />
        <em>
          <br>
          "{{ order.address }} {{ order.address_2 || '' }}
          <br>
          {{ order.city }} {{ order.state || '' }} {{ order.postcode || '' }}
          <br>
          <span class="d-inline-block" :class="`vti__flag ${currentCountry.code && currentCountry.code.toLowerCase()}`" /> {{ currentCountry.name }}"
          <br>
        </em>
      </h5>
      <h5 class="mt-3" v-text="$t('Do you want to edit address?')" />
    </div>
    <div class="col-6">
      <b-button
        block
        variant="secondary"
        class="border-radius-none"
        @click="$store.dispatch('order/confirmAddress', order.access_token);isShowModal=false"
      >
        {{ $t("It's correct") }}
      </b-button>
    </div>
    <div class="m-0 col-6">
      <b-button
        block
        variant="primary"
        class="border-radius-none"
        @click="$emit('showEditAddress'); isShowModal = false; "
      >
        {{ $t('Edit Address') }}
      </b-button>
    </div>
  </b-modal>
</template>
<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['order'],
  data () {
    return {
      isShowModal: false
    }
  },
  computed: {
    currentCountry
  }
}

function currentCountry () {
  if (!this.order.country) {
    return ''
  }
  return this.$store.state.generalInfo?.countries.find(country => country.code === this.order.country)
}
</script>
