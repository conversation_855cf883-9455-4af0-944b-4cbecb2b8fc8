<template>
  <div class="go-checkout">
    <div data-test-id="checkout-subtotal" class=" d-flex justify-content-between mb-3">
      <strong data-test-id="checkout-subtotal-label"> {{ $t('Subtotal') }}</strong>
      <span data-test-id="checkout-subtotal-amount">{{ $formatPriceByRate(order.total_product_amount, order.currency_rate, order. currency_code) }}  </span>
    </div>
    <div v-if="order.total_shipping_amount" data-test-id="checkout-shipping" class="d-flex justify-content-between mb-3">
      <span data-test-id="checkout-shipping-label"> {{ $t('Shipping') }} </span>
      <span data-test-id="checkout-shipping-amount">{{ $formatPriceByRate(order.total_shipping_amount, order.currency_rate, order. currency_code) }} </span>
    </div>
    <div v-if="order.insurance_fee" data-test-id="checkout-insurance" class="d-flex justify-content-between mb-3">
      <span data-test-id="checkout-insurance-label"> {{ $t('Shipping insurance') }} </span>
      <span data-test-id="checkout-insurance-fee">{{ $formatPriceByRate(order.insurance_fee, order.currency_rate, order. currency_code) }} </span>
    </div>
    <div v-if="order.total_discount" data-test-id="checkout-discount" class="d-flex justify-content-between mb-3">
      <div data-test-id="checkout-discount-label" class="label discount-label">
        {{ $t('Discount') }}
        <span v-if="order.discount_code" data-test-id="checkout-discount-code" class="text-uppercase text-success border border-success p-1">{{ order.discount_code }}</span>
      </div>
      <span data-test-id="checkout-discount-amount" class="text-custom">- {{ $formatPriceByRate(order.total_discount, order.currency_rate, order. currency_code) }}</span>
    </div>
    <div v-if="order.payment_discount" data-test-id="checkout-payment-discount" class="d-flex justify-content-between mb-3">
      <div data-test-id="checkout-payment-discount-label" class="label discount-label">
        {{ $t('Payment discount') }}
      </div>
      <span data-test-id="checkout-payment-discount-amount">{{ $formatPriceByRate(order.payment_discount, order.currency_rate, order. currency_code) }}</span>
    </div>
    <div v-if="order.total_tax_amount" data-test-id="checkout-tax" class="d-flex justify-content-between mb-3">
      <span data-test-id="checkout-tax-label">{{ $t('Tax') }}</span>
      <span data-test-id="checkout-tax-amount">{{ $formatPriceByRate(order.total_tax_amount, order.currency_rate, order. currency_code) }}</span>
    </div>
    <div v-if="order.tip_amount" data-test-id="checkout-tip" class="d-flex justify-content-between mb-3">
      <span data-test-id="checkout-tip-label">{{ $t('Tip') }}</span>
      <span data-test-id="checkout-tip-amount">{{ $formatPriceByRate(order.tip_amount, order.currency_rate, order. currency_code) }}</span>
    </div>
    <hr class="w-100 my-3">
    <div data-test-id="checkout-total" class="d-flex justify-content-between mb-6 total">
      <strong data-test-id="checkout-total-label">{{ $t('Total') }}</strong>
      <span data-test-id="checkout-total-amount" class="text-bold">{{ $formatPriceByRate(order.total_amount, order.currency_rate, order. currency_code) }}</span>
    </div>
    <b-alert v-if="order.currency_code !== 'USD' && order.currency_code !== 'EUR' && order.country !== 'GB'" variant="info" class="text-center mt-3" show>
      {{ $t('You will be charged in USD') }} <span class="font-weight-500">&nbsp;${{ Number(order.total_amount.toFixed(2)) }}</span>
    </b-alert>
  </div>
</template>

<script>
export default {
  name: 'OrderSummary',
  // eslint-disable-next-line vue/require-prop-types
  props: ['order'],
}
</script>

<style scoped>
.total {
  font-size: 1.5em;
  font-weight: 500;
  letter-spacing: -0.04em;
  line-height: 1em;
  color: #323232;
}
</style>
