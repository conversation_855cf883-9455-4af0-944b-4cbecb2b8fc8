<template>
  <b-modal
    id="modalRequestCancelOrder"
    v-model="isShowModal"
    centered
    hide-footer
    size="lg"
    body-class="d-flex flex-wrap justify-content-center"
    header-class="py-2 border-none"
    :no-close-on-backdrop="true"
    :no-close-on-esc="true"
    :title="$t('Confirmation')"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalRequestCancelOrder', null)"
    @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalRequestCancelOrder', null)"
  >
    <template #modal-header-close>
      <span class="close-icon"><i class="icon-sen-close-circle" /></span>
    </template>
    <div class="col-12 py-2 text-center">
      <h6 class="mb-5 text-center w-100 text-uppercase">
        {{ $t('Do you want to cancel this order') }}?
      </h6>
    </div>
    <div class="col-6">
      <b-button
        block
        variant="secondary"
        class="border-radius-none"
        @click="confirmCancelOrder"
      >
        {{ $t("Proceed") }}
      </b-button>
    </div>
    <div class="m-0 col-6">
      <b-button
        block
        variant="primary"
        class="border-radius-none"
        @click="isShowModal = false"
      >
        {{ $t('Resume order') }}
      </b-button>
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'ModalRequestCancelOrder',
  props: {
    order: {
      type: Object,
      default: () => ({})
    },
    setSuccessCancelOrder: {
      type: Function,
      default: () => {}
    },
  },
  data () {
    return {
      isShowModal: false
    }
  },
  methods: {
    confirmCancelOrder () {
      this.$store.dispatch('order/confirmCancelOrder', this.order.access_token).then((result) => {
        this.isShowModal = false
        if (result && result.success) {
          this.$store.commit('order/UPDATE_ORDER_DATA', {
            order: {
              request_cancel: {
                order_id: this.order.id,
                status: 'pending',
                sent_email: 0
              }
            }
          })
          this.$emit('setSuccessCancelOrder', true)
          this.$nuxt.refresh()
        } else {
          this.$toast.error(result.message)
        }
      })
    }
  }
}

</script>
