<template>
  <div>
    <p class="mb-2 mt-1">
      <a
        href="#"
        class="text-custom cursor-pointer"
        @click.prevent="() => { userTrigger = true; promotionShow = !promotionShow; }"
      >
        {{ $t('Have a discount code?') }}
      </a>
    </p>
    <b-form v-if="discountApply || promotionShow" class="mb-3" @submit.prevent="$emit('updateCheckoutDiscount')">
      <b-input-group class="flex">
        <b-form-input
          id="coupon_code"
          ref="coupon_code_input"
          :value="discountApply"
          type="text"
          class="col-auto"
          name="coupon_code"
          required
          data-test-id="checkout-discount-input"
          :state="checkDiscount ? (discountApply === order.discount_code && order.total_discount) || (discountApply !== order.discount_code && discountApply.length === 0 && order.total_discount > 0) ? true: false : null"
          :placeholder="$t('Discount Code')"
          @input="(value) => {
            $emit('updateDiscountApply', value);
          }"
        />
        <b-input-group-append>
          <b-button
            variant="dark"
            class="border-radius-none"
            data-test-id="checkout-discount-apply"
            :disabled="isDisabled"
            @click="$emit('updateCheckoutDiscount')"
          >
            <b-spinner v-if="isLoading" class="float-right" style="width: 24px; height: 24px;" />
            <span v-else>
              {{ $t('Apply') }}
            </span>
          </b-button>
          <b-button
            v-if="promotionsList && promotionsList.length"
            variant="custom"
            class="border-radius-none ml-3"
            data-test-id="checkout-discount-show-list"
            @click="isShowListPromotionModal = true"
          >
            {{ $t('View code') }}
          </b-button>
        </b-input-group-append>
        <b-form-invalid-feedback id="coupon_code-feedback">
          <span v-if="checkDiscount && discountApply !== order.discount_code && discountErrorType === discountErrorTypeEnum.USING_OTHER_DISCOUNT">
            {{ $t('Order has been discounted to the maximum, no further discount codes can be applied.') }}
          </span>
          <span v-if="checkDiscount && discountApply !== order.discount_code && discountErrorType === discountErrorTypeEnum.NONE">
            {{ $t('Discount code is invalid') }}
          </span>

          <nuxt-link v-if="checkDiscount && discountApply === order.discount_code && !order.total_discount" class="text-danger" :to="localePath('/cart')">
            {{ $t("Discount conditions has not met") }}. {{ $t("Do you want to add more items?") }}?
          </nuxt-link>
        </b-form-invalid-feedback>
        <b-form-valid-feedback id="coupon_code-feedback">
          <span v-if="checkDiscount && ((discountApply === order.discount_code && order.total_discount) || (discountApply !== order.discount_code && discountApply.length === 0 && order.total_discount > 0))">
            {{ $t('Discount applied') }}
          </span>
        </b-form-valid-feedback>
      </b-input-group>
    </b-form>
    <b-modal
      v-if="promotionsList && promotionsList.length"
      v-model="isShowListPromotionModal"
      centered
      size="lg"
      :title="$t('Select a discount code')"
      hide-footer
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalPromotion', null)"
      @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalPromotion', null)"
    >
      <promotions-list
        :promotions-list="promotionsList"
        :is-checkout-page="true"
        @updateDiscountApply="value=>{
          $emit('updateDiscountApply', value);
          $emit('updateCheckoutDiscount');
          isShowListPromotionModal = false
        }"
      />
    </b-modal>
  </div>
</template>

<script>
import promotionsList from '../common/PromotionsList'
import { DISCOUNT_ERROR_TYPE } from '~/helpers/variableConst'
export default {
  name: 'CouponForm',
  components: {
    promotionsList
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['order', 'discountApply', 'promotionsList', 'checkDiscount', 'isDisabled', 'isLoading', 'discountErrorType'],
  data () {
    return {
      promotionShow: false,
      userTrigger: false,
      isShowListPromotionModal: false,
      discountErrorTypeEnum: DISCOUNT_ERROR_TYPE
    }
  },
  watch: {
    promotionShow (value) {
      if (value && this.userTrigger) { this.$nextTick(() => this.$refs.coupon_code_input.focus()) }
      this.userTrigger = false
    },
    promotionsList () {
      // if (!window.matchMedia('(pointer:coarse)')?.matches || !!this.discountApply) {
      //   this.promotionShow = !!this.discountApply || (this.promotionsList && this.promotionsList.length)
      // }
      this.promotionShow = !!this.discountApply || (this.promotionsList && this.promotionsList.length)
    }
  }
}

</script>

<style scoped>
.classic .input-group-append .border-radius-none {
  border-top-right-radius: 0.3rem !important;
  border-bottom-right-radius: 0.3rem !important;
}
.classic .input-group-append .border-radius-none:last-child {
  border-top-left-radius: 0.3rem !important;
  border-bottom-left-radius: 0.3rem !important;
}

.total {
  font-size: 1.3rem;
}
</style>
