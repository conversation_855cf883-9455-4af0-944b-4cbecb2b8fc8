<template>
  <div class="w-100 d-flex align-items-center product-order-item my-3" :class="{'item-no-ship': item.fulfill_status === 'no_ship'}">
    <div
      v-if="showImg"
      class="image-box"
    >
      <client-only>
        <product-img
          v-if="item.thumb_url"
          :path="item.thumb_url"
          type="listing"
          :alt="item.campaign_title"
          :color="options && options.color?$colorVal(options.color):''"
          @click.native="$store.commit('changeViewImagePath', item.thumb_url)"
        />
      </client-only>
    </div>
    <div class="px-2 product-order-info align-items-center position-relative">
      <nuxt-link :to="item.product_url || '#'">
        <h6
          class="mb-0 text-overflow-hidden cursor-pointer"
        >
          <span class="text-overflow-hidden">
            {{ item.product_name }}
          </span>
        </h6>
      </nuxt-link>
      <p class="mb-1 text-capitalize">
        {{ getVariant(options) }}
      </p>
      <template v-if="item.custom_options">
        <div v-if="item.personalized === 3 || item.full_printed === 5 || (item.personalized === 0 && item.full_printed === 0)" class="col-12">
          <ul v-for="(v, i) in JSON.parse(item.custom_options)" :key="i" class="pl-2 mb-2">
            <template v-for="(vC, iC) in v">
              <li v-if="vC && (vC.value || vC.imagePath)" :key="iC" style="white-space: break-spaces; font-size: 13px">
                <div :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">
                  {{ $t(vC.label) }}:
                  <a v-if="vC.type === 'image' && vC.imagePath" :href="$imgUrl(vC.imagePath, 'full')" target="_blank" @click.prevent="$store.commit('changeViewImagePath', vC.imagePath)">View image</a>
                  <span v-else :class="!(vC.type === 'image' && vC.imagePath) ? 'text-overflow-hidden' : ''">{{ vC.value }}</span>
                </div>
              </li>
            </template>
          </ul>
        </div>
        <div v-else-if="item.personalized === 1" class="col-12 px-0">
          <div v-for="(value, key) in JSON.parse(item.custom_options)" :key="key" class="text-capitalize text-overflow-hidden">
            <span>{{ key.replace(/_/g, ' ') }}</span>: <span>{{ value }}</span>
          </div>
        </div>
      </template>
      <div class="font-weight-500">
        <span>x{{ item.quantity }}</span>
        <span class="ml-3">{{ $formatPriceByRate(item.total_amount, currencyRate, currencyCode) }}</span>
      </div>
      <div v-if="item.fulfill_status === 'no_ship'" class="font-small">
        <i class="icon-sen-alert text-warning mr-2" />
        <span>{{ $t("This product variant is not available. Please try other option/product.") }}</span>
        <span
          class="cursor-pointer text-danger float-right"
          @click="$emit('removeItem', index)"
        ><i class="icon-sen-delete" /></span>
        <nuxt-link :to="localePath('/cart')">
          <span class="text-underline cursor-pointer text-color-primary float-right px-2">{{ $t('Edit') }}</span>
        </nuxt-link>
      </div>
      <product-review v-if="page !== 'checkout'" :pos="pos" :item="item" :order-status="orderStatus" :customer-name="customerName" />
    </div>
  </div>
</template>
<script>
import ProductReview from '~/themes/default/components/product-review/ProductReview'
import { getVariant } from '~/helpers/order'

export default {
  components: {
    ProductReview
  },
  // eslint-disable-next-line vue/require-prop-types
  props: {
    item: Object, // eslint-disable-line vue/require-default-prop
    currencyCode: String, // eslint-disable-line vue/require-default-prop
    currencyRate: Number, // eslint-disable-line vue/require-default-prop
    customerName: String || undefined, // eslint-disable-line vue/require-default-prop
    index: Number, // eslint-disable-line vue/require-default-prop
    orderStatus: Object || undefined, // eslint-disable-line vue/require-default-prop
    page: String, // eslint-disable-line vue/require-default-prop
    pos: undefined,
    showImg: {
      type: Boolean,
      default: true,
    }
  },
  computed: {
    options () {
      return JSON.parse(this.item.options)
    }
  },
  methods: {
    getVariant
  }
}
</script>

<style lang="scss">
.product-order-item {
  h6:hover {
    color: var(--primary-color);
  }

  .image-box {
    position: relative;
    min-width: 80px;
    min-height: 100px;

    img,
    .spinner {
      cursor: pointer;
      width: 80px;
      height: 100px;
    }

    .hover-image {
      background-color: white;
      display: none;
      position: absolute;
      z-index: 1000;
      left: 0;
      top: 0;
      width: max-content;
      img {
        width: 300px;
        height: 375px;
      }

      @media (min-width: 768px) {
        right: 100%;
        left: unset;
        img {
          width: 500px;
          height: 625px;
        }
      }
    }

    &:hover{
      .hover-image {
        display: block;
      }
    }
  }
}

#StatusPage{
  .hover-image {
    @media (min-width: 768px) {
      left: 100%;
    }
  }
}

.product-order-info {
  width: calc(100% - 80px);
}

.item-no-ship {
  background-color: #fff6e6;
}
</style>
