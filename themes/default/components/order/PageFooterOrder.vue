<template>
  <!-- Site Footer -->
  <footer class="site-footer">
    <div class="container-xl py-3">
      <div class="row">
        <div class="col-12 d-flex justify-content-center justify-content-md-between align-items-center">
          <div class="site-logo">
            <img
              v-if="storeInfo && storeInfo.logo_url"
              class="d-none d-md-block"
              :src="$imgUrl(storeInfo.logo_url,'logo')"
              :onerror="`this.onerror=null;this.src='${$config.publicPath}/images/logo-placeholder.png';`"
              :alt="storeInfo.name"
              loading="lazy"
            >
            <span v-else>
              {{ storeInfo.name }}
            </span>
          </div>
          <div class="d-none d-md-flex align-items-center">
            <span class="front-weight-500">{{ $t('Language:') }}</span>
            <language-select :lang="currentLanguage" />
          </div>
          <div style="transform: translateY(-54px); z-index: 99" class="col-12 mt-4 d-md-none d-flex justify-content-center flex-wrap">
            <language-select :lang="currentLanguage" />
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import footerMixin from '~/mixins/footer'

export default {
  mixins: [footerMixin]
}
</script>

<style lang="scss" scoped>
.site-footer{
  .site-logo {
    font-family: 'Keep Calm Regular';
    img {
      max-height: 35px;
    }
  }

  .payment-method {
    img {
      max-height: 45px;
      max-width: 100%;
    }
  }
}
</style>
