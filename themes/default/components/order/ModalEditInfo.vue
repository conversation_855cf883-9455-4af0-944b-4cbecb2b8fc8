<template>
  <b-modal
    id="modalEditAddress"
    v-model="showModalEditAddress"
    centered
    hide-footer
    header-class="py-2 border-none"
    size="lg"
    :no-close-on-backdrop="true"
    :no-close-on-esc="true"
    :hide-header-close="true"
    :title="$t('Edit address')"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalEditAddress', null)"
    @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalEditAddress', null)"
  >
    <div id="formCheckout" class="col-12">
      <b-form action="#" method="post" data-test-id="modal-edit-info" class="checkout-form" @submit.prevent.stop="handleSubmit">
        <b-alert
          v-if="warningText"
          show
          variant="warning"
          dismissible
          @dismissed="warningText=false"
        >
          {{ warningText }}
        </b-alert>

        <h6 class="mb-3">
          {{ $t('Contact info') }}
        </h6>

        <b-form-group
          label-class="text-overflow-hidden"
          class="inner-label-input"
          :class="{'show':userInfo.customer_email}"
          :label="$t('Email address')"
        >
          <b-form-input
            id="email"
            ref="email"
            v-model="userInfo.customer_email"
            type="email"
            name="email"
            :state="submitted ? !$v.userInfo.customer_email.$error : null"
          />
          <b-form-invalid-feedback id="email-feedback">
            <span v-if="!$v.userInfo.customer_email.required">{{ $t('Email is required') }}</span>
            <span v-else-if="!$v.userInfo.customer_email.email">{{ $t('Email is invalid') }}</span>
          </b-form-invalid-feedback>
        </b-form-group>

        <!-- order code -->
        <b-form-group
          label-class="text-overflow-hidden"
          class="inner-label-input"
          :class="{'show':userInfo.order_number}"
          :label="`${$t('Order number')} (${$t('Check in email')})`"
        >
          <b-form-input
            id="order_number"
            ref="order_number"
            v-model="userInfo.order_number"
            type="text"
            name="order_number"
            :state="submitted ? !$v.userInfo.order_number.$error : null"
          />
          <b-form-invalid-feedback id="order-code-feedback">
            <span v-if="!$v.userInfo.order_number.required">{{ $t('Order number is required') }}</span>
          </b-form-invalid-feedback>
        </b-form-group>
        <h6 class="my-3">
          {{ $t('Shipping info') }}
        </h6>

        <!-- full name -->
        <b-form-group
          label-class="text-overflow-hidden"
          class="inner-label-input"
          :class="{'show':userInfo.customer_name}"
          :label="$t('Full name')"
        >
          <b-form-input
            id="customer_name"
            ref="customer_name"
            v-model="userInfo.customer_name"
            type="text"
            name="customer_name"
            :placeholder="$t('Full name')"
            :state="submitted ? !$v.userInfo.customer_name.$error : null"
          />
          <b-form-invalid-feedback id="name-feedback">
            <span v-if="!$v.userInfo.customer_name.required">{{ $t('Full name is required') }}</span>
          </b-form-invalid-feedback>
        </b-form-group>
        <!-- address -->
        <div class="row mt-3">
          <div class="col-12 col-sm-8 pr-3 pr-sm-1">
            <b-form-group
              label-class="text-overflow-hidden"
              class="inner-label-input"
              :class="{'show':userInfo.address}"
              :label="address1Label"
            >
              <b-form-input
                id="address"
                ref="address"
                v-model="userInfo.address"
                type="text"
                name="address"
                placeholder=" "
                :state="submitted ? !$v.userInfo.address.$error : null"
              />
              <b-form-invalid-feedback id="address-feedback">
                <span v-if="!$v.userInfo.address.required">{{ $t('Address is required') }}</span>
              </b-form-invalid-feedback>
            </b-form-group>
          </div>
          <div v-if="isHasField('address_2')" class="col-12 col-sm-4 pl-3 pl-sm-1">
            <b-form-group
              label-class="text-overflow-hidden"
              class="inner-label-input"
              :class="{'show':userInfo.address_2}"
              :label="address2Label"
            >
              <b-form-input
                id="apt"
                ref="address2"
                v-model="userInfo.address_2"
                type="text"
                name="apt"
              />
            </b-form-group>
          </div>
        </div>

        <!-- city -->
        <!-- state -->
        <!-- address -->
        <div class="row">
          <div
            :class="{
              'col-sm-4 col-12': isHasField('state'),
            }"
            class="col-6 pr-sm-1"
          >
            <b-form-group
              label-class="text-overflow-hidden"
              class="inner-label-input"
              :class="{'show':userInfo.city}"
              :label="cityLabel"
            >
              <b-form-input
                id="city"
                ref="city"
                v-model="userInfo.city"
                type="text"
                name="city"
                autocomplete="city"
                :state="submitted ? !$v.userInfo.city.$error : null"
              />
              <b-form-invalid-feedback id="city-feedback">
                <span v-if="!$v.userInfo.city.required">{{ $t('Text is required', { text: cityLabel }) }}</span>
              </b-form-invalid-feedback>
            </b-form-group>
          </div>
          <div v-if="isHasField('state')" class="col-6 col-sm-4 px-sm-1 pr-1">
            <b-form-group
              label-class="col-form-label text-overflow-hidden"
              class="inner-label-input"
              :class="{'show':userInfo.state}"
              :label="stateLabel"
              label-for="state"
            >
              <b-form-select
                v-if="countryState && countryState.length"
                id="state"
                ref="state"
                v-model="userInfo.state"
                type="text"
                name="state"
                :options="countryState"
                :state="submitted && countryState ? !$v.userInfo.state.$error : null"
              />
              <b-form-input
                v-else
                id="state"
                ref="state"
                v-model="userInfo.state"
                type="text"
                name="state"
                autocomplete="state"
                :state="submitted && countryState ? !$v.userInfo.state.$error : null"
              />
              <b-form-invalid-feedback id="state-feedback">
                <span v-if="!$v.userInfo.state.checkState">{{ $t('Text is required', {text: stateLabel }) }}</span>
              </b-form-invalid-feedback>
            </b-form-group>
          </div>
          <div
            :class="{
              'col-sm-4': isHasField('state'),
            }"
            class="pl-1 col-6"
          >
            <b-form-group
              label-class="text-overflow-hidden"
              class="inner-label-input"
              :class="{'show':userInfo.postcode}"
              :label="postCodeLabel"
            >
              <b-form-input
                id="zipcode"
                ref="zipcode"
                v-model="userInfo.postcode"
                type="text"
                name="zipcode"
                autocomplete="zipcode"
                :state="submitted ? !$v.userInfo.postcode.$error : null"
              />
              <b-form-invalid-feedback id="zipcode-feedback">
                <span v-if="!$v.userInfo.postcode.required">{{ $t('Text is required', {text: postCodeLabel}) }}</span>
              </b-form-invalid-feedback>
            </b-form-group>
          </div>
          <div v-if="isHasField('mailboxNumber', false)" class="col-6">
            <b-form-group
              label-class="text-overflow-hidden"
              class="inner-label-input"
              :class="{'show':userInfo.mailbox_number}"
              :label="mailboxLabel"
            >
              <b-form-input
                id="mailbox"
                ref="mailbox"
                v-model="userInfo.mailbox_number"
                type="text"
                name="mailbox"
                autocomplete="mailbox"
              />
            </b-form-group>
          </div>
          <div v-if="isHasField('houseNumber', false)" class="col-6 pl-1">
            <b-form-group
              label-class="text-overflow-hidden"
              class="inner-label-input"
              :class="{'show':userInfo.house_number}"
              :label="houseNumberLabel"
            >
              <b-form-input
                id="house-number"
                ref="houseNumber"
                v-model="userInfo.house_number"
                type="text"
                name="house_number"
                autocomplete="house_number"
              />
            </b-form-group>
          </div>
        </div>
        <div v-if="isHasField('deliveryNote', false)" class="row">
          <div class="col-12 mb-3">
            <div>
              <b-form-textarea
                v-model="orderNote"
                :placeholder="$t('Order note')"
                rows="3"
                size="xs"
                style="overflow: hidden; resize: none;"
              />
            </div>
          </div>
        </div>
        <!-- phone -->
        <template v-if="showPhoneInput">
          <div class="position-relative">
            <b-form-group
              label-class="pr-7 text-overflow-hidden"
              class="inner-label-input mb-1"
              :class="{'show':userInfo.phone}"
            >
              <vue-tel-input
                id="phone"
                ref="phone"
                :class="{
                  'input-error': submitted && !isPhoneValid && userInfo.phone,
                  'input-success': submitted && isPhoneValid && userInfo.phone}"
                :default-country="userInfo.country"
                name="phone"
                autocomplete="phone"
                class="pr-5"
                :dropdown-options="{
                  showDialCodeInSelection: true,
                  showDialCodeInList: true,
                  showFlags:true,
                  showSearchBox: true,
                  width: '400px'
                }"
                :value="phoneInput"
                :input-options="{
                  placeholder: phoneLabel,
                  required: isPhoneNumberRequired
                }"
                @input="checkPhone"
              />
            </b-form-group>
            <div class="phone-tooltip">
              <i>?</i>
              <div class="tooltip-content font-small">
                <p class="mb-1">
                  {{ $t('In case we need to contact') }}
                </p>
                <p class="mb-0">
                  {{ $t('you about your order') }}
                </p>
              </div>
            </div>
          </div>
          <div id="phone-feedback" class="text-danger font-small">
            <span v-if="submitted && !isPhoneValid && userInfo.phone">{{ $t('Phone number is invalid') }}</span>
          </div>
        </template>

        <!-- country -->
        <div class="border p-2 h5 mt-3">
          <span class="d-inline-block" :class="`vti__flag ${currentCountry.code && currentCountry.code.toLowerCase()}`" /> {{ currentCountry.name }}
        </div>

        <div class="cursor-pointer email-checkbox d-flex align-items-center mt-3" @click="userInfo.confirm_address = !userInfo.confirm_address">
          <i class="mr-1" :class="userInfo.confirm_address ? 'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank-outline'" />
          <small>
            {{ $t('I confirm this is a valid address to receive the order') }}
          </small>
        </div>

        <b-button
          size="lg"
          :disabled="!userInfo.confirm_address"
          variant="custom"
          type="submit"
          class="mt-3"
          block
        >
          {{ $t('Save changes') }}
        </b-button>
      </b-form>
    </div>
  </b-modal>
</template>
<script>
import Vue from 'vue'
import Vuelidate from 'vuelidate'
import { email, required } from 'vuelidate/lib/validators'
import addressForm from '~/mixins/addressForm'

Vue.use(Vuelidate)

export default {
  mixins: [addressForm],
  // eslint-disable-next-line vue/require-prop-types
  props: ['order'],
  data () {
    return {
      showModalEditAddress: false,
      phoneInput: this.order.customer_phone,
      // always show phone input (ref: https://senprints.atlassian.net/browse/SDV-2177)
      showPhoneInput: true, // this.$store.state.storeInfo?.checkout_phone,
      isPhoneValid: false,
      submitted: false,
      successText: false,
      warningText: false,
      userInfo: {
        customer_name: this.order.customer_name,
        customer_email: this.order.customer_email,
        order_number: this.order.order_number,
        address: this.order.address,
        address_2: this.order.address_2,
        city: this.order.city,
        state: this.order.state,
        postcode: this.order.postcode,
        phone: this.order.customer_phone,
        country: this.order.country,
        confirm_address: false,
        house_number: this.order.house_number,
        mailbox_number: this.order.mailbox_number,
      },
      orderNote: this.order.order_note
    }
  },
  validations () {
    const userInfo = {
      customer_name: { required },
      customer_email: { required, email },
      order_number: { required },
      state: { checkState },
    }
    if (this.isRequired('address_1')) {
      userInfo.address = { required }
    }
    if (this.isRequired('city')) {
      userInfo.city = { required }
    }
    if (this.isRequired('postCode')) {
      userInfo.postcode = { required }
    }
    return {
      userInfo
    }
  },
  head,
  watch: {
    showModalEditAddress
  },
  methods: {
    handleSubmit,
  }
}

function head () {
  const script = []
  return { script }
}
function checkState (value) {
  if (!this.countryState || this.countryState.find(item => item.value === value)) {
    return true
  }
  return !this.isRequired('state')
}
function showModalEditAddress (value) {
  if (value) {
    this.$nextTick(() => {
    })
  }
}

function handleSubmit () {
  this.submitted = true

  // stop here if form is invalid
  this.$v.$touch()
  if (this.$v.$invalid) {
    if (this.$v.userInfo.customer_email.$error) {
      return this.$refs.customer_email.focus()
    }
    if (this.$v.userInfo.customer_name.$error) {
      return this.$refs.customer_name.focus()
    }
    if (this.$v.userInfo.address.$error) {
      return this.$refs.address.focus()
    }
    if (this.$v.userInfo.city.$error) {
      return this.$refs.city.focus()
    }
    if (this.countryState && this.$v.userInfo.state.$error) {
      return this.$refs.state.focus()
    }
    if (this.$v.userInfo.postcode.$error) {
      return this.$refs.postcode.focus()
    }

    try {
      if (this.userInfo.phone && !this.isPhoneValid && this.$refs.phone) {
        return this.$refs.phone.focus()
      }
    } catch (e) {
    // noop
    }
    return window.scrollTo(0, 0)
  }

  this.$store.dispatch('order/updateCustomerInfo', {
    ...this.userInfo,
    order_note: this.orderNote || undefined,
  }).then((result) => {
    if (result.success) {
      this.showModalEditAddress = false
      this.successText = result.message || this.$t('Update info success')
      this.$nuxt.refresh()
    } else {
      this.warningText = result.message || this.$t('Server busy')
    }
  }).catch(() => {
    this.warningText = this.$t('Server busy')
  })
}
</script>

<style lang="scss">

.phone-tooltip {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgb(185, 185, 185);
  color: #fff;
  border-radius: 50%;
  height: 15px;
  width: 15px;
  text-align: center;
  line-height: 15px;
  cursor: pointer;

  .tooltip-content {
    position: absolute;
    display: none;
    background-color: rgb(66, 66, 66);
    width: max-content;
    border-radius: 10px;
    padding: 0.5rem;
    top: -100%;
    left: 50%;
    transform: translate(-50%, -100%);

    &::after {
      content: "";
      position: absolute;
      bottom: -9px;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid rgb(66, 66, 66);
      left: 50%;
      transform: translateX(-50%);
    }

    @media (max-width: 992px) {
      transform: translate(-80%, -100%);

      &::after {
        left: 80%;
      }
    }
  }

  &:hover {
    .tooltip-content {
      display: inherit;
    }
  }
}

#modalEditAddress {
  .form-group {
    position: relative;
  }

  .border-option {
    border: 1px solid #ced4da;

    .delivery-item:not(:last-child),
    .item-gateway:not(:last-child) {
      border-bottom: 1px solid #ced4da;
    }
  }

  .col-form-label {
    z-index: 100;
    transition: all .2s;
    color: #737373;
    font-weight: normal;
    width: 100%;
    font-size: 16px;
    padding: 11px 13px !important;
    position: absolute;
    height: 45px;

    &.pr-7 {
      padding-right: 4rem !important;
    }

  }

  input.form-control,
  select.custom-select {
    position: relative;
    transition: all .2s;
    height: 45px;
    color: black;
    background-color: transparent;
    z-index: 100;
  }

  .inner-label-input.show {
    .col-form-label {
      color: #737373;
      font-weight: normal;
      width: 100%;
      height: auto;
      font-size: 12px;
      padding: 3px 14px !important;
      position: absolute;
      z-index: 1;
    }

    input.form-control,
    select.custom-select {
      padding-top: 1.3rem;
    }
  }

    .vti__input, .inner-label-input.show {
      .col-form-label {
        color: #737373;
        font-weight: normal;
        width: 100%;
        height: auto;
        font-size: 12px;
        padding: 3px 14px !important;
        position: absolute;
      }

      input.form-control,
      select.custom-select {
        padding-top: 1.2rem;
      }
    }

    .vue-tel-input {
      border-radius: 0;
      border-color: #ced4da;
      padding: 5px 0;

      .vti__dropdown {
        .vti__dropdown-arrow {
          right: 18px;
        }

        .vti__country-code {
          font-size: 1rem;
        }

        .vti__dropdown-list {
          width: 100%;
          min-height: 200px !important;
          z-index: 100;
        }
      }

      .vti__input.vti__search_box {
        width: 92%;
        margin-left: 20px;
      }

      &:focus-within {
        border-color: #80bdff;
        box-shadow: none;
      }
    }
}
</style>
