<template>
  <b-navbar id="PageHeaderOrder" toggleable="md">
    <div class="container-xl p-md-0">
      <b-navbar-brand class="m-0">
        <div class="site-logo">
          <img
            v-if="storeInfo && storeInfo.logo_url"
            :src="$imgUrl(storeInfo.logo_url,'logo')"
            :onerror="`this.onerror=null;this.src='${$config.publicPath}/images/logo-placeholder.png';`"
            :alt="storeInfo.name"
            loading="lazy"
          >
          <span v-else>
            {{ storeInfo.name }}
          </span>
        </div>
      </b-navbar-brand>
    </div>
  </b-navbar>
</template>

<script>
import headerMixin from '~/mixins/header'

export default {
  mixins: [headerMixin]
}

</script>

<style lang="scss">
#PageHeaderOrder {
  z-index: 1;
  transition: all 0.5s ease;
  min-height: 80px;
  background: white;
  .site-logo {
    font-family: 'Keep Calm Regular';
    img {
      max-height: 35px;
    }
  }
}

</style>
