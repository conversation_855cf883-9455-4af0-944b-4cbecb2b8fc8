<template>
  <div>
    <div class="row justify-content-between align-items-center cursor-pointer" @click="visible = !visible">
      <div class="col">
        {{ faq.question }}
      </div>
      <b-button variant="outline-custom" class="border col-auto collapse-icon">
        <i :class="visible ? 'icon-sen-minus': 'icon-sen-plus'" />
      </b-button>
    </div>
    <b-collapse :id="`collapse-${faq.id}`" v-model="visible" class="mt-2">
      <div v-html="faq.answer" />
    </b-collapse>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['faq'],
  data () {
    return {
      visible: false
    }
  }
}
</script>
