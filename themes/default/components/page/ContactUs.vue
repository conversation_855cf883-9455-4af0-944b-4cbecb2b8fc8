<template>
  <div>
    <b-form class="mb-3" @submit.prevent="handleSubmit">
      <!-- name -->
      <b-form-input
        id="name"
        v-model="contactForm.fullName"
        size="lg"
        type="text"
        :placeholder="$t('Your full name')"
        autofocus
      />
      <!-- email -->
      <b-form-input
        id="email"
        v-model="contactForm.email"
        size="lg"
        type="email"
        :state="submitted ? !$v.contactForm.email.$error : null"
        :placeholder="$t('Your email')"
        class="mt-3"
      />
      <small>
        ({{ $t('contact_us_text-2') }})
      </small>

      <!-- type -->
      <b-dropdown
        id="dropdown-type"
        size="lg"
        variant="outline-custom"
        block
        :right="true"
        :text="$t(contactForm.type) || $t('Subject')"
        class="border mt-3 border-radius-none"
        toggle-class="text-left text-overflow-hidden"
        menu-class="w-100"
      >
        <b-dropdown-item
          v-for="(item, index) in optionsSelect"
          :key="index"
          :active="contactForm.type===item"
          @click="contactForm.type = item"
        >
          {{ $t(item) }}
        </b-dropdown-item>
      </b-dropdown>

      <!-- order number -->
      <b-form-input
        id="order"
        v-model="contactForm.order"
        size="lg"
        type="text"
        class="mt-3"
        :placeholder="$t('Order number')"
      />

      <small v-if="lastOrder">
        ({{ $t('get your order number in your email confirmation') }} {{ $t('or') }} <a :href="trackLastOrderUrl" target="_blank" rel="noopener noreferrer" v-text="$t('view last order status')" />)
      </small>

      <small v-else>
        ({{ $t('get your order number in your email confirmation') }} {{ $t('and') }} <a :href="trackLastOrderUrl" target="_blank" rel="noopener noreferrer" v-text="$t('track your order here')" />)
      </small>

      <b-form-invalid-feedback v-if="submitted" id="email-feedback">
        <span v-if="!$v.contactForm.email.required">{{ $t('Email is required') }}</span>
        <span v-if="!$v.contactForm.email.email">{{ $t('Email is invalid') }}</span>
      </b-form-invalid-feedback>

      <!-- message -->
      <b-form-textarea
        id="message"
        v-model="contactForm.message"
        :placeholder="$t('Your message')"
        rows="4"
        size="lg"
        :state="submitted ? !$v.contactForm.message.$error : null"
        max-rows="6"
        class="mt-3"
      />
      <b-form-invalid-feedback v-if="submitted" id="message-feedback">
        <span v-if="!$v.contactForm.message.required">{{ $t('Message is required') }} </span>
        <span v-else-if="!$v.contactForm.message.checkMessage">{{ $t('Message must have at least 10 letters') }}.</span>
      </b-form-invalid-feedback>

      <!-- file -->
      <p class="mb-0 mt-3">
        <span class="font-weight-500">{{ $t('File Attachment') }} </span>
        <span>({{ $t('limit 5 file') }})</span>
      </p>
      <b-form-file
        v-model="fileUpload"
        accept="image/*"
        :multiple="true"
        :placeholder="$t('No file chosen')"
        :browse-text="$t('Choose file')"
        @change="updateFile"
      />
      <small>
        ({{ $t('contact_us_text-3') }})
      </small>

      <recaptcha class="mt-3" />

      <b-button
        :disabled="isFileLoading"
        type="submit"
        variant="custom"
        size="lg"
        class="mt-3 text-capitalize"
        block
      >
        <b-spinner v-show="isFileLoading" class="float-right" /> {{ $t('Send over') }}
      </b-button>
    </b-form>
    <div v-if="!(successForm||warningForm)" style="font-size: 12px;">
      <p><span>{{ $t('Please do not send mutiple requests on the same issue.') }}</span></p>
      <p><span>{{ $t('Thank you for your patience! You will receive a reply from us within 1 business day.') }}</span></p>
    </div>
    <b-alert
      variant="success"
      dismissible
      fade
      :show="successForm"
      class="mt-3"
      @dismissed="successForm=false"
    >
      <strong>{{ $t('Success') }}!</strong> {{ $t('You will receive a reply from us within 1 business day') }}.
    </b-alert>
    <b-alert
      ref="warningMessage"
      variant="warning"
      dismissible
      fade
      :show="warningForm"
      @dismissed="warningForm=false"
    >
      <strong>{{ $t('Error') }}!</strong> {{ message }}
    </b-alert>
  </div>
</template>

<script>
import contactUsMixin from '~/mixins/contactUs'

export default {
  mixins: [contactUsMixin]
}
</script>

<style lang="scss">
  .page-contact a {
    color: var(--primary-color);
  }
</style>
