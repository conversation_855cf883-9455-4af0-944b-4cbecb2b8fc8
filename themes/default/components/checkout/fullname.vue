<script>
export default {
  name: 'Fullname'
}
</script>

<template>
  <b-form-group
    label-class="text-overflow-hidden"
    class="inner-label-input"
    :class="{'show':userInfo.name}"
    :label="`${$t('Full name')} *`"
  >
    <b-form-input
      id="name"
      ref="name"
      v-model="userInfo.name"
      type="text"
      data-test-id="checkout-name"
      name="name"
      autocomplete="name"
      :state="submitted ? !$v.userInfo.name.$error : null"
    />
    <b-form-invalid-feedback id="name-feedback">
      <span v-if="!$v.userInfo.name.required">{{ $t('Full name is required') }}</span>
      <span v-else-if="!$v.userInfo.name.separateName">{{ $t('Please enter correct first and last name') }}</span>
    </b-form-invalid-feedback>
  </b-form-group>
</template>
