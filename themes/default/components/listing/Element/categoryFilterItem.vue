<template>
  <div class="filter-category-item">
    <div class="d-flex justify-content-between align-items-center">
      <nuxt-link :to="getUrl(category.slug)" class="btn pl-0 d-flex align-items-center" :class="{checked:currentCategory===category.slug}">
        <i :class="currentCategory===category.slug?'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank'" /><span class="ml-2">{{ category.name }}</span>
      </nuxt-link>
      <i v-if="category.child_menu && category.child_menu.length" class="btn" :class="isShowChildCategory?'icon-sen-minus':'icon-sen-plus'" @click="toggle" />
    </div>
    <div
      v-show="isShowChildCategory"
      class="sub-categories pl-3"
    >
      <div v-for="(sub_category, index) in category.child_menu" :key="index" class="filter-category-item">
        <nuxt-link :to="getUrl(sub_category.slug)" class="btn" :class="{checked:currentCategory===sub_category.slug}">
          <i :class="currentCategory===sub_category.slug?'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank'" /><span class="ml-2">{{ sub_category.name }}</span>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['category', 'currentCategory', 'pageType'],
  data () {
    return {
      isShowChildCategory: false
    }
  },
  computed: {
    getUrl
  },
  watch: {
    category: {
      deep: true,
      handler: resetData
    },
    currentCategory: resetData
  },
  mounted: resetData,
  methods: {
    toggle
  }
}

function toggle () {
  this.isShowChildCategory = !this.isShowChildCategory
}

function resetData () {
  this.isShowChildCategory = this.category.child_menu.find(el => el.slug === this.currentCategory)
}

function getUrl () {
  return (categorySlug) => {
    const query = { ...this.$route.query }
    query.page = 1
    if (this.pageType === 'category') {
      if (this.currentCategory === categorySlug) {
        delete query.sub_cat
      } else {
        query.sub_cat = categorySlug
      }
    } else if (this.currentCategory === categorySlug) {
      delete query.category
    } else {
      query.category = categorySlug
    }
    return this.localePath({ path: this.$route.currentPath, query })
  }
}
</script>

<style lang="scss">
.filter-category-item{
  .btn{
    color: #C4C4C4;
    &:hover{
      color: #363636;
    }
    &:focus{
      box-shadow: none;
    }
    &.checked{
      color: var(--primary-color);
    }
  }
  i {
    font-size: 1.25rem;
  }
}
</style>
