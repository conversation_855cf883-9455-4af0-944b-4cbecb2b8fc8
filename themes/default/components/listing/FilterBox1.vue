<template>
  <div>
    <!-- Category Filter -->
    <div v-if="categories && categories.length > 0" class="category-filter">
      <div v-if="showClearFilterButton" class="position-absolute top-0 right-0 mr-3">
        <nuxt-link
          class="badge badge-pill badge-success"
          :to="localePath({
            path: $route.currentPath,
            query:{
              ...$route.query,
              ...{
                price: null,
                sub_cat: null,
                category: null,
                product: null,
                color: null
              }}})"
        >
          {{ $t('Clear Filters') }}
        </nuxt-link>
      </div>
      <p class="h-5 font-12x mb-3 pb-3 border-bottom font-weight-500 text-uppercase">
        {{ $t('Explorer') }}
      </p>
      <div class="categories text-blur mb-8">
        <category-filter-item
          v-for="(category, index) in categories"
          :key="index"
          :category="category"
          :current-category="currentCategory"
          :page-type="pageType"
        />
      </div>
    </div>
    <div v-if="templateFilterList && templateFilterList.length > 0" class="template-filter">
      <p class="h-5 font-12x mt-4 mb-3 pb-3 border-bottom font-weight-500 text-uppercase">
        {{ $t('Product') }}
      </p>
      <div class="templates text-blur mb-8 overflow-auto" style="max-height: 230px">
        <div v-for="(template, tIndex) in templateFilterList" :key="tIndex" class="filter-category-item">
          <div class="d-flex justify-content-between align-items-center">
            <nuxt-link
              :to="getUrl('product', template.id !== parseInt(currentTemplate) ? template.id : null)"
              class="btn pl-0 d-flex align-items-center"
              :class="{ checked : template.id === parseInt(currentTemplate) }"
            >
              <i
                :class="template.id === parseInt(currentTemplate) ? 'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank'"
              />
              <span class="ml-2 max-line-1 text-left">{{ template.name }}</span>
            </nuxt-link>
          </div>
        </div>
      </div>
    </div>
    <!-- Price Filter -->
    <div class="filter-price mt-3">
      <p class="h-5 font-12x mb-4 pb-3 border-bottom font-weight-500 text-uppercase">
        {{ $t('Price') }}
      </p>
      <p class="text-center  text-555 mb-3">
        {{ $formatPrice(value[0]) }} - {{ $formatPrice(value[1]) }}
      </p>
      <client-only>
        <vue-slider
          v-model="value"
          class="price-slider"
          tooltip="none"
          :max="maxPrice"
          :min="minPrice"
          :lazy="true"
          @change="updateFilterPrice"
        />
      </client-only>
    </div>
    <!-- Color Filter -->
    <div v-if="filter && filter.color_group && filter.color_group.length > 0" class="filter-color mt-3">
      <p class="h-5 font-12x mb-4 pb-3 border-bottom font-weight-500 text-uppercase">
        {{ $t('Color') }}
      </p>
      <div class="d-flex flex-wrap">
        <nuxt-link
          v-for="(color, index) in filter.color_group"
          :key="index"
          :to="getUrl('color', currentColor===color? '': color.replace(/ /g, '-'))"
        >
          <color-item
            :color="color"
            :active="currentColor===color"
          />
        </nuxt-link>
      </div>
    </div>
  </div>
</template>
<script>
import categoryFilterItem from './Element/categoryFilterItem'
import ColorItem from '~/themes/default/components/common/ColorItem'

export default {
  components: {
    categoryFilterItem,
    ColorItem
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['pageType', 'filter', 'maxPrice', 'minPrice', 'categories', 'currentCategory', 'currentMinPrice', 'currentMaxPrice', 'currentColor', 'currentTemplate', 'templateFilterList', 'generalTemplates'],
  data () {
    return {
      value: [this.currentMinPrice, this.currentMaxPrice],
      selectedTemplate: []
    }
  },
  computed: {
    showClearFilterButton () {
      return this.currentCategory || this.currentTemplate || this.currentColor || this.value.join('-') !== [this.minPrice, this.maxPrice].join('-')
    },
    getUrl
  },
  watch: {
    currentMinPrice: resetData,
    currentMaxPrice: resetData
  },
  methods: {
    updateFilterPrice
  }
}

function resetData () {
  this.value = [this.currentMinPrice, this.currentMaxPrice]
}

function updateFilterPrice (value, index) {
  this.$router.push(getUrl.call(this)('price', value.join('-')))
}

function getUrl () {
  return (key, value) => {
    const query = { ...this.$route.query }
    query.page = 1
    let newPath = this.$route.currentPath
    if (key === 'product' && this.pageType === 'tag' && value) {
      const selectTemplate = this.generalTemplates.find(item => item.id === Number(value))
      const keyWord = this.$route.path.split('-')
      newPath = [keyWord[0], selectTemplate.name.replace(/\s/g, '_')].join('-')
      delete query.product
    } else if (value) {
      query[key] = value
    } else {
      delete query[key]
    }
    return this.localePath({ path: newPath, query })
  }
}
</script>

<style lang="scss">
.collection-filters{
  color: #C4C4C4;
}

.price-slider {

  .vue-slider-process {
    background-color: var(--primary-color) !important;
  }

  .vue-slider-dot-handle {
    border:2px solid var(--primary-color) !important;
  }
}

.max-line-1 {
  display: -webkit-box !important;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
</style>
