<template>
  <div class="row">
    <div class="col-12 col-lg-8 mb-0 d-flex flex-wrap justify-content-center justify-content-lg-start align-items-center text-center text-md-left">
      <h1 class="mb-0 d-block h4 font-weight-bold text-overflow-hidden" :title="title">
        {{ title }}
      </h1>
      <div class="ml-2">
        {{ totalCampaign || 0 }} {{ $t('result') }}
      </div>
    </div>
    <div class="col-12 col-lg-4 d-flex justify-content-center justify-content-lg-end align-items-center">
      <span>
        {{ $t('Sort by') }}:
      </span>
      <b-dropdown right :text="$t(currentFilterItem)" variant="light" toggle-class="pr-4">
        <b-dropdown-item
          v-if="pageType==='collectionSlug'"
          :to="localePath({ path: $route.currentPath, query: { ...$route.query, ...{sort: 'featured'} }})"
          :active="currentSortType==='featured'"
          @click="$lscache.set('lastSortType', 'featured')"
        >
          {{ $t('Featured') }}
        </b-dropdown-item>
        <b-dropdown-item
          v-if="pageType==='search' || pageType==='tag'"
          :to="localePath({ path: $route.currentPath, query: { ...$route.query, ...{sort: 'relevant'} }})"
          :active="currentSortType==='relevant'"
          @click="$lscache.set('lastSortType', 'relevant')"
        >
          {{ $t('Relevant') }}
        </b-dropdown-item>
        <b-dropdown-item
          v-for="(filterItem, index) in filterItems"
          :key="index"
          :to="localePath({ path: $route.currentPath, query: { ...$route.query, ...{sort: filterItem.value} }})"
          :active="currentSortType===filterItem.value"
          @click="$lscache.set('lastSortType', filterItem.value)"
        >
          {{ $t(filterItem.text) }}
        </b-dropdown-item>
      </b-dropdown>
    </div>
    <div v-if="pageType !== 'artist' && filter && filter.collection_group && filter.collection_group.length > 0 && (!$store.state.storeInfo || !$store.state.storeInfo.disable_related_collection)" class="col-12 filter-color mt-1 d-flex align-items-center overflow-auto">
      <span class="min-w-max-content d-none d-md-block">{{ $t('Related collections') }}: </span>
      <collection-item
        v-for="(collection, index) in filter.collection_group"
        :key="index"
        :value="collection.name"
        :slug="currentCollection===collection.slug ? '/collection': `/collection/${collection.slug}`"
        :active="currentCollection===collection.slug"
      />
    </div>
  </div>
</template>
<script>
import CollectionItem from '~/themes/default/components/common/CollectionItem'
export default {
  components: {
    CollectionItem
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['title', 'currentSortType', 'totalCampaign', 'pageType', 'filter', 'currentCollection'],
  data () {
    return {
      filterItems
    }
  },
  computed: {
    currentFilterItem
  }
}

const filterItems = [{
  text: 'Popular',
  value: 'popular'
}, {
  text: 'Newest',
  value: 'newest'
}, {
  text: 'Price: High To Low',
  value: 'highest_price'
}, {
  text: 'Price: Low To High',
  value: 'lowest_price'
}, {
  text: 'Alphabet, A-Z',
  value: 'a_z'
}, {
  text: 'Alphabet, Z-A',
  value: 'z_a'
}]

function currentFilterItem () {
  if (this.currentSortType === 'featured') { return 'Featured' }
  if (this.currentSortType === 'relevant') { return 'Relevant' }
  return (this.filterItems.find(item => item.value === this.currentSortType) || this.filterItems[0]).text
}
</script>
