<template>
  <b-modal
    id="modalSelectSize"
    v-model="isShowModalSelectSize"
    centered
    hide-footer
    size="lg"
    title-class="text-center w-100"
    :title="$t('Please choose a size')"
  >
    <div class="px-5 py-lg-3 py-1">
      <!-- <img :src="`${$config.publicPath}/images/selectSize.gif`" alt="size guide" class="size-select-img mb-3"> -->
      <div class="w-100 w-lg-50 mx-auto size-group">
        <div>
          <ul v-if="sizeGuideList && sizeGuideList.length" class="list-group">
            <li v-for="(sizeguide, productIndex) in sizeGuideList" :key="productIndex" class="list-group-item cursor-pointer size-item" :class="{'active': false}" @click="$emit('selectSize', sizeguide.size.toLowerCase()); isShowModalSelectSize = false">
              {{ sizeguide.size }} - {{ $t('Chest') }} {{ sizeguide.width }} in
            </li>
          </ul>
          <ul v-else-if="optionList && optionList.size" class="list-group">
            <li v-for="(size, productIndex) in optionList.size" :key="productIndex" class="list-group-item cursor-pointer" :class="{'active': false}" @click="$emit('selectSize', size); isShowModalSelectSize = false">
              {{ size }}
            </li>
          </ul>
        </div>
      </div>
      <div v-if="sizeGuideList && sizeGuideList.length" class="w-100 w-lg-50 mt-3 text-center mx-auto" @click="$emit('openSizeGuild')">
        <strong
          v-if="sizeGuideList && sizeGuideList.length"
          class="cursor-pointer text-info"
        >{{ $t('Size guide') }}</strong>
        <span>
          <i class="icon-sen-ruler ml-1 text-info" />
        </span>
      </div>
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'ModalSelectSize',
  // eslint-disable-next-line vue/require-prop-types
  props: ['sizeGuideList', 'optionList'],
  data () {
    return {
      isShowModalSelectSize: false
    }
  }
}

</script>

<style scoped lang="scss">
.size-item:hover {
  color: white;
  background-color: var(--primary-color);
}
.size-group {
  overflow-y: auto;
  @media (min-width: 992px) {
    max-height: 50vh;
  }
  @media (max-width: 991px) {
    max-height: 70vh;
  }
}
</style>
