<template>
  <b-modal
    id="modalConfirmDesign"
    v-model="isShowModalConfirmDesign"
    centered
    hide-footer
    hide-header
    size="xl"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalConfirmDesign', null)"
    @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalConfirmDesign', null)"
  >
    <span class="close-icon" @click="isShowModalConfirmDesign = false"><i class="icon-sen-close-circle" /></span>
    <div class="row p-0 overflow-auto">
      <div class="col-12 col-lg-7">
        <custom-carousel
          v-if="(imgUrls && imgUrls.length > 1)"
          ref="carousel"
          carousel-classes="campaign-carousel-images"
        >
          <div v-for="( image, index ) in imgUrls" :key="index" class="thumb">
            <img :src="image" loading="lazy">
          </div>
        </custom-carousel>
        <div v-else-if="imgUrls && imgUrls[0]" class="thumb">
          <img :src="imgUrls && imgUrls[0]" loading="lazy">
        </div>
      </div>
      <div class="col-12 col-lg-5 d-flex flex-wrap align-content-center pt-4">
        <b-form-checkbox
          id="checkbox-1"
          v-model="confirm"
          class="w-100"
          name="checkbox-1"
        >
          <div>
            {{ $t('I have reviewed my design and confirm that I have entered the text correctly') }}.
          </div>
        </b-form-checkbox>
        <b-button
          block
          size="lg"
          variant="custom"
          :disabled="isLoadingAddToCart || !confirm"
          class="w-100 text-uppercase font-weight-600 border-radius-none mt-4"
          @click="$emit('addToCart', null, true); isShowModalConfirmDesign = false"
        >
          <loading-dot v-if="isLoadingAddToCart" />
          <span v-else>{{ $t('Approve Design') }}</span>
        </b-button>
      </div>
    </div>
  </b-modal>
</template>

<script>
import CustomCarousel from '~/themes/default/components/common/CustomCarousel.vue'
export default {
  name: 'ModalConfirmDesign',
  components: {
    CustomCarousel
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['imgUrls', 'isLoadingAddToCart'],
  data () {
    return {
      isShowModalConfirmDesign: false,
      confirm: true
    }
  }
}

</script>

<style scoped lang="scss">
  .thumb {
    img {
      width: 100%;
      user-select: none;
      -moz-user-select: none;
      -webkit-user-drag: none;
      -webkit-user-select: none;
      -ms-user-select: none;
      user-drag: none;
    }
  }

  .close-icon {
    cursor: pointer;
    position: absolute;
    right: 0.5rem;
    top: 0rem;
    font-size: 2rem;
    z-index: 100;
  }
</style>
