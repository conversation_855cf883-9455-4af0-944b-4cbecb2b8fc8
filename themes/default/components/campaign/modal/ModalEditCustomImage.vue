<template>
  <b-modal
    v-model="isShowModal"
    centered
    hide-footer
    size="lg"
    title-class="text-custom text-center"
    :title="$t('Edit design')"
    @shown="isShown = true"
    @hidden="isShown = false; design.canvas._onupdate(); isImageLowQuanlity ? $emit('openConfirmQuantityImage'): ''"
  >
    <div v-if="design" class="mb-5">
      <div v-if="isShown" class="thumb-canvas d-flex justify-content-center">
        <design-item
          :canvas="design.canvas"
          :custom-image="customImage"
        />
      </div>
      <div class="position-absolute bottom-0 left-0 w-100 bg-white p-1 border d-flex flex-wrap">
        <div class="w-100">
          <b-button
            block
            size="lg"
            variant="custom"
            class="border text-uppercase font-weight-600"
            @click="isShowModal = false"
          >
            {{ $t('Save') }}
          </b-button>
        </div>
      </div>
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'ModalEditCustomImage',
  // eslint-disable-next-line vue/require-prop-types
  props: ['design', 'customImage', 'isImageLowQuanlity'],
  data () {
    return {
      isShown: false,
      isShowModal: false
    }
  }
}

</script>

<style scoped lang="scss">
.thumb-canvas {
  width: 100%;
}
</style>
