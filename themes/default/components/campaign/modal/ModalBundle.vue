<template>
  <b-modal
    v-model="isShowModalBundle"
    centered
    hide-footer
    title-class="text-custom text-center"
    header-class="py-2 border-none"
    :title="currentProduct && currentProduct.name || ''"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalBundle', null)"
    @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalBundle', null)"
  >
    <template #modal-header-close>
      <span class="close-icon"><i class="icon-sen-close-circle" /></span>
    </template>

    <div v-if="currentProduct" class="mb-5">
      <div class="bundle-modal-image d-flex justify-content-center">
        <product-img
          :path="currentProduct.thumb_url"
          :alt="currentProduct.name"
          type="full_hd"
          :color="currentOptions.color ? $colorVal(currentOptions.color.replace(/-/g, ' ')) : ''"
        />
      </div>

      <option-list
        v-if="currentProduct.options"
        class="px-5"
        :option-list="currentProduct.options"
        :current-options="currentOptions"
        :current-product="currentProduct"
        @updateOption="(option)=> {Object.assign(currentOptions , option); saveBundleDiscountOption()}"
        @openSizeGuild="openSizeGuild(currentProduct)"
      />

      <div class="position-absolute bottom-0 left-0 w-100 bg-white p-1 border d-flex flex-wrap">
        <div class="w-100">
          <b-button
            :disabled="!!(currentProduct.currentVariant && currentProduct.currentVariant.out_of_stock)"
            block
            size="lg"
            variant="custom"
            class="border text-uppercase font-weight-600"
            @click="isShowModalBundle = false"
          >
            {{ $t('Save') }}
          </b-button>
        </div>
      </div>
    </div>
  </b-modal>
</template>

<script>
import modalBundleProductMixin from '~/mixins/modalBundleProduct'
import OptionList from '~/themes/default/components/campaign/OptionList'
export default {
  name: 'ModalBundleProduct',
  components: {
    OptionList
  },
  mixins: [modalBundleProductMixin]

}

</script>

<style scoped lang="scss">
</style>
