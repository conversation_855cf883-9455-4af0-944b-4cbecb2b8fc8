<template>
  <b-modal
    v-model="isShowModalAddToCart"
    centered
    hide-footer
    size="xl"
    title-class="text-custom text-center"
    :title="$t('Added to cart successfully!')"
    data-test-id="modal-add-item-success"
    @shown="$tracking.newCustomTracking('modal-shown', null, 'modalAddToCart', null)"
    @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalAddToCart', null)"
  >
    <div class="d-flex flex-wrap">
      <div class="col-12 col-lg-8 d-flex flex-wrap align-items-center">
        <div class="col-auto product-add-to-cart-item">
          <img
            v-if="uploadFileDesignUrl && uploadFileDesignUrl[0]"
            :src="uploadFileDesignUrl[0]"
            alt="SenPrints"
          >
          <product-img v-else :path="isShowCampaignThumbnailInsteadOfProductComputed ? filterImages[0].file_url : currentProduct.thumb_url || filterImages[0]&&filterImages[0].file_url" :color="color" :alt="title" />
        </div>
        <div class="col text-overflow-hidden">
          <h5 class="text-overflow-hidden">
            {{ title }}
          </h5>
          <div>
            <span>{{ currentProduct && currentProduct.name }}</span>
            <template v-for="(item, index) in Object.keys(optionList)">
              <span v-if="!currentOptions[item]?.startsWith('__')" :key="index"> / {{ currentOptions[item] }}</span>
            </template>
          </div>
          <div>Qlt: {{ quantity }}</div>
        </div>
      </div>
      <div class="col-12 col-lg-4 d-flex flex-column justify-content-center">
        <b-button
          variant="outline-custom"
          block
          class="border viewcart-button"
          data-test-id="view-cart-button"
          dusk="view-cart-button"
          @click="isShowModalAddToCart = false; $router.push(localePath('/cart'))"
        >
          {{ $t('View cart') }} ({{ cartTotalQuantity }})
        </b-button>
        <b-button
          :disabled="isLoadingCheckout || isLoadingUploadImage"
          variant="custom"
          block
          @click="isShowModalAddToCart = false; $store.dispatch('order/createOrder')"
        >
          <loading-dot v-if="isLoadingCheckout || isLoadingUploadImage" variant="light" />
          <span v-else>{{ $t('Proceed to checkout') }}</span>
        </b-button>
      </div>
      <div v-if="relatedCart && relatedCart.length" class="col-12">
        <h5 class="my-3 text-center text-uppercase">
          {{ $t('Frequently bought together') }}
        </h5>
        <carousel-campaign :campaigns="relatedCart" />
      </div>
    </div>
  </b-modal>
</template>

<script>
import CarouselCampaign from '~/themes/default/components/products/CarouselCampaign'

export default {
  name: 'ModalAddToCart',
  components: {
    CarouselCampaign,
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['filterImages', 'relatedCart', 'currentProduct', 'currentOptions', 'quantity', 'cartTotalQuantity', 'optionList', 'title', 'uploadFileDesignUrl', 'isLoadingUploadImage', 'campaign'],

  data () {
    return {
      isShowModalAddToCart: false
    }
  },
  computed: {
    color,
    isLoadingCheckout,
    isShowCampaignThumbnailInsteadOfProductComputed () {
      return this.campaign?.system_type === 'custom'
    }
  }
}

function color () {
  if (this.currentOptions && this.currentOptions.color) {
    return this.$colorVal(this.currentOptions.color.replace(/-/g, ' '))
  }
  return ''
}

function isLoadingCheckout () {
  return this.$store.state.order.isLoadingCheckout
}
</script>

<style lang="scss">
@media (min-width: 768px) {
  .product-add-to-cart-item {
    img {
      height: 120px;
      width: 96px;
    }
  }
}
</style>
