<template>
  <b-modal
    id="modalBundleCustom"
    v-model="isShowModalBundle"
    centered
    hide-footer
    size="lg"
    title-class="h5 text-overflow-hidden"
    header-class="py-2 border-none"
    :title="currentProduct && currentProduct.name || ''"
    @shown="isShown = true; $tracking.newCustomTracking('modal-shown', null, 'modalBundleCustom', null)"
    @hidden="isShown = false; $tracking.newCustomTracking('modal-hiden', null, 'modalBundleCustom', null)"
  >
    <template #modal-header-close>
      <span class="close-icon"><i class="icon-sen-close-circle" /></span>
    </template>
    <div class="row overflow-auto h-100 pb-5 mb-lg-0">
      <images-box
        ref="imageBox"
        class=" col-12 col-lg-6"
        :is-shown="isShown"
        :current-product="currentProduct"
        :campaign="currentProduct"
        :option-list="currentProduct.options"
        :is-modal="true"
        :current-options="currentOptions"
        :current-design="currentDesign"
        :filter-designs="filterDesigns"
        :is-show-design="isShowDesign"
        :custom-image="customImage"
        @updateShowDesign="updateShowDesign"
      />
      <div class="col-12 col-lg-6 info-box">
        <option-list
          v-if="currentProduct.options"
          :option-list="currentProduct.options"
          :current-options="currentOptions"
          :current-product="currentProduct"
          @updateOption="(option)=> {Object.assign(currentOptions , option); saveBundleDiscountOption()}"
          @openSizeGuild="openSizeGuild(currentProduct)"
        />

        <personalize-custom-option
          v-if="currentProduct && currentProduct.personalized === 0 && currentProduct.template_custom_options"
          :campaign="campaignData"
          :is-modal="true"
          :current-product="currentProduct"
          :current-options="currentOptions"
          :custom-options="currentProduct.template_custom_options"
          :common-options="currentProduct.common_options"
          :is-loaded-custom-option="true"
          @updateTempCustomOption="value => {currentProduct.tempCustomOption = value; tempCustomOption = value}"
          @updateTempCommonOption="value => {currentProduct.tempCommonOption = value; tempCommonOption = value}"
          @updateCustomizeOptions="value => {currentProduct.customerCustomOptions = value; customerCustomOptions = value; $emit('updateBundleProduct', currentProduct, { customerCustomOptions: value, customOptionGroupNumbers })}"
          @updateCustomOptionGroupFee="updateCustomOptionPrice"
          @isLoadingAddToCart="value => isLoadingAddToCart = value"
          @updateCustomOptionGroupNumbers="value =>{customOptionGroupNumbers = value; $emit('updateBundleProduct', currentProduct, { customOptionGroupNumbers: value, customerCustomOptions })}"
        />

        <personalize-custom
          v-if="currentProduct.personalized === 1"
          :is-modal="true"
          class="d-none d-lg-block"
          :option-error="optionError"

          :custom-text-list="customTextList"
          :custom-image-list="customImageList"
          :file-upload="fileUpload"
          :is-upload-file="isUploadFile"

          :custom-design-array="customDesignArray"
          :custom-design-type="customDesignType"
          :custom-design="customDesign"
          :loading-change-custom-design="loadingChangeCustomDesign"

          @changeCurrentDesign="changeCurrentDesign"
          @updateCustomDesign="updateCustomDesign"
          @updateCanvasData="updateCanvasData"
          @editDesign="$refs.modalEditCustomImage.isShowModal = true"
        />
        <personalize-pb
          v-if="currentProduct.personalized === 2"
          :campaign="currentProduct"
          :current-design="currentDesign"
          :is-modal="true"
          @scrollToImage="scrollToImage"
          @changeCurrentDesign="changeCurrentDesign"
        />

        <personalize-custom-option
          v-if="currentProduct.personalized === 3 && currentProduct.options"
          :campaign="campaignData"
          :is-modal="true"
          :temp-custom-option="currentProduct.tempCustomOption"
          :temp-common-option="currentProduct.tempCommonOption"
          :custom-option-group-numbers="currentProduct.customOptionGroupNumbers"
          :custom-options="customOptions"
          :common-options="commonOptions"
          :is-loaded-custom-option="true"
          @updateTempCustomOption="value => {currentProduct.tempCustomOption = value; tempCustomOption = value}"
          @updateTempCommonOption="value => {currentProduct.tempCommonOption = value; tempCommonOption = value}"
          @updateCustomizeOptions="value => {currentProduct.customerCustomOptions = value; customerCustomOptions = value; $emit('updateBundleProduct', currentProduct, { customerCustomOptions: value, customOptionGroupNumbers })}"
          @updateCustomOptionGroupFee="updateCustomOptionPrice"
          @isLoadingAddToCart="value => isLoadingAddToCart = value"
          @updateCustomOptionGroupNumbers="value => {customOptionGroupNumbers = value; $emit('updateBundleProduct', currentProduct, { customOptionGroupNumbers: value, customerCustomOptions })}"
        />
      </div>

      <div ref="bottomButton" class="bottom-button position-absolute bottom-0 w-100 bg-white p-1">
        <!-- custom text -->
        <personalize-custom-bottom-input
          v-if="campaignData.personalized === 1 && selectedCustom"
          class="d-lg-none"
          :is-modal="true"
          :option-error="optionError"
          :selected-custom="selectedCustom"
          :custom-design-type="customDesignType"
          :custom-item-list="customItemList"
          :custom-design-array="customDesignArray"
          :custom-design="customDesign"
          :loading-change-custom-design="loadingChangeCustomDesign"
          :is-upload-file="isUploadFile"
          :file-upload="fileUpload"

          @updateSelectedCustom="value=> selectedCustom = value"
          @changeCurrentDesign="changeCurrentDesign"
          @updateDesignQuery="updateDesignQuery"
          @updateCustomDesign="updateCustomDesign"
          @updateCanvasData="updateCanvasData"
          @editDesign="$refs.modalEditCustomImage.isShowModal = true"
        />
        <b-button
          :disabled="isUploadFile || !!(currentProduct.currentVariant && currentProduct.currentVariant.out_of_stock)"
          block
          size="lg"
          variant="custom"
          class="border text-uppercase font-weight-600"
          @click="saveBundleCustom"
        >
          <b-spinner v-if="isUploadFile" small />
          {{ $t('Save') }}
        </b-button>
      </div>
    </div>

    <modal-edit-custom-image
      ref="modalEditCustomImage"
      :design="currentDesign"
      :custom-image="customImage"
      :is-image-low-quanlity="isImageLowQuanlity"
      @openConfirmQuantityImage="isConfirmQuantityImage? '' : isShowModalConfirmQuantityImage = true"
    />

    <b-modal
      v-model="isShowModalConfirmQuantityImage"
      centered
      size="lg"
      body-class="d-flex flex-wrap justify-content-center"
      hide-header
      hide-footer
      @shown="$tracking.newCustomTracking('modal-shown', null, 'modalConfirmQuantityImage', null)"
      @hidden="$tracking.newCustomTracking('modal-hiden', null, 'modalConfirmQuantityImage', null)"
    >
      <div class="col-12 py-2 text-center">
        <h4>{{ $t('Image resolution is low that cause bad print quality.') }}</h4>
        <p>{{ $t('Do you want to upload higher resolution image (minimum size is heightxwidth px)?', {height: Math.round(customImage && customImage.height * customImage.scaleY / 2) , width: Math.round(customImage && customImage.width * customImage.scaleX / 2)}) }}</p>
      </div>
      <div class="col-12 col-md-6">
        <b-button
          block
          variant="primary"
          class="border-radius-none mb-3"
          @click="confirmQuantityImage(true)"
        >
          {{ $t('Upload new image') }}
        </b-button>
      </div>
      <div class="m-0 col-12 col-md-6">
        <b-button
          block
          variant="secondary"
          class="border-radius-none mb-3"
          @click="confirmQuantityImage(false)"
        >
          {{ $t('Keep old image') }}
        </b-button>
      </div>
    </b-modal>
  </b-modal>
</template>

<script>
import productReviewMixin from '~/mixins/productReview'
import modalBundleProductMixin from '~/mixins/modalBundleProduct'

import campaignCustom from '~~/mixins/campaignCustom'
import ImagesBox from '~/themes/default/components/campaign/ImagesBox'
import OptionList from '~/themes/default/components/campaign/OptionList'
import PersonalizeCustom from '~/themes/default/components/campaign/PersonalizeCustom'
import PersonalizePb from '~/themes/default/components/campaign/PersonalizePb'
import PersonalizeCustomOption from '~/themes/default/components/campaign/PersonalizeCustomOption'
import PersonalizeCustomBottomInput from '~/themes/default/components/campaign/PersonalizeCustom/BottomInput'

import ModalEditCustomImage from '~/themes/default/components/campaign/modal/ModalEditCustomImage'
import { isMissingRequiredOption } from '~/helpers/campaign'

export default {
  name: 'ModalBundleCustom',
  components: {
    ImagesBox,
    OptionList,
    PersonalizeCustom,
    PersonalizePb,
    PersonalizeCustomOption,
    ModalEditCustomImage,
    PersonalizeCustomBottomInput
  },
  mixins: [modalBundleProductMixin, campaignCustom, productReviewMixin],
  // eslint-disable-next-line vue/require-prop-types
  props: ['customOptions', 'commonOptions'],
  data () {
    return {
      isShown: false,
      isCustom: true,
      forceUpdate: 0,
      optionError: ''
    }
  },
  head,
  watch: {
    currentProduct: {
      handler () {
        this.resetData()
      },
      deep: true
    },
    isShown: resetData,
    customFeePrice: resetData,
  },
  methods: {
    resetData,
    saveBundleCustom,
    updateCustomOptionPrice,
  }
}

function head (app) {
  if (this.currentProduct && this.currentProduct.personalized === 2) {
    return {
      link: [{
        rel: 'stylesheet',
        href: 'https://apis.personalbridge.com/style.css'
      }],
      script: [{
        src: app.$config.personalBridgeUrl,
        body: true
      }]
    }
  }
  return {}
}

function updateCustomOptionPrice (value) {
  this.customFeePrice = value
}

function resetData () {
  if (this.isShown && this.currentProduct) {
    this.isShowDesign = false
    this.customImage = false
    this.currentFileUploadUrl = false
    this.fileUpload = false
    this.campaignData = this.currentProduct
    this.optionList = { ...this.currentProduct.options }
    this.currentOptions = { ...this.currentProduct.currentOptions }
    if (this.currentProduct.personalized === 1 || this.currentProduct.personalized === 2) {
      this.getCampaignCustom(this.campaignData.slug)
    } else if (this.currentProduct.personalized === 3 || this.currentProduct?.full_printed === 5) {
      this.currentProduct.customFeePrice = this.customFeePrice || 0
      this.currentProduct.extraCustomFee = this.customFeePrice || 0
      this.currentProduct.customerCustomOptions = Object.freeze(this.customerCustomOptions)
      this.currentProduct.customOptionGroupNumbers = Object.freeze(this.customOptionGroupNumbers)
    }
  }
}

async function saveBundleCustom () {
  if (this.currentProduct.personalized === 1) {
    const checkCustomText = this.customTextList && this.customTextList.length && this.customTextList.find(item => !item.data.text)
    if (checkCustomText) {
      this.selectedCustom = checkCustomText
      this.optionError = 'customText'
      if (this.trackerTimeoutoptionError) {
        clearTimeout(this.trackerTimeoutoptionError)
      }

      this.trackerTimeoutoptionError = setTimeout(() => {
        this.optionError = ''
      }, 2000)
      this.$nextTick(() => {
        if (window.innerWidth > 768 || this.isModal) {
          document.getElementsByClassName('require-input')[0]?.focus()
        } else {
          document.getElementById('bottomCustomTextInput')?.focus()
        }
      })
    } else if (this.customImageList && this.customImageList.length && !this.fileUpload) {
      this.selectedCustom = this.customImageList[0]
      this.optionError = 'customImage'
      if (this.trackerTimeoutoptionError) {
        clearTimeout(this.trackerTimeoutoptionError)
      }
      this.trackerTimeoutoptionError = setTimeout(() => {
        this.optionError = ''
      }, 2000)
    } else {
      this.isShowModalBundle = false
      this.$emit('saveBundleCustom')
    }
  } else if (this.currentProduct.personalized === 2 && this.currentProduct.filterDesigns && this.currentProduct.filterDesigns.length) {
    const designData = this.currentProduct.filterDesigns[0].designData
    const validated = await window.pbsdk.validate(`design_${designData && designData.campaign_id}_${designData && designData.print_space}`)
    if (validated) {
      this.isShowModalBundle = false
      this.$emit('saveBundleCustom')
    } else {
      try {
        document.getElementsByClassName('ant-form-explain')[0].parentNode.parentNode.parentNode.scrollIntoView()
      } catch (error) {
      }
    }
  } else if (this.currentProduct.personalized === 3 || this.currentProduct.full_printed === 5) {
    const baseCustomOptions = [...this.currentProduct?.tempCustomOption] ?? []
    const baseCommonOptions = [...this.currentProduct?.tempCommonOption] ?? []
    const customOptions = [...this.currentProduct.customerCustomOptions]
    if (isMissingRequiredOption(baseCustomOptions.flat(), baseCommonOptions.flat(), customOptions)) {
      this.$toast.error(this.$t('Please fill in at least one option'))
      const invalidInput = document.getElementsByClassName('is-invalid')
      if (invalidInput.length) {
        invalidInput[0].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    } else {
      this.isShowModalBundle = false
      this.$emit('saveBundleCustom')
    }
  } else {
    this.isShowModalBundle = false
  }
}
</script>

<style lang="scss">
#modalBundleCustom {
  @media (max-width: 1023px) {
    .modal-content {
      height: 100vh;
      overflow: auto;
    }

    .modal-dialog{
      margin: 0 auto;
    }
  }

  @media (min-width: 1024px) {
    .info-box{
      max-height: 70vh;
      overflow-y: auto;
    }
  }

  .close-icon {
    font-size: 2rem;
  }

  .modal-body {
    overflow: auto;
  }

  .campaign-images-box {

    .thumb-canvas {
      width: 100%;
    }

    #viewBox{
      width: 100%;
    }

    .thumb {
      position: relative;
      width: 100%;
      padding-top: 125%;
      overflow: hidden;
      background-color: transparent;

      &>picture>img,
      .spinner,
      .zoom-on-hover {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        object-fit: contain;
        transition: transform 0.2s;
      }
    }

  }

  .viewcart-button {
      border: solid 1px black !important;
  }

  .bottom-button {
    transition: all .5s;
    position: fixed;
    bottom: 0px;
    left: 0;
    z-index: 100;

    .form-group > div {
      display: flex;
    }
  }

  #modalBundleCustom .addToCart-button {
    position: absolute;
    bottom: 0;
  }

  .size-select-img {
    width: 100%;
    max-width: 300px;
  }

  .product-add-to-cart-item {
    img {
      width: 80px;
      height: 100px;
    }
  }
}

.next-preview-customtext-btn {
  i {
    line-height: 2.9rem;
    font-size: 1.9rem;
  }
}
</style>
