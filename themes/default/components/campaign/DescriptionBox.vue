<template>
  <!-- info -->
  <div class="accordion mt-4" role="tablist">
    <div v-if="dataDescription">
      <div v-b-toggle.description class="text-secondary text-hover-custom h6 m-0 px-0 py-2 cursor-pointer" role="tab">
        <span class="text-capitalize">
          {{ $t('Description') }}
        </span>
        <span class="float-right">
          <i class="icon-sen-chevron-up when-open" />
          <i class="icon-sen-chevron-down when-closed" />
        </span>
      </div>
      <b-collapse id="description" accordion="my-accordion" role="tabpanel" class="collapse-tab">
        <div v-html="dataDescription" />
      </b-collapse>
    </div>

    <div v-if="dataProductDetail">
      <div v-b-toggle.productDetail class="text-secondary text-hover-custom m-0 h6 px-0 py-2 cursor-pointer" role="tab">
        <span class="text-capitalize">
          {{ $t('Product Detail') }}
        </span>

        <span class="float-right">
          <i class="icon-sen-chevron-up when-open" />
          <i class="icon-sen-chevron-down when-closed" />
        </span>
      </div>
      <b-collapse id="productDetail" accordion="my-accordion" role="tabpanel" class="collapse-tab">
        <div v-html="dataProductDetail" />
      </b-collapse>
    </div>

    <div>
      <div v-b-toggle.shippingInfo class="text-secondary text-hover-custom m-0 h6 px-0 py-2 cursor-pointer" role="tab">
        <span class="text-capitalize">
          {{ $t('Shipping info') }}
        </span>

        <span class="float-right">
          <i class="icon-sen-chevron-up when-open" />
          <i class="icon-sen-chevron-down when-closed" />
        </span>
      </div>
      <b-collapse id="shippingInfo" accordion="my-accordion" role="tabpanel" class="collapse-tab">
        <p>{{ $t('Production time') }}: </p>
        <ul>
          <li>
            {{ $t('Printed apparel (normal/ partial printing)') }}:  <strong>3-5 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('Special for Canvas/Poster printing') }}:  <strong>5-7 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('All Over Print apparel and products (Full Prints)') }}: <strong>7-10 {{ $t('business days') }}</strong>
          </li>
        </ul>
        <p>{{ $t('Shipping time') }}: </p>
        <ul>
          <li>
            {{ $t('Printed apparel (normal/ partial printing)') }}:  <strong>5-7 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('All Over Print apparel and products (Full Prints)') }}: <strong>11-14 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('International orders may take an additional') }}: <strong>1-2 {{ $t('weeks') }}</strong>.
          </li>
        </ul>
        <p class="mb-1">
          <strong>{{ $t('Note') }}: </strong>
          <span>{{ $t('Shipping time can take from') }}</span>
          <strong> {{ $t('21 to 25 business days') }}</strong>
          <span>, {{ $t('the tracking is normal that there might not be timely updated of your parcel therefore please wait patiently') }}.</span>
        </p>
        <a
          class="mb-1"
          :href="localePath(`/page/shipping-policy`)"
          style="color: var(--primary-color);"
          target="_blank"
        >{{ $t('View more') }}</a>
      </b-collapse>
      <div v-b-toggle.refundInfo class="text-secondary text-hover-custom m-0 h6 px-0 py-2 cursor-pointer" role="tab">
        <span class="text-capitalize">
          {{ $t('Return / refund detail') }}
        </span>

        <span class="float-right">
          <i class="icon-sen-chevron-up when-open" />
          <i class="icon-sen-chevron-down when-closed" />
        </span>
      </div>
      <b-collapse id="refundInfo" accordion="my-accordion" role="tabpanel" class="collapse-tab">
        <p>{{ $t('While we want every order to be perfect, mistakes can happen occasionally. We can offer reproductions and refunds for your orders if there are order mistakes. If you are submitting a reproduction or refund request, please include photo evidence of your product in your order.') }}</p>
        <p>{{ $t('Cases where we cover the Reprinting cost (Reproductions) or Refunding') }}:</p>
        <ul>
          <li>
            {{ $t('There is a manufacturing issue with your product.') }}
          </li>
          <li>
            {{ $t('The product in your order is broken or damaged during transit.') }}
          </li>
          <li>
            {{ $t('You receive the wrong product in your order.') }}
          </li>
          <li>
            {{ $t('The order is lost in transit, and the initial shipping address was correct.') }}
          </li>
          <li>
            {{ $t('The order is lost in transit, and the actual shipping time exceeds the general shipping time frame for orders.') }}
          </li>
        </ul>
        <p>{{ $t('If the goods delivered to you fall under one of the criteria noted above, please follow below steps to request from our customer service') }}: </p>
        <ul class="mb-1">
          <li>
            {{ $t('You must submit a request for a return within') }}&nbsp;<strong>14 {{ $t('business days') }}</strong>&nbsp;{{ $t('after you receive your goods.') }}
          </li>
          <li>
            {{ $t('After') }}&nbsp;<strong>14 {{ $t('business days') }}</strong>,&nbsp;{{ $t('no return will be accepted. Also, if goods are returned prior to submitting your request, those goods are forfeited.') }}
          </li>
          <li>
            {{ $t('Please note that customers are responsible for return shipping labels. We will send you return instructions separately by email.') }}
          </li>
          <li>
            {{ $t('You must submit a request for a return within') }}&nbsp;<strong>14 {{ $t('business days') }}</strong>&nbsp;{{ $t('after you receive your goods.') }}
          </li>
          <li>
            {{ $t('It will take approximately') }}&nbsp;<strong>5-10 {{ $t('business days') }}</strong>&nbsp;{{ $t('to process your refund.') }}
          </li>
        </ul>
        <a
          class="mb-1"
          :href="localePath(`/page/return-policy`)"
          style="color: var(--primary-color);"
          target="_blank"
        >{{ $t('View more') }}</a>
      </b-collapse>
    </div>
  </div>
</template>
<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['dataDescription', 'dataProductDetail']
}
</script>

<style lang="scss">
.collapsed  .when-open,
.not-collapsed  .when-closed {
  display: none;
}

.collapse-tab {
  max-height: 300px;
  overflow-y: auto;
}

.share-item {
  margin-right: 10px;

  img {
    height: 30px;
    width: 30px;
  }
}
</style>
