<template>
  <div>
    <p class="h6 text-uppercase mr-2">
      {{ $t('Customize') }}
      <i class="icon-sen-text-box-multiple-outline" />
    </p>
    <div :id="isModal ? 'artwork_form_selector_modal' : 'artwork_form_selector'" @click="$emit('changeCurrentDesign', currentDesign)" />
    <div class="d-flex justify-content-center mb-3 d-md-none">
      <b-button size="sm" variant="custom" class="border cursor-pointer" @click="$emit('scrollToImage')">
        {{ $t('Back to preview') }}
        <i class="icon-sen-arrow-up-thin" />
      </b-button>
    </div>
  </div>
</template>
<script>

/* eslint-disable vue/require-prop-types */
export default {
  props: ['isModal', 'campaign', 'currentDesign']

}
</script>
