<template>
  <div v-if="productStats" class="d-flex align-items-center">
    <span><i class="font-4xl icon-sen-cart-arrow-down" /></span>
    <div class="pl-2">
      <span class="font-weight-500">{{ $t('Other people want this') }}.</span>
      <span v-if="productStats.add_to_cart"> {{ $t('There are value people have this in cart right now', { value: productStats.add_to_cart} ) }}.</span>
      <span v-else-if="productStats.visit"> {{ $t('There are value people are viewing this', { value: productStats.visit} ) }}.</span>
    </div>
  </div>
</template>
<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['productStats']
}
</script>
