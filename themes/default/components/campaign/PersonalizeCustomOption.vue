<template>
  <div class="position-relative personalize">
    <loading-dot v-if="uploading" class="coc-loading" />
    <div class="custom-text-box border p-3 my-3">
      <p class="h6 mr-2 mb-0">
        <span class="text-uppercase">
          {{ $t('Customize your product') }}
        </span>
        <i class="icon-sen-image-multiple-outline" />
        <span v-if="isRequired" class="text-danger"> {{ $t('Required') }}</span>
      </p>
      <div>
        <div class="mt-2">
          <hr v-if="uniqueCommonOptionsList.length">
          <template v-for="( eO, iO ) in uniqueCommonOptionsList">
            <div v-if="commonOptionGroups?.[0]?.[iO]" :key="eO.uniqueId">
              <div v-if="eO.type === CUSTOM_OPTION_TYPE.TEXT" class="co-text my-2">
                <div class="d-flex justify-content-between">
                  <label>{{ $t(eO.label) }}</label>
                </div>
                <div class="position-relative">
                  <input
                    v-if="isLoadedCustomOption"
                    v-model="commonOptionGroups[0][iO].value"
                    class="form-control"
                    :class="(!commonOptionGroups?.[0]?.[iO]?.value && requiredValue(commonOptionGroups?.[0]?.[iO]?.unrequired) === 0) || requiredValue(commonOptionGroups?.[0]?.[iO]?.is_invalid) === 1 ? 'is-invalid' : ''"
                    :placeholder="commonOptionGroups?.[0]?.[iO]?.placeholder ? $t(commonOptionGroups?.[0]?.[iO]?.placeholder) : $t(eO.label)"
                    @input="inputText(0, iO, $event, 2)"
                    @change="changeText(0, iO, $event, 2)"
                  >
                  <small v-if="(commonOptionGroups?.[0]?.[iO]?.value?.length ?? commonOptionGroups?.[0]?.[iO]?.value?.length ?? 0) > 0 || requiredValue(commonOptionGroups?.[0]?.[iO]?.unrequired) === 1" class="d-flex align-items-center text-counter">{{ commonOptionGroups?.[0]?.[iO]?.value?.length ?? commonOptionGroups?.[0]?.[iO]?.value?.length ?? 0 }}{{ (commonOptionGroups?.[0]?.[iO]?.max_length ? ' / ' + commonOptionGroups?.[0]?.[iO]?.max_length : null) }}</small>
                </div>
              </div>
              <div v-if="eO.type === CUSTOM_OPTION_TYPE.DROPDOWN" class="co-dropdown my-2">
                <label>{{ $t(eO.label) }}</label>
                <v-select
                  v-if="isLoadedCustomOption"
                  v-model="commonOptionGroups[0][iO].value"
                  :options="eO?.value"
                  :clearable="false"
                  :placeholder="$t('Select a') + ' ' + $t(eO.label)"
                  :class="(!commonOptionGroups?.[0]?.[iO]?.value && requiredValue(commonOptionGroups?.[0]?.[iO]?.unrequired) === 0) || requiredValue(commonOptionGroups?.[0]?.[iO]?.is_invalid) === 1 ? 'is-invalid' : ''"
                  @input="() => { trackCustomInputChange(commonOptionGroups[0][iO].label, commonOptionGroups[0][iO].value) }"
                />
              </div>
              <div v-if="eO.type === CUSTOM_OPTION_TYPE.IMAGE" class="co-image my-2">
                <label>{{ $t(eO.label) }}</label>
                <b-form-file
                  v-if="isLoadedCustomOption"
                  v-model="commonOptionGroups[0][iO].value"
                  accept="image/*"
                  :placeholder="commonOptionGroups?.[0]?.[iO]?.value ? commonOptionGroups?.[0]?.[iO]?.value.name : $t('Choose a image or drop it here...')"
                  :browse-text="$t('Upload')"
                  :class="(!commonOptionGroups?.[0]?.[iO]?.value && requiredValue(commonOptionGroups?.[0]?.[iO]?.unrequired) === 0) || requiredValue(commonOptionGroups?.[0]?.[iO]?.is_invalid) === 1 ? 'is-invalid' : ''"
                  @change="upload(0, iO, $event, 2)"
                />
              </div>
            </div>
          </template>
          <hr v-if="uniqueCommonOptionsList.length">
          <label v-if="customOptions.group?.limit > 1">{{ $t('Number of') }} {{ customOptions.group.name ? $t(customOptions.group.name) : $t('custom') }} </label>
          <v-select
            v-if="customOptions.group?.limit > 1"
            v-model="coGroupLimit"
            :options="groupLimitSelect"
            :clearable="false"
            :placeholder="$t('Select number of') + ' ' + $t(customOptions.group.name)"
            :class="{'is-invalid': !coGroupLimit}"
          >
            <template #option="{label}">
              <div>
                {{ label }} {{ label > 1 && customOptions.group.extra_custom_fee && parseFloat(customOptions.group.extra_custom_fee) > 0 ? `(+${ $formatPrice(customOptions.group.extra_custom_fee * (label - 1) ) })` : '' }}
              </div>
            </template>
          </v-select>
          <div v-for="i in coGroupLimit" :key="`${i}`" class="my-3">
            <hr v-if="customOptions.group?.limit > 1">
            <template v-for="( eO, iO ) in uniqueCustomOptionsList">
              <div v-if="optionGroups?.[(i - 1)]?.[iO]" :key="eO.uniqueId">
                <div v-if="eO.type === CUSTOM_OPTION_TYPE.TEXT" class="co-text my-2">
                  <div class="d-flex justify-content-between">
                    <label>{{ $t(customOptions.group.name ?? eO.label) + ' ' + (customOptions.group.limit > 1 ? ' ' + i : '') }}</label>
                  </div>
                  <div class="position-relative">
                    <input
                      v-if="isLoadedCustomOption"
                      v-model="optionGroups[(i - 1)][iO].value"
                      class="form-control"
                      :class="(!optionGroups[(i - 1)][iO]?.value && requiredValue(optionGroups[(i - 1)][iO]?.unrequired) === 0) || requiredValue(optionGroups[(i - 1)][iO]?.is_invalid) === 1 ? 'is-invalid' : ''"
                      :placeholder="optionGroups[(i - 1)][iO]?.placeholder ? $t(optionGroups[(i - 1)][iO]?.placeholder) : $t(eO.label)"
                      @input="inputText((i - 1), iO, $event)"
                      @change="changeText((i - 1), iO, $event)"
                    >
                    <small v-if="(optionGroups[(i - 1)][iO]?.value?.length ?? optionGroups[(i - 1)][iO]?.value?.length ?? 0) > 0 || requiredValue(optionGroups[(i - 1)][iO]?.unrequired) === 1" class="d-flex align-items-center text-counter">{{ optionGroups[(i - 1)][iO]?.value?.length ?? optionGroups[(i - 1)][iO]?.value?.length ?? 0 }}{{ (optionGroups[(i - 1)][iO]?.max_length ? ' / ' + optionGroups[(i - 1)][iO]?.max_length : null) }}</small>
                  </div>
                </div>
                <div v-if="eO.type === CUSTOM_OPTION_TYPE.DROPDOWN" class="co-dropdown my-2">
                  <label>{{ $t(customOptions.group.name ?? eO.label) + ' ' + (customOptions.group?.limit > 1 ? ' ' + i : '') }}</label>
                  <v-select
                    v-if="isLoadedCustomOption"
                    v-model="optionGroups[(i - 1)][iO].value"
                    :options="eO?.value"
                    :clearable="false"
                    :placeholder="$t('Select a') + ' ' + $t(eO.label)"
                    :class="(!optionGroups[(i - 1)][iO]?.value && requiredValue(optionGroups[(i - 1)][iO]?.unrequired) === 0) || requiredValue(optionGroups[(i - 1)][iO]?.is_invalid) === 1 ? 'is-invalid' : ''"
                    @input="() => { trackCustomInputChange(optionGroups[(i - 1)][iO].label, optionGroups[(i - 1)][iO].value) }"
                  />
                </div>
                <div v-if="eO.type === CUSTOM_OPTION_TYPE.IMAGE" class="co-image my-2">
                  <label>{{ $t(customOptions.group.name ?? eO.label) + ' ' + (customOptions.group.limit > 1 ? ' ' + i : '') }}</label>
                  <b-form-file
                    v-if="isLoadedCustomOption"
                    v-model="optionGroups[(i - 1)][iO].value"
                    accept="image/*"
                    :placeholder="optionGroups[(i - 1)][iO]?.value ? optionGroups[(i - 1)][iO]?.value.name : $t('Choose a image or drop it here...')"
                    :browse-text="$t('Upload')"
                    :class="(!optionGroups[(i - 1)][iO]?.value && requiredValue(optionGroups[(i - 1)][iO]?.unrequired) === 0) || requiredValue(optionGroups[(i - 1)][iO]?.is_invalid) === 1 ? 'is-invalid' : ''"
                    @change="upload((i - 1), iO, $event)"
                  />
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { CUSTOM_OPTION_TYPE } from '~/helpers/variableConst'

export default {
  name: 'PersonalizeCustomOption',
  // eslint-disable-next-line vue/require-prop-types
  props: ['isModal', 'customOptions', 'commonOptions', 'tempCustomOption', 'customOptionGroupNumbers', 'campaign', 'isLoadedCustomOption', 'currentProduct', 'currentOptions'],
  data () {
    return {
      CUSTOM_OPTION_TYPE,
      defaultOptions: [],
      coGroupLimit: 0,
      optionGroups: [],
      commonOptionGroups: [],
      customerCustomOptions: [],
      groupLimitSelect: [],
      validate: {
        groupNumber: true
      },
      uploading: false,
      extraCustomFee: 0,
      extraCommonCustomFee: 0,
      allExtraCustomFee: 0,
      maxLength: null
    }
  },
  computed: {
    queryGroupNumber () {
      if (!this.isModal && this.$route.query.groups && (typeof this.$route.query.groups === 'string' || typeof this.$route.query.groups === 'number')) {
        let groupNumber = parseInt(this.$route.query.groups)
        if (groupNumber) {
          if (groupNumber <= 0) {
            groupNumber = 1
          } else if (groupNumber > this.customOptions.group?.limit) {
            groupNumber = this.customOptions.group?.limit
          }
          return groupNumber
        }
      }
      return 1
    },
    uniqueCustomOptionsList () {
      if (!this.customOptions || !this.customOptions.options) {
        return []
      }
      return this.customOptions.options.map((eO, iO) => ({
        ...eO,
        uniqueId: `${eO?.type ?? 'uuid'}-${iO}`
      }))
    },
    uniqueCommonOptionsList () {
      if (!this.commonOptions) {
        return []
      }
      return [...this.commonOptions.options].map((eO, iO) => ({
        ...eO,
        uniqueId: `${eO?.type ?? 'uuid'}-common-${iO}`
      }))
    },
    isRequired () {
      return this.uniqueCommonOptionsList.some(option => this.requiredValue(option?.unrequired) === 0)
    },
  },
  watch: {
    coGroupLimit (newLimit, oldLimit) {
      if (newLimit <= 0) {
        this.coGroupLimit = oldLimit
      }
      if (newLimit > this.customOptions.group?.limit) {
        this.coGroupLimit = this.customOptions.group.limit
      }
      if (this.coGroupLimit > oldLimit && this.coGroupLimit > this.optionGroups.length) {
        for (let i = 0; i < (this.coGroupLimit - oldLimit); i++) {
          this.optionGroups.push(JSON.parse(JSON.stringify(this.defaultOptions)))
        }
      }
      if (this.coGroupLimit < oldLimit) {
        this.optionGroups.splice(this.coGroupLimit, (oldLimit - this.coGroupLimit))
      }
      this.$emit('updateCustomOptionGroupNumbers', this.coGroupLimit)
      this.$emit('updateCustomOptionGroupFee', parseFloat(this.allExtraCustomFee))
    },
    optionGroups: {
      handler (newValue) {
        this.$emit('updateTempCustomOption', newValue)
        this.$emit('updateCustomizeOptions', this.optionsValidate(this.coGroupLimit, newValue) ? this.customerCustomOptions.flat() : [])
        if (!this.isModal) {
          this.optionsToQueryParameters(newValue)
        }
        this.calculateExtraCustomOptionsFee()
        this.$emit('updateCustomOptionGroupFee', parseFloat(this.allExtraCustomFee))
        this.checkAllowCustomCampaignAddToCart()
      },
      deep: true
    },
    commonOptionGroups: {
      handler (newValue) {
        this.$emit('updateTempCommonOption', newValue)
        this.$emit('updateCustomizeOptions', this.commonOptionsValidate(newValue) ? this.customerCustomOptions.flat() : [])
        if (!this.isModal) {
          this.commonOptionsToQueryParameters(newValue)
        }
        this.calculateExtraCustomOptionsFee()
        this.$emit('updateCustomOptionGroupFee', parseFloat(this.allExtraCustomFee))
        this.checkAllowCustomCampaignAddToCart()
      },
      deep: true
    },
    customOptions: {
      handler () {
        this.optionGroups = [this.mapOptions(this.customOptions.options)]
        this.defaultOptions = this.customOptions ? this.mapOptions(this.customOptions.options) : []
        this.extraCustomFee = 0
        if (this.customOptions.group && this.customOptions.group.extra_custom_fee) {
          this.extraCustomFee = this.customOptions.group.extra_custom_fee
        }
        this.groupLimitSelect = Array.from({ length: this.customOptions.group.limit }, (_, i) => i + 1)
        this.calculateExtraCustomOptionsFee()
      },
      deep: true
    },
    commonOptions: {
      handler () {
        this.commonOptionGroups = [this.mapOptions(this.commonOptions.options)]
        this.extraCommonCustomFee = 0
        if (this.commonOptions && this.commonOptions.extra_custom_fee) {
          this.extraCommonCustomFee = this.commonOptions.extra_custom_fee
        }
        this.calculateExtraCustomOptionsFee()
      },
      deep: true
    }
  },
  created () {
    this.defaultOptions = this.mapOptions(this.customOptions.options)
    this.commonOptionGroups = this.commonOptions ? [this.mapOptions(this.commonOptions.options)] : []
    this.groupLimitSelect = Array.from({ length: this.customOptions.group?.limit ?? 0 }, (_, i) => i + 1)
    this.extraCustomFee = 0
    if (this.customOptions && this.customOptions.group && this.customOptions.group.extra_custom_fee) {
      this.extraCustomFee = this.customOptions.group.extra_custom_fee
    }
    this.extraCommonCustomFee = 0
    if (this.commonOptions && this.commonOptions.extra_custom_fee) {
      this.extraCommonCustomFee = this.commonOptions.extra_custom_fee
    }
  },
  mounted () {
    if (this.queryGroupNumber) {
      this.coGroupLimit = this.queryGroupNumber
      if (!this.isModal) {
        this.optionGroups = this.optionsFromQueryParameters()
        this.commonOptionGroups = this.commonOptionsFromQueryParameters()
      }
    } else {
      this.coGroupLimit = 1
    }
  },
  methods: {
    upload (index, iO, event, type = 1) {
      const files = event.target.files

      if (files.length > 0) {
        const maxSize = 20

        if (files[0].size === 0) {
          event.target.value = null
          return this.$toast.error('Error: The file is corrupted')
        }

        if (files[0].size > maxSize * 1024 * 1024) {
          event.target.value = null
          return this.$toast.error(`Error: File size must be smaller than ${maxSize}MB`)
        }

        this.uploading = true
        this.$emit('isLoadingAddToCart', true)
        const upload = this.$preSignedUploader(files[0])
        upload.then(async (result) => {
          const path = await result.Key
          if (type === 1) {
            this.optionGroups[index][iO].imagePath = path
            this.optionGroups[index][iO].value = new File([], path)

            this.trackCustomInputChange(this.optionGroups[index][iO].label, path)
          } else {
            this.commonOptionGroups[index][iO].imagePath = path
            this.commonOptionGroups[index][iO].value = new File([], path)

            this.trackCustomInputChange(this.commonOptionGroups[index][iO].label, path)
          }
          this.uploading = false
          this.$emit('isLoadingAddToCart', false)
        }).catch((error) => {
          event.target.value = null
          if (type === 1) {
            this.optionGroups[index][iO].value = null
            this.optionGroups[index][iO].imagePath = null
          } else {
            this.commonOptionGroups[index][iO].value = null
            this.commonOptionGroups[index][iO].imagePath = null
          }
          this.$toast.error(`Error: ${error.message}`)
          this.uploading = false
          this.$emit('isLoadingAddToCart', false)
        })
      } else {
        event.target.value = null
        if (type === 1) {
          this.optionGroups[index][iO].value = null
          this.optionGroups[index][iO].imagePath = null
        } else {
          this.commonOptionGroups[index][iO].value = null
          this.commonOptionGroups[index][iO].imagePath = null
        }
      }
    },
    optionsValidate (coGroupLimit, optionGroups) {
      let valid = true
      if (!coGroupLimit || !optionGroups.length || coGroupLimit !== optionGroups.length) {
        valid = false
      }
      optionGroups.forEach((e) => {
        e.forEach((eO) => {
          if ((eO.type === CUSTOM_OPTION_TYPE.TEXT && !eO?.value && (!eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.DROPDOWN && !eO?.value && (!eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.IMAGE && (!eO?.value || !eO?.imagePath) && (!eO?.unrequired || eO?.unrequired === false))
          ) {
            valid = false
          }
        })
      })
      let hasValue = false
      optionGroups.forEach((e) => {
        e.forEach((eO) => {
          if ((eO.type === CUSTOM_OPTION_TYPE.TEXT && (eO?.value || !eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.DROPDOWN && (eO?.value || !eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.IMAGE && (eO?.value || eO.imagePath || !eO?.unrequired || eO?.unrequired === false))
          ) {
            hasValue = true
          }
        })
      })
      if (!hasValue) {
        valid = false
      }
      optionGroups = optionGroups.map((e, i) => {
        return e.map((eO) => {
          return {
            ...eO,
            g_index: i,
            g_type: 'group'
          }
        })
      })
      const commonOptionGroups = [...this.commonOptionGroups].map((e, i) => {
        return e.map((eO) => {
          return {
            ...eO,
            g_type: 'common'
          }
        })
      })
      if (valid) {
        this.customerCustomOptions[0] = [...optionGroups]
        this.customerCustomOptions[1] = [...commonOptionGroups]
      }
      return valid
    },
    commonOptionsValidate (commonOptionGroups) {
      let valid = true
      if (!commonOptionGroups.length) {
        valid = false
      }
      commonOptionGroups.forEach((e) => {
        e.forEach((eO) => {
          if ((eO.type === CUSTOM_OPTION_TYPE.TEXT && !eO?.value && (!eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.DROPDOWN && !eO?.value && (!eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.IMAGE && (!eO?.value || !eO?.imagePath) && (!eO?.unrequired || eO?.unrequired === false))
          ) {
            valid = false
          }
        })
      })
      let hasValue = false
      commonOptionGroups.forEach((e) => {
        e.forEach((eO) => {
          if ((eO.type === CUSTOM_OPTION_TYPE.TEXT && (eO?.value || !eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.DROPDOWN && (eO?.value || !eO?.unrequired || eO?.unrequired === false)) ||
            (eO.type === CUSTOM_OPTION_TYPE.IMAGE && (eO?.value || eO.imagePath || !eO?.unrequired || eO?.unrequired === false))
          ) {
            hasValue = true
          }
        })
      })
      if (!hasValue) {
        valid = false
      }
      const optionGroups = [...this.optionGroups].map((e, i) => {
        return e.map((eO) => {
          return {
            ...eO,
            g_index: i,
            g_type: 'group'
          }
        })
      })
      commonOptionGroups = commonOptionGroups.map((e, i) => {
        return e.map((eO) => {
          return {
            ...eO,
            g_type: 'common'
          }
        })
      })
      if (valid) {
        this.customerCustomOptions[0] = [...optionGroups]
        this.customerCustomOptions[1] = [...commonOptionGroups]
      }
      return valid
    },
    optionsFromTempValue () {
      const newOptionGroups = JSON.parse(JSON.stringify(this.tempCustomOption))
      for (let i = 0; i < this.coGroupLimit; i++) {
        for (let l = 0; l < newOptionGroups.length; l++) {
          if (newOptionGroups[i][l].type === CUSTOM_OPTION_TYPE.IMAGE && newOptionGroups[i][l].imagePath && !newOptionGroups[i][l].value) {
            newOptionGroups[i][l].value = new File([], newOptionGroups[i][l].imagePath)
          }
        }
      }
      return newOptionGroups
    },
    optionsFromQueryParameters () {
      const g = []
      for (let i = 0; i < this.coGroupLimit; i++) {
        g[i] = JSON.parse(JSON.stringify(this.defaultOptions))
        for (let l = 0; l < g[i].length; l++) {
          if (g[i][l].type === CUSTOM_OPTION_TYPE.TEXT) {
            const k = 'text_' + (i + 1) + '_' + l
            if (this.$route.query[k] && (typeof this.$route.query[k] === 'string' || typeof this.$route.query[k] === 'number')) {
              g[i][l].value = this.$route.query[k]
              if (g[i][l]?.max_length && g[i][l].max_length < this.$route.query[k]?.length) {
                g[i][l].value = this.$route.query[k].slice(0, g[i][l].max_length)
              }
            }
          }

          if (g[i][l].type === CUSTOM_OPTION_TYPE.DROPDOWN) {
            const k = 'dropdown_' + (i + 1) + '_' + l
            if (this.$route.query[k] && (typeof this.$route.query[k] === 'string' || typeof this.$route.query[k] === 'number')) {
              const v = this.customOptions.options[l]?.value.filter((d) => {
                return d.toLowerCase() === this.$route.query[k].toLowerCase()
              })
              if (v.length > 0) {
                g[i][l].value = v[0]
              }
            }
          }

          if (g[i][l].type === CUSTOM_OPTION_TYPE.IMAGE) {
            const k = 'image_' + (i + 1) + '_' + l
            if (this.$route.query[k] && (typeof this.$route.query[k] === 'string')) {
              g[i][l].value = new File([], this.$route.query[k])
              g[i][l].imagePath = this.$route.query[k]
            }
          }
        }
      }
      return g
    },
    commonOptionsFromQueryParameters () {
      const g = []
      for (let i = 0; i < 1; i++) {
        g[i] = []
        if (this.commonOptionGroups[i]) {
          g[i] = JSON.parse(JSON.stringify(this.commonOptionGroups[i]))
        }
        for (let l = 0; l < g[i].length; l++) {
          if (g[i][l].type === CUSTOM_OPTION_TYPE.TEXT) {
            const k = 'c_text_' + (i + 1) + '_' + l
            if (this.$route.query[k] && (typeof this.$route.query[k] === 'string' || typeof this.$route.query[k] === 'number')) {
              g[i][l].value = this.$route.query[k]
              if (g[i][l]?.max_length && g[i][l].max_length < this.$route.query[k]?.length) {
                g[i][l].value = this.$route.query[k].slice(0, g[i][l].max_length)
              }
            }
          }

          if (g[i][l].type === CUSTOM_OPTION_TYPE.DROPDOWN) {
            const k = 'c_dropdown_' + (i + 1) + '_' + l
            if (this.$route.query[k] && (typeof this.$route.query[k] === 'string' || typeof this.$route.query[k] === 'number')) {
              const v = this.commonOptions.options[l]?.value.filter((d) => {
                return d.toLowerCase() === this.$route.query[k].toLowerCase()
              })
              if (v.length > 0) {
                g[i][l].value = v[0]
              }
            }
          }

          if (g[i][l].type === CUSTOM_OPTION_TYPE.IMAGE) {
            const k = 'c_image_' + (i + 1) + '_' + l
            if (this.$route.query[k] && (typeof this.$route.query[k] === 'string')) {
              g[i][l].value = new File([], this.$route.query[k])
              g[i][l].imagePath = this.$route.query[k]
            }
          }
        }
      }
      return g
    },
    optionsToQueryParameters (optionGroups) {
      const query = { ...this.$route.query, ...this.currentOptions }
      optionGroups.forEach((e, i) => {
        e.forEach((eO, iO) => {
          if (eO.type === CUSTOM_OPTION_TYPE.TEXT) {
            query['text_' + (i + 1) + '_' + iO] = eO?.value
          }

          if (eO.type === CUSTOM_OPTION_TYPE.DROPDOWN) {
            query['dropdown_' + (i + 1) + '_' + iO] = eO?.value
          }

          if (eO.type === CUSTOM_OPTION_TYPE.IMAGE) {
            query['image_' + (i + 1) + '_' + iO] = eO.imagePath
          }
        })
      })

      if (optionGroups.length) {
        query.groups = this.coGroupLimit = optionGroups.length
      }

      Object.keys(query).forEach((key) => {
        if (query[key] === null || query[key] === '') {
          delete query[key]
        }
      })
      this.$router.push(this.localePath({ query }))
    },
    commonOptionsToQueryParameters (commonOptionGroups) {
      const query = { ...this.$route.query, ...this.currentOptions }
      commonOptionGroups.forEach((e, i) => {
        e.forEach((eO, iO) => {
          if (eO.type === CUSTOM_OPTION_TYPE.TEXT) {
            query['c_text_' + (i + 1) + '_' + iO] = eO?.value
          }

          if (eO.type === CUSTOM_OPTION_TYPE.DROPDOWN) {
            query['c_dropdown_' + (i + 1) + '_' + iO] = eO?.value
          }

          if (eO.type === CUSTOM_OPTION_TYPE.IMAGE) {
            query['c_image_' + (i + 1) + '_' + iO] = eO.imagePath
          }
        })
      })
      this.$router.push(this.localePath({ query }))
    },
    inputText (groupIndex, optionIndex, event, type = 1) {
      let value = event.target?.value
      const totalLength = value?.length
      const maxLength = (type === 1 ? this.optionGroups[groupIndex][optionIndex]?.max_length : this.commonOptionGroups[groupIndex][optionIndex]?.max_length) || this.maxLength
      if (maxLength && totalLength > maxLength) {
        value = value.slice(0, maxLength)
      }
      if (type === 1) {
        this.optionGroups[groupIndex][optionIndex].value = value
        this.trackCustomInputChange(this.optionGroups[groupIndex][optionIndex].label, this.optionGroups[groupIndex][optionIndex].value)
      } else {
        this.commonOptionGroups[groupIndex][optionIndex].value = value
        this.trackCustomInputChange(this.commonOptionGroups[groupIndex][optionIndex].label, this.commonOptionGroups[groupIndex][optionIndex].value)
      }
    },
    changeText (groupIndex, optionIndex, event, type = 1) {
      let value = event.target?.value?.trim()
      let totalLength = value?.length
      if (totalLength > 0 && value?.trim()?.length === 0) {
        value = ''
        totalLength = 0
      }
      value = value.trim()
      totalLength = value.length
      const maxLength = (type === 1 ? this.optionGroups[groupIndex][optionIndex]?.max_length : this.commonOptionGroups[groupIndex][optionIndex]?.max_length) || this.maxLength
      if (maxLength && totalLength > maxLength) {
        value = value.slice(0, maxLength)
      }
      if (type === 1) {
        this.optionGroups[groupIndex][optionIndex].value = value
      } else {
        this.commonOptionGroups[groupIndex][optionIndex].value = value
      }
    },
    requiredValue (value) {
      return isNaN(value) ? 0 : Number(value)
    },
    mapOptions (options, resetValue = true) {
      const vm = this
      if (!options || !Array.isArray(options)) {
        return []
      }
      return JSON.parse(JSON.stringify(options)).map(function (g) {
        g.unrequired = g?.unrequired || false
        g.price = g?.price ? parseFloat(g.price) : 0
        if (g.type === CUSTOM_OPTION_TYPE.TEXT) {
          g.placeholder = g?.value
          g.value = resetValue ? null : g?.value
          g.max_length = g?.max_length || vm.maxLength
        }
        if (g.type === CUSTOM_OPTION_TYPE.DROPDOWN && resetValue) {
          g.value = null
        }
        if (g.type === CUSTOM_OPTION_TYPE.IMAGE && resetValue) {
          g.imagePath = null
        }
        return g
      })
    },
    checkAllowCustomCampaignAddToCart () {
      let response = true
      this.optionGroups.forEach((group) => {
        group.forEach((option) => {
          if ((!option?.value && this.requiredValue(option?.unrequired) === 0) || (option?.max_length && option?.value?.length && option.max_length < option.value.length)) {
            response = false
          }
        })
      })
      this.commonOptionGroups.forEach((group) => {
        group.forEach((option) => {
          if ((!option?.value && this.requiredValue(option?.unrequired) === 0) || (option?.max_length && option?.value?.length && option.max_length < option.value.length)) {
            response = false
          }
        })
      })
      this.$emit('updateAllowCustomCampaign', response)
    },
    calculateExtraCustomOptionsFee () {
      this.allExtraCustomFee = 0
      const hasCommonOptions = this.commonOptionGroups.some(group => group.some(option => option?.value && option?.value?.length > 0))
      if (this.campaign.personalized === 0 && this.currentProduct.full_printed === 5) {
        // idx > 0 to set free for the first group
        this.optionGroups.forEach((group, idx) => {
          if (group.some(option => option?.value) && idx > 0) {
            this.allExtraCustomFee += parseFloat(this.extraCustomFee)
          }
          group.forEach((option) => {
            if (option?.value && option?.price && parseFloat(option.price) > 0 && idx > 0) {
              this.allExtraCustomFee += parseFloat(option.price)
            }
          })
        })
      } else {
        this.optionGroups.forEach((group, idx) => {
          if (group.some(option => option?.value) && idx > 0) {
            this.allExtraCustomFee += parseFloat(this.extraCustomFee)
          }
        })
      }
      if (hasCommonOptions) {
        this.allExtraCustomFee += parseFloat(this.extraCommonCustomFee)
      }
    },
    trackCustomInputChange (label, value) {
      window.dispatchEvent(new CustomEvent('sp_custom_input_change', {
        detail: `Input: ${label} | Value: ${value}`
      }))
    }
  }
}
</script>

<style>
.is-invalid div[role="combobox"], .is-invalid .custom-file-label {
  border: 1px solid #dc3545;
}
.coc-loading {
  position: absolute;
  z-index: 999999;
  width: 100%;
  height: 100%;
  display: flex !important;
  justify-content: center;
  align-items: center;
  background-color: #d6d6d67a;
}
.text-counter {
  font-size: 80%;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 0;
}
.vs__dropdown-toggle {
  height: calc(1.5em + 0.75rem + 2px);
}
</style>
