<template>
  <div>
    <h6 class="sen-text-truncate">
      <span>{{ currentProduct.name }} </span>
      <span v-if="currentOptions">
        <template
          v-for="(item, optionIndex) in acceptKeys"
        >
          <span
            v-if="!currentOptions[item]?.startsWith('__')"
            :key="optionIndex"
            class="text-uppercase"
          >&nbsp;/&nbsp;
            {{ currentOptions[item].split('-').join(' ') || item }}
          </span>
        </template>
      </span>
    </h6>
    <div v-if="isShowAuthor && campaign && campaign.seller && $store.state.storeInfo.id === 1">
      <span style="color: #979797">{{ $t('Designed and Sold by') }}</span> <span><strong>{{ campaign.seller.nickname }}</strong></span>
    </div>
  </div>
</template>

<script>
import { getDisabledKeys } from '~/helpers/order'

export default {
  name: 'CampaignGeneralInfo',
  // eslint-disable-next-line vue/require-prop-types
  props: ['campaign', 'currentProduct', 'currentOptions', 'optionList', 'isShowAuthor'],
  computed: {
    // Key has only 1 element is disabled
    disabledKeys () {
      return getDisabledKeys(this.optionList)
    },
    acceptKeys () {
      return Object.keys(this.optionList).filter(key => !this.disabledKeys.includes(key))
    }
  }
}
</script>

<style lang="scss">
.title-campaign {
  font-size: 16px;

  @media (min-width: 768px) {
    font-size: 25px;
  }
}
</style>
