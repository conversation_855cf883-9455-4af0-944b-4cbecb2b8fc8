<template>
  <div id="app">
    <client-only>
      <VueEasyLightbox
        :visible="visible"
        :imgs="currentPath"
        :index="imageClicked"
        @hide="handleHide"
      />
    </client-only>
  </div>
</template>
<script>
import VueEasyLightbox from 'vue-easy-lightbox'
export default {
  components: {
    VueEasyLightbox,
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['images', 'color', 'imageClicked'],
  data () {
    return {
      pictureType: 1,
      index: null,
      type: 'full_hd',
      visible: false,
    }
  },

  computed: {
    currentPath,
  },
  mounted () {

  },
  methods: {
    handleHide () {
      this.visible = false
      this.$emit('close-box', null)
    },
    show () {
      this.visible = true
    },
  }
}

function currentPath () {
  this.imageList = []
  if (!this.images) {
    return this.imageList
  }
  for (const element of this.images) {
    const imagePath = element.file_url
    if (imagePath.startsWith('data:image/png')) {
      this.imageList.push(imagePath)
      continue
    }
    if (imagePath && imagePath.startsWith('/images/')) {
      this.imageList.push(imagePath)
    }
    if (this.pictureType === 1) {
      this.imageList.push(this.$imgUrl(imagePath, this.type || 'thumb', 'webp', this.color))
    } else if (this.pictureType === 2) {
      this.imageList.push(this.$imgUrl(imagePath, this.type || 'thumb', null, this.color))
    }
    if (this.errorPath) {
      return this.errorPath
    }
    // this.imageList.push(`${this.$config.publicPath}/images/no-image.webp`)
  }

  if (this.imageClicked != null) {
    this.show()
  }
  return this.imageList
}

</script>
<style lang="scss">
</style>
