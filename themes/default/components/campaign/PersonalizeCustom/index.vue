<template>
  <div v-if="(customTextList && customTextList.length) || customDesignType || (customImageList && customImageList.length)" class="custom-box border p-3 my-3 personalize">
    <div v-if="customTextList && customTextList.length">
      <p class="h6 text-uppercase mr-2 mb-0">
        {{ $t('Customize text') }}
        <i class="icon-sen-text-box-multiple-outline" />
      </p>
      <p v-show="isShowAlertInput" class="text-danger mb-1">
        {{ $t('Write something to customize the product') }}
      </p>
      <custom-text-item
        v-for="(customText, index) in customTextList"
        :key="index"
        :is-modal="isModal"
        :option-error="optionError"
        :custom-text="customText.data"
        @selectDesign="$emit('changeCurrentDesign', customText.design)"
        @updateDesignQuery="$emit('updateDesignQuery', customText)"
        @updateCanvasData="value=>$emit('updateCanvasData', customText, value)"
      />
    </div>

    <div v-if="customDesignType">
      <h6>{{ $t('Select design') }} <loading-dot v-if="loadingChangeCustomDesign" variant="dark" class="mt-2" /></h6>
      <b-form-select
        :value="customDesign"
        class="text-uppercase"
        :disabled="loadingChangeCustomDesign"
        @input="(value)=>$emit('updateCustomDesign', value )"
      >
        <b-form-select-option
          v-for="item in customDesignArray[customDesignType]"
          :key="item"
          :value="item"
        >
          {{ item }}
        </b-form-select-option>
      </b-form-select>
    </div>

    <div v-if="customImageList && customImageList.length" class="mt-3">
      <p class="h6 mr-2 mb-0">
        <span class="text-uppercase">
          {{ $t('Customize image') }}
        </span>
        <i class="icon-sen-image-multiple-outline" />
        <span v-show="!fileUpload" class="text-danger"> {{ $t('Required') }}</span>
      </p>
      <p v-show="isShowAlertInput2" class="text-danger mb-1">
        {{ $t('Add images to customize the product') }}
      </p>
      <custom-image-item
        v-for="(customImage, index) in customImageList"
        :key="index"
        name="campaign"
        :index="index"
        :is-modal="isModal"
        :option-error="optionError"
        :custom-image="customImage.data"
        :is-upload-file="isUploadFile"
        :file-upload="fileUpload"
        @selectDesign="$emit('changeCurrentDesign', customImage.design)"
        @updateCanvasData="value=>$emit('updateCanvasData', customImage, value)"
        @editDesign="$emit('editDesign', customImage.design)"
      />
    </div>
  </div>
</template>
<script>

/* eslint-disable vue/require-prop-types */
import CustomTextItem from '~/themes/default/components/campaign/PersonalizeCustom/CustomTextItem'
import CustomImageItem from '~/themes/default/components/campaign/PersonalizeCustom/CustomImageItem'

export default {
  components: {
    CustomTextItem,
    CustomImageItem
  },
  props: [
    'isModal', 'optionError',
    'customTextList', 'customImageList', 'isUploadFile', 'fileUpload',
    'customDesignType', 'customDesign', 'loadingChangeCustomDesign', 'customDesignArray'],
  data () {
    return {
      showListingProduct: false,
      isShowAlertInput: false,
      isShowAlertInput2: false
    }
  },
  watch: {
    optionError (value) {
      if (value === 'customText') {
        this.isShowAlertInput = true
      } else {
        this.isShowAlertInput = false
      }

      if (value === 'customImage') {
        this.isShowAlertInput2 = true
      } else {
        this.isShowAlertInput2 = false
      }
    }
  }
}
</script>

<style lang="scss">
.custom-box {
  border-radius: 5px;
  background-color: #f5f5f5;
}
</style>
