<template>
  <div class="row bottom-input">
    <b-form-group
      v-if="customDesignType && isSelectedCustomDesignType"
      label-class="font-weight-500 px-1"
      class="col mb-2 inner-label-input"
      :class="customItemList && ((customDesignType && customItemList.length) || (!customDesignType && customItemList.length > 1)) ? 'pr-1' : ''"
    >
      <div slot="label">
        {{ $t('Select design') }}
      </div>
      <b-form-select
        id="bottomCustomDesignSelect"
        ref="bottomCustomDesignSelect"
        :value="customDesign"
        size="lg"
        class="text-uppercase bottom-select"
        :class="customItemList && ((customDesignType && customItemList.length) || (!customDesignType && customItemList.length > 1)) ? 'pr-1' : ''"
        :disabled="loadingChangeCustomDesign"
        @input="value => $emit('updateCustomDesign', value)"
      >
        <b-form-select-option
          v-for="item in customDesignArray[customDesignType]"
          :key="item"
          :value="item"
        >
          {{ item }}
        </b-form-select-option>
      </b-form-select>
    </b-form-group>

    <b-form-group
      v-if="!isSelectedCustomDesignType && selectedCustom.data.type==='i-text'"
      label-class="text-capitalize font-weight-500 px-1"
      class="col mb-2 inner-label-input"
      :class="customItemList && ((customDesignType && customItemList.length) || (!customDesignType && customItemList.length > 1)) ? 'pr-1' : ''"
    >
      <div slot="label">
        <span>
          {{ selectedCustom.data.name.replaceAll('_', ' ') }}
        </span>
        <span>
          ( {{ selectedCustom.data.text.length }} | {{ selectedCustom.data.maxLength || 16 }})
        </span>
        <span>
          *
        </span>
      </div>
      <b-form-input
        id="bottomCustomTextInput"
        ref="bottomCustomTextInput"
        :value="selectedCustom.data.text"
        size="lg"
        class="px-1"
        :maxlength="selectedCustom.data.maxLength || 16"
        :state="optionError==='customText' && !selectedCustom.data.text.length ? false : null"
        :placeholder="selectedCustom.data.placeholder"
        @input="value=>$emit('updateCanvasData', selectedCustom, value)"
        @focus="$emit('changeCurrentDesign', selectedCustom.design); $emit('focusInput')"
        @blur="$emit('updateDesignQuery', selectedCustom); $emit('blurInput')"
        @keydown.enter="nextCustomText"
      />
    </b-form-group>

    <b-form-group
      v-else-if="!isSelectedCustomDesignType && selectedCustom.data.type==='image'"
      label-class="text-capitalize font-weight-500 px-1"
      class="col mb-2 inner-label-input"
      :class="customItemList && ((customDesignType && customItemList.length) || (!customDesignType && customItemList.length > 1)) ? 'pr-1' : ''"
    >
      <b-button
        v-show="fileUpload"
        variant="outline-custom"
        size="lg"
        class="border mr-2"
        @click="$emit('changeCurrentDesign', selectedCustom.design); $refs.modalEditCustomImage.isShowModal = true"
      >
        <i class="icon-sen-square-edit-outline" />
      </b-button>
      <div
        class="form-control-file-bottom-button"
        :class="optionError==='customImage' ? 'require-input' : ''"
      >
        <input
          id="fileInputCustomImage"
          ref="fileInputCustomImage"
          type="file"
          accept="image/*"
          :disabled="isUploadFile"
          @change="updateFile"
          @click="$emit('changeCurrentDesign', selectedCustom.design)"
        >
        <label for="fileInputCustomImage" class="d-flex" :class="{disabled: isUploadFile}">
          <div class="text-box col text-overflow-hidden">{{ fileUpload && fileUpload.name || $t('Choose a file or drop it here...') }}</div>
          <div class="button-box col-auto">
            <b-spinner v-if="isUploadFile" small />
            <i v-else class="icon-sen-camera" />
            <span> {{ $t('Upload') }}</span>
          </div>
        </label>
      </div>
    </b-form-group>

    <div v-if="customItemList && ((customDesignType && customItemList.length) || (!customDesignType && customItemList.length > 1))" class="col-auto pl-1 next-preview-customtext-btn">
      <b-button
        size="lg"
        variant="outline-custom"
        class="border py-0 px-2"
        block
        @click="nextCustomText"
      >
        <i :class="(customDesignType && isSelectedCustomDesignType) || (!customDesignType && selectedCustom===customItemList[customItemList.length-1]) ? 'icon-sen-skip-previous' : 'icon-sen-skip-next' " />
      </b-button>
    </div>
  </div>
</template>
<script>
/* eslint-disable vue/require-prop-types */
export default {
  components: {
  },
  props: [
    'isModal', 'optionError', 'selectedCustom', 'customItemList',
    'currentDesign', 'isUploadFile', 'fileUpload',
    'customDesignType', 'customDesign', 'loadingChangeCustomDesign', 'customDesignArray'
  ],
  data () {
    return {
      isSelectedCustomDesignType: false
    }
  },
  methods: {
    updateFile,
    nextCustomText
  }
}

function updateFile (e) {
  if (e.srcElement.files[0]) { this.$emit('updateCanvasData', this.selectedCustom, e.srcElement.files[0]) }
}

function nextCustomText () {
  if (this.selectedCustom.data.type === 'i-text' && !this.selectedCustom.data.text) {
    this.trackerTimeoutoptionError = setTimeout(() => {
      this.optionError = ''
    }, 2000)
    this.optionError = 'customText'
    this.$refs.bottomCustomTextInput.$el.focus()
    return
  }
  const index = this.customItemList.indexOf(this.selectedCustom)
  if (index !== -1 && index < this.customItemList.length - 1) {
    this.$emit('updateSelectedCustom', this.customItemList[index + 1])
  } else if (!this.customDesignType) {
    this.$emit('updateSelectedCustom', this.customItemList[0])
  } else if (this.isSelectedCustomDesignType) {
    this.$emit('updateSelectedCustom', this.customItemList[0])
    this.isSelectedCustomDesignType = false
  } else {
    this.isSelectedCustomDesignType = true
  }
}
</script>

<style lang="scss">
.bottom-input .form-group.inner-label-input {
  .col-form-label {
    color: gray;
    font-size: 14px;
    padding: 0 1rem;
    position: absolute;
  }

  input.form-control {
    height: 3rem;
    color: black;
    font-size: 15px;
    padding-top: 1.6rem;
  }

  select.bottom-select {
    position: relative;
    background-color: transparent;
    height: 3rem;
    color: black;
    font-size: 15px;
    padding-left: 0.3rem;
    padding-top: 1.3rem;
    z-index: 100;
  }
}
</style>
