<template>
  <b-form-group
    label-class="text-capitalize font-weight-500"
  >
    <div slot="label">
      <span v-show="fileUpload" class="text-info cursor-pointer" @click="$emit('editDesign')"> {{ $t('Edit position') }}</span>
    </div>
    <div
      class="form-control-file"
      :class="optionError==='customImage' ? 'require-input' : ''"
    >
      <input
        :id="isModal ? `fileInputModal-${name}-${index}` : `fileInput-${name}-${index}`"
        type="file"
        accept="image/*"
        :disabled="isUploadFile"
        @change="updateFile"
        @click="$emit('selectDesign')"
      >
      <label :for="isModal ? `fileInputModal-${name}-${index}` : `fileInput-${name}-${index}`" class="d-flex" :class="{disabled: isUploadFile}">
        <div class="text-box col text-overflow-hidden">{{ fileUpload && fileUpload.name || $t('Choose a file or drop it here...') }}</div>
        <div class="button-box col-auto">
          <b-spinner v-if="isUploadFile" small />
          <i v-else class="icon-sen-camera" />
          <span> {{ $t('Upload') }}</span>
        </div>
      </label>
    </div>
  </b-form-group>
</template>

<script>
export default {
  name: 'CustomImageItem',
  // eslint-disable-next-line vue/require-prop-types
  props: ['index', 'isModal', 'customImage', 'optionError', 'isUploadFile', 'fileUpload', 'name'],
  methods: {
    updateFile
  }
}

function updateFile (e) {
  if (e.srcElement.files[0]) { this.$emit('updateCanvasData', e.srcElement.files[0]) }
}
</script>

<style lang="scss">
.form-control-file {

  &.require-input label div{
    border-color: red;
  }

  input {
    display: none;

  }

  label {
    background: white;
    cursor: pointer;

    div {
      padding: 6px 12px;
      border: solid 1px #ced4da;
      color: #8F8F8F;
    }

    .button-box {
      background-color: #E9ECEF;
      border-left: none;
    }

    &.disabled {
      cursor: not-allowed;
      background-color: rgb(241, 241, 241);
    }
  }
}
</style>
