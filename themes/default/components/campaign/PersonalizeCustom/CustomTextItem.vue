<template>
  <b-form-group
    label-class="text-capitalize font-weight-500"
  >
    <div slot="label">
      <span>{{ customText.name.replaceAll('_', ' ') }}</span>
      <!-- <span>( {{ customText.text.length }} | {{ customText.maxLength || 16 }})</span> -->
      <span>
        *
      </span>
      <span v-show="!customText.text.length" class="text-danger"> {{ $t('Required') }}</span>
    </div>
    <div class="position-relative">
      <b-form-input
        :name="isModal ? `customtextModal_${customText.name}` : `customtext_${customText.name}`"
        :maxlength="customText.maxLength || 16"
        :state="optionError==='customText' && !customText.text.length ? false : null"
        :class="optionError==='customText' && !customText.text.length ? 'require-input' : ''"
        :value="customText.text"
        :placeholder="customText.placeholder"
        @input="value=>updateText(value)"
        @focus="$emit('selectDesign')"
        @blur="$emit('updateDesignQuery')"
      />
      <span class="custom-text-count text-danger">{{ customText.text.length }} / {{ customText.maxLength || 16 }}</span>
    </div>
  </b-form-group>
</template>
<script>
export default {
  components: {
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['index', 'isModal', 'customText', 'optionError'],
  methods: {
    updateText
  }
}

function updateText (value) {
  this.$emit('updateCanvasData', value)
}
</script>

<style lang="scss">
.custom-text-count {
  position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #ddd;
    font-size: 80%;
    z-index: 0;
}
</style>
