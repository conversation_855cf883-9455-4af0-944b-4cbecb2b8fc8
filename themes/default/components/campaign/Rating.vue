<template>
  <a
    v-if="reviewCount > 0 && reviewDisplay !== 'disable'"
    :href="$route.fullPath + '#productReviewBox'"
    class="top-summary-review d-flex align-items-center"
    @click.prevent="moveToProductReviewBox"
  >
    <b-form-rating
      variant="warning"
      :value="averageRating"
      readonly
      no-border
      inline
      size="lg"
      class="h-auto p-0"
    />
    <span class="top-summary-review-text" style="text-transform: lowercase;">{{ reviewCount }} {{ $t('Reviews') }}</span>
  </a>
</template>
<script>

export default {
  name: 'CampaignRating',
  components: {
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['averageRating', 'reviewCount', 'reviewDisplay'],
  methods: {
    moveToProductReviewBox () {
      document.getElementById('productReviewBox').scrollIntoView()
    }
  }
}

</script>

<style lang="scss">
.top-summary-review {
  .form-control:focus {
    box-shadow: unset;
  }
  span.top-summary-review-text {
    padding-top: 0.2rem;
  }
}

.b-rating {
  margin: 0 -0.25rem
}
</style>
