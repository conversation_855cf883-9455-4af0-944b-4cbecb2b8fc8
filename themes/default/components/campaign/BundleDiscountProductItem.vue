<template>
  <div v-if="product && index < 3" :key="forceUpdate" class="product-bundle-item2 my-1 row">
    <div class="col-6 col-md-8">
      <b-form-checkbox
        :checked="product.isSelected"
        @change="$emit('updateBundleProduct', product, {'isSelected': !product.isSelected});"
      >
        <div class="text-capitalize text-overflow-hidden">
          {{ product.name || '' }}
        </div>
        <div>
          <span v-if="variantInStock && variantInStock.out_of_stock" class="text-danger font-weight-500">
            {{ $t('Out of stock') }}
          </span>
          <span v-else>
            {{ $formatPrice(((variantInStock && variantInStock.price) || product.price) + Number(((product.customFeePrice || 0) * ((product.customOptionGroupNumbers || 1) - 1))), product.currency_code) }}
          </span>
        </div>
      </b-form-checkbox>
    </div>
    <div v-if="product.existVariantList && product.existVariantList.length" class="col-6 col-md-4">
      <b-dropdown
        variant="outline-custom"
        size="sm"
        block
        lazy
        :right="true"
        toggle-class="border text-overflow-hidden text-uppercase pr-3"
        :text="getLabelVariantKey(currentVariantKey) || ''"
      >
        <b-dropdown-item
          v-for="(variant, variantIndex) in product.existVariantList"
          :key="variantIndex"
          :active="currentVariantKey && variant === currentVariantKey"
          class="text-uppercase"
          @click="updateOption(variant)"
        >
          {{ getLabelVariantKey(variant) }}
        </b-dropdown-item>
      </b-dropdown>
    </div>
  </div>
</template>
<script>
export default {
  components: {
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['index', 'product', 'forceUpdate'],
  data () {
    return {
      currentProductSelected: true,
      currentVariantKey: '',
      variantInStock: {},
      refetchTimes: 0
    }
  },
  computed: {
    productHasOneColor () {
      return !(this.product.options.color?.length > 1)
    }
  },
  watch: {
    product () {
      this.handleCurrentVariants()
    }
  },
  created: handleCurrentVariants,
  methods: {
    updateOption,
    handleCurrentVariants,
    getLabelVariantKey
  }
}

function updateOption (variant) {
  let optionsListFull = this.product.options
  if (typeof optionsListFull === 'string') {
    optionsListFull = JSON.parse(optionsListFull)
  }
  const optionKeys = Object.keys(optionsListFull)
  const optionValue = variant.split('-')
  const currentVariant = this.product.variantsList.find(item => item.variant_key === variant)
  const currentOptions = {}
  optionKeys.forEach((item, index) => {
    currentOptions[item] = optionValue[index].replace(/_/g, '-')
  })
  this.currentVariantKey = variant
  this.$emit('updateBundleProduct',
    this.product,
    {
      currentOptions,
      currentVariant,
      currentVariantKey: variant
    })
  this.currentVariantKey = this.product.currentVariantKey
}

function handleCurrentVariants () {
  if (this.product?.currentVariant?.out_of_stock) {
    const fixedOptions = this.product.currentOptions?.size
    let variantsInStock = null
    if (fixedOptions) {
      const regex = new RegExp(`-${fixedOptions}$`, 'i')
      variantsInStock = this.product.variantsList.find((variant) => {
        return regex.test(variant.variant_key) && variant.out_of_stock === 0
      })
    } else {
      variantsInStock = this.product.variantsList.find((variant) => {
        return variant.out_of_stock === 0
      })
    }

    if (variantsInStock) {
      this.currentVariantKey = variantsInStock?.variant_key?.replace(/_/g, ' ')?.replace(/-/g, ' / ') ?? ''
      this.variantInStock = variantsInStock
      return
    }
    if (this.refetchTimes < 3) {
      this.$emit('resetBundleProduct', this.product.id)
      this.refetchTimes += 1
    }
  }
  this.currentVariantKey = this.product.currentVariantKey
  this.variantInStock = this.product.currentVariant
}

function getLabelVariantKey (variantKey) {
  if (this.productHasOneColor) {
    const color = this.product.options?.color[0]
    if (color) {
      variantKey = variantKey.replace(new RegExp(`${color}[_-]?| / `, 'g'), '')
    }
  }
  return variantKey.replace(/_/g, ' ').replace(/-/g, ' / ')
}
</script>

<style lang="scss">
.product-item {
  &.active {
    border: 1px solid var(--primary-color)
  }

  img,
  .spinner {
    height: 100%;
    width: 100%;
  }

  min-width: 80px;
  max-width: 80px;
  height: 100px;
}

.buyitnow-button,
.quantity-select {
  border: 1px solid #ced4da !important;
  min-width: 70px;
}

.campaign-info {
  .nav-item:first-child{
    .nav-link{
      padding-left: 0;
    }
  }
 .nav-link {
   color: #C6C6C6;
   &.active {
     color: var(--primary-color);
   }
   @media (max-width: 767px) {
     padding:0 5px;
   }
 }
}

.plus-icon {
  min-height: 25px;
  min-width: 25px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-bundle-item {
  &.active {
    border: 1px solid var(--primary-color)
  }

  .product-bundle-item,
  .spinner {
    height: 100%;
    width: 100%;
  }

  min-width: 100px;
  max-width: 100px;
  height: 125px;
}
</style>
