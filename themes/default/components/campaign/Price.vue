<template>
  <div>
    <span class="h4 text-color-primary">{{ $formatPrice(currentPrice, currentProduct.currency_code) }}</span>
    <del v-if="isShowOldPrice" class="ml-3">{{ $formatPrice(currentOldPrice, currentProduct.currency_code) }}</del>
  </div>
</template>
<script>
export default {
  name: 'CampaignPrice',
  // eslint-disable-next-line vue/require-prop-types
  props: ['currentProduct', 'currentPrice', 'currentOldPrice'],
  computed: {
    isShowOldPrice () {
      return this.$store.state.storeInfo.store_type !== 'google_ads' &&
        !this.$store.state.storeInfo.disable_pre_discount &&
        this.currentOldPrice
    }
  }
}

</script>

<style lang="scss">
</style>
