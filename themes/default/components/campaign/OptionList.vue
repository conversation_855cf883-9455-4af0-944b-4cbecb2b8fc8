<template>
  <div class="list-options">
    <template v-for="(item, index) in Object.keys(optionList)">
      <div v-if="optionList[item]?.length >= 2" :key="index" class="mb-1">
        <div
          class="d-flex align-items-center"
          :class="{
            'justify-content-between': $store.state.storeInfo.option_label_enable || isAutomaticEnableOptionLabel,
            'justify-content-end': !$store.state.storeInfo.option_label_enable && !isAutomaticEnableOptionLabel
          }"
        >
          <span v-if="$store.state.storeInfo.option_label_enable || isAutomaticEnableOptionLabel" class="font-weight-bold text-capitalize pl-1">
            {{ $t( item.replace('_', ' ') ) }}
          </span>
          <div v-if="item === 'size' && currentProduct && currentProduct.attributes && JSON.parse(currentProduct.attributes).size_chart && sizeGuideList && sizeGuideList.length" class="cursor-pointer text-info" @click="$emit('openSizeGuild')">
            <span class="font-weight-500">
              {{ $t('Size guide') }}
            </span>
            <span>
              <i class="icon-sen-ruler" />
            </span>
          </div>
        </div>
        <div class="w-100 option-block overflow-auto d-flex flex-md-wrap">
          <template v-if="item === 'color'">
            <color-item
              v-for="(value, optionIndex) in optionList[item]"
              :key="`${item}-${optionIndex}`"
              :color="value"
              size="xl"
              :title="value"
              :active="value.replace(/ /g, '-') === currentOptions[item]"
              @click.native="updateOption(item, value)"
            />
          </template>
          <template v-else>
            <option-item
              v-for="(value, optionIndex) in optionList[item]"
              :key="`${item}-${optionIndex}`"
              :class="{active: value.replace(/ /g, '-') === currentOptions[item]}"
              :value="value"
              @click.native="updateOption(item, value)"
            />
          </template>
        </div>
      </div>
    </template>
  </div>
</template>
<script>

/* eslint-disable vue/require-prop-types */
import ColorItem from '~/themes/default/components/common/ColorItem'
import OptionItem from '~/themes/default/components/common/OptionItem'
import sizeguide from '~/mixins/sizeguide'

export default {
  components: {
    ColorItem,
    OptionItem
  },
  mixins: [sizeguide],
  props: [
    'currentProduct', 'optionList', 'currentOptions'],
  data () {
    return {
      showListingProduct: false,
      isShowAlertInput: false,
      isShowAlertInput2: false
    }
  },
  computed: {
    isAutomaticEnableOptionLabel () {
      return this.currentProduct && (this.currentProduct.system_type === 'mockup' || this.currentProduct?.full_printed === 5)
    }
  },
  methods: {
    updateOption
  }
}

function updateOption (key, value) {
  if (!key || !value) {
    return
  }
  const option = {}
  option[key] = value && value.replace(/ /g, '-')
  this.$emit('updateOption', option)
}
</script>

<style lang="scss">
</style>
