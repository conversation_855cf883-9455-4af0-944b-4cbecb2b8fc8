<template>
  <div
    v-if="isDropdownType"
    class="product-list my-2"
  >
    <b-dropdown
      variant="outline-custom"
      block
      lazy
      toggle-class="border text-overflow-hidden text-capitalize pr-3"
      menu-class="w-100"
      :text="products.find((product) => product.id === currentProduct.id).name"
    >
      <b-dropdown-item
        v-for="(product, index) in products"
        :key="index"
        @click="product.id !== currentProduct.id ? $emit('updateOption', {product: $toSlug(product.name)}):''; showListingProduct = true"
      >
        {{ product.name }}
      </b-dropdown-item>
    </b-dropdown>
  </div>
  <div v-else class="product-list overflow-auto d-flex flex-md-wrap my-2">
    <div
      v-for="( product, index ) in products"
      :key="index"
      sp-action="change_product"
      class="product-item m-1 shadow-hover-1 cursor-pointer border-radius-none overflow-hidden"
      :class="{
        active: product.id === currentProduct.id,
        'd-md-none': (index > 3) && !showListingProduct
      }"
      @click="product.id !== currentProduct.id ? $emit('updateOption', {product: $toSlug(product.name)}):''; showListingProduct = true"
    >
      <product-img :path="isShowCampaignThumbnailInsteadOfProduct ? campaign.thumb_url : product.thumb_url" :alt="product.name" :color="color(currentOptions?.color)" />
    </div>
    <div
      v-if="products.length > 4"
      class="product-item cursor-pointer m-1 shadow-hover-1 d-none d-md-flex justify-content-center align-items-center display-4 border text-gray"
      :class="{active: showListingProduct }"
      @click="showListingProduct = !showListingProduct"
    >
      <i :class="showListingProduct ? 'icon-sen-minus' : 'icon-sen-plus'" />
    </div>
  </div>
</template>
<script>
import { isShowCampaignThumbnailInsteadOfProduct } from '~/helpers/campaign'

export default {
  name: 'CampaignProductList',
  components: {
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['currentProduct', 'products', 'isDropdownType', 'currentOptions', 'campaign'],
  data () {
    return {
      showListingProduct: false
    }
  },
  computed: {
    isShowCampaignThumbnailInsteadOfProduct () {
      return isShowCampaignThumbnailInsteadOfProduct(this.campaign)
    }
  },
  methods: {
    moveToProductReviewBox () {
      document.getElementById('productReviewBox').scrollIntoView()
    },
    color (optionColor) {
      if (optionColor) {
        return this.$colorVal(optionColor.replace(/-/g, ' '))
      }
      return ''
    }
  }
}

</script>

<style lang="scss">
.product-item {
  &.active {
    border: 1px solid var(--primary-color)
  }

  img,
  .spinner {
    height: 100%;
    width: 100%;
  }

  min-width: 80px;
  max-width: 80px;
  height: 100px;
}
</style>
