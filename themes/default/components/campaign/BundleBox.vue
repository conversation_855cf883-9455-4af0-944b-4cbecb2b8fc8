<template>
  <div>
    <p class="text-center">
      <span class="font-weight-500 text-center"><strong>{{ $t('Frequently bought together') }}</strong> </span>
    </p>
    <div class="d-flex overflow-auto">
      <div class="product-bundle-item m-1 shadow-hover-1">
        <product-img :path="currentProduct.thumb_url || filterImages[0] && filterImages[0].file_url" :alt="currentProduct.name" :color="currentOptions.color ? $colorVal(currentOptions.color.replace(/-/g, ' ')) : ''" />
      </div>
      <template v-for="( product, index ) in bundleDiscount.products">
        <div
          v-if="index < 3"
          :key="index"
          class="d-flex align-items-center"
        >
          <div class="plus-icon">
            <i class="icon-sen-plus" />
          </div>
          <div
            class="product-bundle-item m-1 shadow-hover-1 cursor-pointer"
            @click="$emit('openBundleProduct', product)"
          >
            <product-img
              :key="forceUpdate"
              :path="product.thumb_url"
              :alt="product.name"
              :color="product.currentOptions && product.currentOptions.color ? $colorVal(product.currentOptions.color.replace(/-/g, ' ')) : ''"
            />

            <personalize-tag v-if="product && product.personalized" class="size-sm" />
          </div>
        </div>
      </template>
    </div>
    <bundle-discount-product-item
      v-for="( product, index ) in bundleDiscount.products"
      :key="index"
      :force-update="forceUpdate"
      :index="index"
      :product="product"
      @updateBundleProduct="(product, value) => $emit('updateBundleProduct', product, value)"
      @resetBundleProduct="(productId) => $emit('resetBundleProduct', productId)"
    />
    <hr>
    <div class="d-flex justify-content-between">
      <div v-if="saveBundleDiscount">
        <span class="text-uppercase">{{ $t('Total') }}: </span><del v-if="$store.state.storeInfo.store_type !== 'google_ads'" class="font-weight-600">{{ $toLocalePrice(currentPrice + totalBundleDiscount) }}</del>
      </div>
      <div><span class="text-uppercase">{{ $t('Total price') }}: </span><span class="font-weight-600 text-custom">{{ $toLocalePrice(currentPrice + totalBundleDiscount - saveBundleDiscount) }}</span></div>
    </div>
    <div v-if="saveBundleDiscount" class="d-flex justify-content-end py-3">
      <b-badge variant="info" class="font-weight-500 py-1">
        {{ $t('Saving') }} {{ $toLocalePrice(saveBundleDiscount) }}
      </b-badge>
    </div>
    <div>
      <b-button
        :key="forceUpdate"
        block
        size="lg"
        variant="custom"
        :disabled="!!(currentVariant && currentVariant.out_of_stock) || isLoadingAddToCart || !bundleDiscount.products.find((item, index) => index < 3 && item.isSelected) || !allowCustomCampaignAddToCart"
        class="text-uppercase font-weight-600 border-radius-none"
        @click="$emit('addToCart', 3)"
      >
        <loading-dot v-if="isLoadingAddToCart" />
        <span v-else><i class="font-2xl icon-sen-cart-plus mr-1" />{{ $t('Add all to cart') }}</span>
      </b-button>
    </div>
  </div>
</template>
<script>

import BundleDiscountProductItem from '~/themes/default/components/campaign/BundleDiscountProductItem'

export default {
  components: {
    BundleDiscountProductItem
  },
  /* eslint-disable vue/require-prop-types */
  props: ['filterImages', 'currentProduct', 'currentOptions', 'isLoadingAddToCart', 'currentPrice', 'currentVariant',
    'bundleDiscount', 'totalBundleDiscount', 'saveBundleDiscount', 'bundleProductVariant', 'forceUpdate', 'allowCustomCampaignAddToCart']

}

</script>

<style lang="scss">

.plus-icon {
  min-height: 25px;
  min-width: 25px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-bundle-item {
  &.active {
    border: 1px solid var(--primary-color)
  }

  .product-img,
  .spinner {
    height: 100%;
    width: 100%;
  }

  position: relative;
  min-width: 112px;
  max-width: 112px;
  height: 138px;

}

.product-bundle-item2 {
  .custom-control-label {
    width: 100%;
  }
}

.bundle-modal-image {
  img {
    width: 100%;
    max-width: 450px;
  }
}

</style>
