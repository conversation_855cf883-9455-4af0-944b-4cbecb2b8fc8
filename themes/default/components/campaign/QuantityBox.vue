<template>
  <div class="mb-3">
    <!-- quantity add to cart -->
    <p>
      <strong>{{ $t('Quantity') }} </strong>
      <span v-if="currentVariant && currentVariant.out_of_stock" class="text-danger font-weight-500">
        {{ $t('Out of stock') }}
      </span>
    </p>
    <div class="row mt-1">
      <div class="col-auto pr-1 d-flex align-items-center">
        <b-form-select
          :disabled="!!(currentVariant && currentVariant.out_of_stock)"
          size="lg"
          :value="quantity"
          class="cursor-pointer"
          data-test-id="quantity-box"
          @input="(value)=>$emit('updateQuantity', value )"
        >
          <b-form-select-option
            v-for="item in 50"
            :key="item"
            :value="item"
          >
            {{ item }}
          </b-form-select-option>
        </b-form-select>
      </div>
      <div class="col pl-1">
        <b-button
          v-if="!isModal"
          id="addToCart"
          :disabled="isLoadingAddToCart || loadingChangeCustomDesign || !!(currentVariant && currentVariant.out_of_stock) || !allowCustomCampaignAddToCart"
          block
          size="lg"
          variant="custom"
          class="position-relative px-1 addtocart-button border text-uppercase font-weight-600"
          data-test-id="campaign-add-to-cart"
          dusk="add-to-cart-button"
          @click="$emit('addToCart', 1)"
        >
          <loading-dot v-if="isLoadingAddToCart || loadingChangeCustomDesign" variant="light" />
          <span v-else><i class="font-2xl icon-sen-cart-plus mr-1" />{{ $t('Add to cart') }} </span>
        </b-button>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  components: {
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ['isModal', 'currentVariant', 'quantity', 'isLoadingAddToCart', 'loadingChangeCustomDesign', 'allowCustomCampaignAddToCart']

}

</script>

<style lang="scss">
.buyitnow-button,
.quantity-select {
  border: 1px solid #ced4da !important;
  min-width: 70px;
}
</style>
