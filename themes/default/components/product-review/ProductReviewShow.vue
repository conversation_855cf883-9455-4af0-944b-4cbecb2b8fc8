<template>
  <div v-if="reviewSummary.summary.review_count > 0" id="productReviewBox" class="my-5">
    <div class="row review-header">
      <div class="col-lg-8">
        <h4 class="text-uppercase">
          {{ $t('Product reviews') }}
        </h4>
      </div>
      <div class="col-lg-4">
        <b-button v-b-modal="'write-a-review-modal'" size="md" variant="outline-success" class="float-right text-capitalize">
          {{ $t('Write a review') }}
        </b-button>
      </div>
    </div>
    <div class="row mt-3 mb-4">
      <div class="col-lg-4 rating-box">
        <div class="average-rating">
          <div class="rt-header d-flex">
            <h2>
              {{ averageRating }}
            </h2>
            <div class="rating-nowrap d-flex flex-column justify-content-center">
              <b-form-rating
                class="rating-readonly p-0"
                :value="averageRating"
                readonly
                no-border
                size="sm"
                variant="warning"
              />
              <span class="rating-num">{{ reviewCount }} {{ $t('Reviews') }}</span>
            </div>
          </div>
          <div class="rt-star" data-star="5">
            <span>
              <b-form-rating
                class="rating-readonly p-0"
                value="5"
                readonly
                no-border
                size="sm"
                variant="warning"
              />
            </span>
            <b-progress :value="fiveStartCount / reviewCount * 100" :max="100" class="ps-progress" />
            <span class="ps-num">{{ fiveStartCount }}</span>
          </div>
          <div class="rt-star" data-star="4">
            <span>
              <b-form-rating
                class="rating-readonly p-0"
                value="4"
                readonly
                no-border
                size="sm"
                variant="warning"
              />
            </span>
            <b-progress :value="fourStarCount / reviewCount * 100" :max="100" class="ps-progress" />
            <span class="ps-num">{{ fourStarCount }}</span>
          </div>
          <div class="rt-star" data-star="3">
            <span>
              <b-form-rating
                class="rating-readonly p-0"
                value="3"
                readonly
                no-border
                size="sm"
                variant="warning"
              />
            </span>
            <b-progress :value="reviewSummary.summary.three_star_count / reviewCount * 100" :max="100" class="ps-progress" />
            <span class="ps-num">{{ reviewSummary.summary.three_star_count }}</span>
          </div>
          <div class="rt-star" data-star="2">
            <span>
              <b-form-rating
                class="rating-readonly p-0"
                value="2"
                readonly
                no-border
                size="sm"
                variant="warning"
              />
            </span>
            <b-progress :value="reviewSummary.summary.two_star_count / reviewCount * 100" :max="100" class="ps-progress" />
            <span class="ps-num">{{ reviewSummary.summary.two_star_count }}</span>
          </div>
          <div class="rt-star" data-star="1">
            <span>
              <b-form-rating
                class="rating-readonly p-0"
                value="1"
                readonly
                no-border
                size="sm"
                variant="warning"
              />
            </span>
            <b-progress :value="reviewSummary.summary.one_star_count / reviewCount * 100" :max="100" class="ps-progress" />
            <span class="ps-num">{{ reviewSummary.summary.one_star_count }}</span>
          </div>
        </div>
      </div>
      <div class="col-lg-8 d-flex flex-column justify-content-between">
        <div class="mt-4">
          <h5 v-if="reviewSummary.files && reviewSummary.files.length > 0" class="text-capitalize">
            {{ $t('All Image') }} ({{ reviewSummary.files.length }})
          </h5>
          <product-review-image-box v-if="reviewSummary.files && reviewSummary.files.length > 0" :files="reviewSummary.files" :name="campaignName" />
        </div>
        <div class="mt-4">
          <h5 class="text-title d-inline-block">
            {{ $t('Filter by') }}:
          </h5>
          <div class="d-inline-block">
            <span :class="filter === 'helpful' ? 'active' : ''" class="filter-review badge badge-pill rounded-pill mb-2 mb-md-0" @click="$emit('changeFilter', 'helpful')">{{ $t('Helpful') }}</span>
            <span :class="filter === 'newest' ? 'active' : ''" class="filter-review badge badge-pill rounded-pill mb-2 mb-md-0" @click="$emit('changeFilter', 'newest')">{{ $t('Newest') }}</span>
            <span :class="filter === 'five_star' ? 'active' : ''" class="filter-review badge badge-pill rounded-pill mb-2 mb-md-0" @click="$emit('changeFilter', 'five_star')">5<i class="icon-sen-star" /></span>
            <span :class="filter === 'four_star' ? 'active' : ''" class="filter-review badge badge-pill rounded-pill mb-2 mb-md-0" @click="$emit('changeFilter', 'four_star')">4<i class="icon-sen-star" /></span>
            <span :class="filter === 'three_star' ? 'active' : ''" class="filter-review badge badge-pill rounded-pill mb-2 mb-md-0" @click="$emit('changeFilter', 'three_star')">3<i class="icon-sen-star" /></span>
            <span :class="filter === 'two_star' ? 'active' : ''" class="filter-review badge badge-pill rounded-pill mb-2 mb-md-0" @click="$emit('changeFilter', 'two_star')">2<i class="icon-sen-star" /></span>
            <span :class="filter === 'one_star' ? 'active' : ''" class="filter-review badge badge-pill rounded-pill mb-2 mb-md-0" @click="$emit('changeFilter', 'one_star')">1<i class="icon-sen-star" /></span>
          </div>
        </div>
      </div>
    </div>
    <b-overlay :show="loading" rounded="sm">
      <div v-if="reviews.total > 0" class="review-detail-box">
        <div v-for="(r, i) in reviews.data" :key="i" class="review-item">
          <hr>
          <div class="row my-4">
            <div class="col-lg-4">
              <div class="customer-box d-flex align-items-center">
                <div class="avatar mr-3">
                  <img :src="r.avatar_url || `${$config.publicPath}/images/default-user-icon.webp`" onerror="this.src = '/images/default-user-icon.webp'" loading="lazy" :alt="$t(r.customer_name)" class="img-fluid rounded-circle h-100 border-danger">
                </div>
                <div class="info">
                  <h6 class="font-weight-500 mb-1 text-capitalize">
                    {{ r.customer_name }}
                  </h6>
                  <p class="purchased-in-text mb-0 text-danger font-weight-600">
                    {{ $t('Purchased in') }} {{ r.customer_location }}
                  </p>
                </div>
              </div>
            </div>
            <div class="col-lg-8">
              <div class="customer-rating-star mt-2 mt-lg-0">
                <b-form-rating
                  class="rating-readonly p-0"
                  :value="r.average_rating"
                  readonly
                  no-border
                  size="md"
                  variant="warning"
                />
              </div>
              <div class="attr-product d-flex align-items-baseline mt-2 mb-md-1 flex-wrap">
                <nuxt-link v-if="r.product_url" :to="r.product_url" target="_blank" class="badge badge-pill badge-outline-success mr-2 mb-2 mb-md-0">
                  {{ r.product_name }}
                </nuxt-link>
                <span v-else class="badge badge-pill badge-outline-success mr-2 mb-2 mb-md-0">
                  {{ r.product_name }}
                </span>
                <span v-if="r.product_size" class="badge badge-pill badge-outline-success mr-2 mb-2 mb-md-0">{{ $t('Size') }}: <span class="text-uppercase">{{ r.product_size }}</span></span>
                <span v-if="r.product_color" class="badge badge-pill badge-outline-success d-flex align-items-center mb-2 mb-md-0">{{ $t('Color') }}: <span class="color ml-2" :style="`background: ${$colorVal(r.product_color)}`" /></span>
              </div>
              <p class="review-content pt-1 pb-2 mb-0">
                {{ r.comment }}
              </p>
              <product-single-review-image-box v-if="r.files.length > 0" :name="r.product_name" :files="r.files" />
              <p class="reviewed-at mt-3 mb-0">
                {{ $t('Reviewed on') }} {{ r.created_at }}
              </p>
            </div>
          </div>
        </div>
        <div v-if="reviews.total > reviews.per_page" class="review-pagination d-flex justify-content-center justify-content-md-end">
          <b-pagination
            :value="reviews.current_page"
            :per-page="reviews.per_page"
            :total-rows="reviews.total"
            pills
            @change="page => { $emit('changePage', page) }"
          />
        </div>
      </div>
      <div v-else class="review-detail-box text-center">
        <span>{{ $t('There are no reviews yet') }}</span>
      </div>
    </b-overlay>
    <b-modal
      id="write-a-review-modal"
      :ref="'write-a-review-modal'"
      centered
      size="md"
      :title="$t('Enter your order information')"
      @hidden="resetForm"
    >
      <b-overlay :show="submitting" rounded="sm">
        <b-form ref="form">
          <b-alert
            v-for="(m, i) in errorMessages"
            :key="i"
            ref="warningMessage"
            variant="danger"
            dismissible
            fade
            show
          >
            <strong>{{ $t(m[0]) }}</strong>
          </b-alert>
          <b-alert
            v-if="error"
            variant="danger"
            fade
            show
          >
            <strong>{{ $t("Your order does not exist.") }}</strong>
          </b-alert>
          <div class="col-12 px-0">
            <div class="form-group">
              <label for="order_email">{{ $t('Email') }} <span class="text-danger">*</span></label>
              <b-form-input
                id="order_email"
                v-model="form.email"
                type="email"
                name="order_email"
                class="form-control"
                required
                placeholder="<EMAIL>"
              />
            </div>
          </div>
          <div class="col-12 my-2 px-0">
            <div class="form-group">
              <label for="order_number">{{ $t('Order number') }} <span class="text-danger">*</span></label>
              <b-form-input
                id="order_number"
                v-model="form.order_number"
                type="text"
                name="order_number"
                class="form-control"
                required
                placeholder="XX-12345678"
              />
            </div>
          </div>
        </b-form>
      </b-overlay>
      <template #modal-footer>
        <div class="w-100">
          <b-button
            type="submit"
            variant="success"
            size="md"
            class="float-right"
            :disabled="submitting"
            @click="submit()"
          >
            {{ $t('Write review') }}
          </b-button>
        </div>
      </template>
    </b-modal>
  </div>
</template>
<script>
import _ from 'lodash'
import ProductReviewImageBox from '~/themes/default/components/product-review/ProductReviewImageBox'
import ProductSingleReviewImageBox from '~/themes/default/components/product-review/ProductSingleReviewImageBox'

export default {
  components: { ProductSingleReviewImageBox, ProductReviewImageBox },
  // eslint-disable-next-line vue/require-prop-types
  props: ['reviewSummary', 'reviews', 'loading', 'campaignName', 'filter'],
  data () {
    return {
      submitting: false,
      error: false,
      form: {
        email: '',
        order_number: '',
      },
      errorMessages: []
    }
  },
  computed: {
    averageRating () {
      return this.reviewSummary.summary.review_count > 0 ? this.reviewSummary.summary.average_rating : 4.5
    },
    reviewCount () {
      return this.reviewSummary.summary.review_count > 0 ? this.reviewSummary.summary.review_count : 2
    },
    fiveStartCount () {
      return this.reviewSummary.summary.review_count > 0 ? this.reviewSummary.summary.five_star_count : 1
    },
    fourStarCount () {
      return this.reviewSummary.summary.review_count > 0 ? this.reviewSummary.summary.four_star_count : 1
    }
  },
  methods: {
    resetForm () {
      this.form = {
        email: '',
        order_number: '',
      }
      this.submitting = false
      this.errorMessages = []
    },
    async submit () {
      this.submitting = true
      this.errorMessages = []
      await this.$axios.$post('/public/order/pre-check-review-order', this.form).then((json) => {
        if (json.success && json.data?.products?.length) {
          const order = json.data
          this.$router.push(this.localePath(`/order/status/${order.access_token}#review-${order.products[0].id}`))
        } else {
          this.error = true
        }
      }).catch((error) => {
        if (error.response?.data) {
          this.errorMessages = _.values(error.response.data.errors)
        }
      })
      this.submitting = false
    },
  }
}
</script>

<style lang="scss">
#productReviewBox {
  .form-control:focus {
    box-shadow: none;
  }
  .rating-box {
    .b-rating .b-rating-star:first-child, .b-rating .b-rating-value:first-child {
      padding-left: 0 !important;
    }

    .form-control:focus {
      box-shadow: none;
    }

    .form-control-sm {
      height: calc(1.5em + 0.3rem + 2px);
    }

    .average-rating {
      .rt-header {
        h2 {
          font-size: 40px;
          font-weight: 600;
          min-width: 80px;
          line-height: 1.5;
          margin-right: 10px;
          margin-bottom: 0;
        }

        .rating-nowrap {
          .rating-num {
            font-size: 14px;
            font-weight: 500;
          }
          .rating {
            font: normal normal normal 24px/1 "Material Design Icons";
            height: 20px;
            overflow: hidden;
            position: relative;
            vertical-align: top;
            color: #232323;

            .product-rate {
              color: #FFC107;
              font: normal normal normal 24px/1 "Material Design Icons";
              left: 0;
              overflow: hidden;
              padding-top: 1.5em;
              position: absolute;
              top: 0;
            }

            .product-rate:before {
              content: "\F04CE\F04CE\F04CE\F04CE\F04CE";
              font-size: 16px;
              left: 0;
              letter-spacing: 2px;
              position: absolute;
              top: 0;
            }
          }

          .rating:before {
            color: #232323;
            content: "\F04D2\F04D2\F04D2\F04D2\F04D2";
            float: left;
            font-size: 16px;
            left: 0;
            letter-spacing: 2px;
            position: absolute;
            top: 0;
          }
        }
      }
      .rt-star {
        align-items: center;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        max-width: 460px;
        width: 100%;

        > span {
          color: #232323;
          width: 80px;
          min-width: 80px;
          font-size: 16px;
          font-weight: 500;
          margin-right: 10px;

          .b-rating .b-rating-star:first-child, .b-rating .b-rating-value:first-child {
            padding-left: 0 !important;
          }

          .b-rating .b-rating-star, .b-rating .b-rating-value {
            padding: 0 !important;
            font-size: 12px;
          }
        }

        > span.ps-num {
          width: 50px;
          min-width: 50px;
        }

        .ps-progress {
          background: #E9E9E9;
          border-radius: 63px;
          height: 13px;
          position: relative;
          width: 100%;
          margin-right: 11px;

          span {
            border-radius: 63px;
            background: #C7C7C7;
            height: 100%;
            left: 0;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
          }

          .progress-bar {
            background-color: #C7C7C7 !important;
            border-radius: 63px !important;
          }
        }
      }
    }
  }
  .all-image-box {
    margin-left: -5px;
    margin-right: -5px;

    a {
      border: 0;
      height: -webkit-fit-content;
      height: -moz-fit-content;
      height: fit-content;
      margin: 0;
      padding: 6px;
      width: inherit;

      .image-box {
        width: 100%;
        padding-top: 100%;
        cursor: zoom-in;

        .more-image-overlay {
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 1;
          font-size: 21px;
        }
      }

      img {
        -o-object-fit: cover;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        text-align: center;
        font-size: 20px;
        color: white;
        width: 100%;
        border: 1px solid #f0f0f0;
      }
    }
  }
  .filter-review {
    background-color: #E9E9E9;
    font-size: 15px;
    color: #838383;
    font-weight: 500;
    padding: 5px 20px;
    cursor: pointer;
    margin: 0 8px;
    border: 2px solid #e0e0e0;

    &.active {
      border: 2px solid #FFC107;
    }

    i {
      color: #FFC107;
      font-size: 16px;
    }
  }
  .customer-box {
    .avatar {
      img {
        width: 65px !important;
        height: 65px !important;
      }
    }

    .info {
      p {
        color: #717171;
        font-size: 0.85rem;
      }
    }
  }
  .customer-rating-star {
    .form-control-md {
      height: calc(1.5em + 2px);
    }

    .b-rating {
      .b-rating-star {
        flex-grow: unset !important;
        padding: 0 0.15em;
        font-size: 1.1rem;
      }
      .b-rating-star:first-child {
        padding-left: 0 !important;
      }
    }
  }
  .attr-product {
    .badge {
      font-size: 14px;
      line-height: 1.1;

      .color {
        display: block;
        height: 14px;
        width: 14px;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 0 8px 1px #c9c9c9;
      }
    }
  }

  .review-content {
    color: #000000;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 27px;
    letter-spacing: 0em;
    text-align: left;
  }

  .reviewed-at {
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0.015em;
    text-align: left;
    color: #808080;
  }

  .badge-outline-success {
    color: #4BB232;
    border: 1px solid #4BB232;
    background-color: transparent;
  }

  .badge-outline-success[href]:hover, .badge-outline-success[href]:focus {
    color: #4BB232;
    text-decoration: none;
    background-color: rgba(75, 178, 50, 0.2);
  }

  .col-24 {
    flex: 0 0 20%;
    max-width: 20%;
  }

  @media (min-width: 768px) {
    .col-md-15 {
      flex: 0 0 12.5%;
      max-width: 12.5%;
    }
  }

  .review-pagination {
    .pagination {
      .page-item {
        margin-left: 3px;
        margin-right: 3px;

        .page-link {
          display: flex;
          align-items: center;
          justify-content: center;
          border: unset;
          font-weight: 600;
          color: #636363;
          font-size: 14px;
          background-color: #F0F0F0;
          height: 36px;
          width: 36px;
          line-height: 1.25;
        }

        span.page-link {
          text-align: center;
          line-height: 1.3;
        }
      }

      .page-item.active {
        .page-link {
          color: #ffffff;
          background-color: #F5478E;
        }
      }
    }
  }
}
</style>
