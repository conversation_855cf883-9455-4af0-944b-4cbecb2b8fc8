<template>
  <div class="review-image">
    <div class="all-image-box row">
      <CoolLightBox
        :items="items.map(image => $imgUrl(image.src, image.type === 'video' ? 'product-review-video' : 'product-review-image'))"
        :index="index"
        @close="index = null"
      />
      <a v-for="(image, i) in items" :key="i" class="col-4 col-sm-2 image-item" @click.prevent="index = i">
        <div class="image-box position-relative rounded">
          <img :src="$imgUrl(image.thumb, 'product-review-thumb')" :alt="name" onerror="this.onerror=null;this.src=`${$config.publicPath}/images/no-image.webp`;" loading="lazy" class="img-fluid rounded h-100">
          <div v-if="image.type === 'video'" class="position-absolute bottom-0 w-100 px-1 rounded-bottom border border-top-0" style="background-color: rgba(0,0,0,.54)">
            <i class="icon-sen-play-circle-outline text-white" /> <span class="text-white">{{ $t('Video') }}</span>
          </div>
        </div>
      </a>
    </div>
  </div>
</template>
<script>
import CoolLightBox from 'vue-cool-lightbox'
import 'vue-cool-lightbox/dist/vue-cool-lightbox.min.css'

export default {
  components: { CoolLightBox },
  // eslint-disable-next-line vue/require-prop-types
  props: ['files', 'name'],
  data () {
    return {
      index: null
    }
  },
  computed: {
    items () {
      return JSON.parse(JSON.stringify(this.files))
    }
  }
}
</script>
