<template>
  <div class="review-box position-absolute" style="top:0; right: 0">
    <template v-if="average_rating !== 0">
      <b-form-rating
        :value="average_rating"
        class="col-12 col-sm-7 p-0"
        no-border
        size="md"
        readonly
        variant="warning"
      />
    </template>
    <template v-else>
      <b-button v-b-modal="'product-review-modal-' + item.id" size="sm" variant="outline-success">
        {{ $t('Review product') }}
      </b-button>
      <b-modal
        :id="'product-review-modal-' + item.id"
        :ref="'product-review-modal-' + item.id"
        centered
        size="lg"
        :title="$t('Product review')"
        @show="showModal"
        @hidden="resetForm"
      >
        <b-overlay :show="loading" rounded="sm">
          <b-form ref="form">
            <b-alert
              v-if="!canReview"
              variant="warning"
              show
            >
              <strong>{{ $t("Your review is welcome once you've received the product.") }}</strong>
            </b-alert>
            <b-alert
              v-for="(m, i) in errorMessages"
              :key="i"
              ref="warningMessage"
              variant="danger"
              dismissible
              fade
              show
            >
              <strong>{{ $t(m) }}</strong>
            </b-alert>
            <b-alert
              v-for="(m, i) in uploadErrorMessages"
              :key="i"
              ref="warningMessage"
              variant="danger"
              dismissible
              fade
              show
            >
              <strong>{{ $t(m) }}</strong>
            </b-alert>
            <div class="row d-flex align-items-center" style="margin-left: -10px; margin-right: -10px">
              <div class="product-order-item col-12 col-md-6">
                <h6>{{ item.product_name }} / <span class="mb-0 text-capitalize">{{ getVariant(item) }}</span></h6>
                <div
                  class="image-box"
                >
                  <client-only>
                    <product-img
                      v-if="item.thumb_url"
                      :path="item.thumb_url"
                      type="listing"
                      :alt="item.campaign_title"
                      :color="options.color?$colorVal(options.color):''"
                    />
                  </client-only>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="row d-flex align-items-center">
                  <span class="col-12 col-sm-5" style="font-size: 14px">{{ $t('Print quality') }}</span>
                  <b-form-rating
                    v-model="form.print_quality_rating"
                    class="col-12 col-sm-7 p-0"
                    no-border
                    size="md"
                    variant="warning"
                  />
                </div>
                <div class="row d-flex align-items-center">
                  <span class="col-12 col-sm-5" style="font-size: 14px">{{ $t('Product quality') }}</span>
                  <b-form-rating
                    v-model="form.product_quality_rating"
                    class="col-12 col-sm-7 p-0"
                    no-border
                    size="md"
                    variant="warning"
                  />
                </div>
                <div class="row d-flex align-items-center">
                  <span class="col-12 col-sm-5" style="font-size: 14px">{{ $t('Customer support') }}</span>
                  <b-form-rating
                    v-model="form.customer_support_rating"
                    class="col-12 col-sm-7 p-0"
                    no-border
                    size="md"
                    variant="warning"
                  />
                </div>
              </div>
              <div class="col-12 mt-2">
                <b-form-group
                  label-for="comment-input"
                  :invalid-feedback="$t('The review may not be greater than 300 characters.')"
                  :state="!commentState"
                >
                  <b-form-textarea
                    id="comment-input"
                    v-model="form.comment"
                    :state="!commentState"
                    :placeholder="$t('Your review')"
                    rows="5"
                  />
                </b-form-group>
              </div>
              <div class="col-12 my-2">
                <b-form-file
                  ref="uploadInput"
                  :accept="allowedTypes.toString()"
                  :placeholder="$t('Upload product photo/video or drop it here...')"
                  :drop-placeholder="$t('Drop file here...')"
                  :disabled="form.assets.length >= 5"
                  multiple
                  :browse-text="$t('Upload')"
                  @change="uploadFile"
                />
              </div>
              <div class="col-12 upload-files-box d-flex flex-wrap">
                <div v-for="(asset, index) in form.assets" :key="index" class="upload-file-item position-relative mx-2 my-2 mb-lg-0">
                  <b-img
                    :src="$imgUrl(asset.type === 'video' ? asset.thumb : asset.url, 'logo')"
                    height="80"
                    width="80"
                    rounded
                  />
                  <div v-if="asset.type === 'video'" class="position-absolute bottom-0 w-100 px-1 rounded-bottom" style="background-color: rgba(0,0,0,.54)">
                    <i class="icon-sen-play-circle-outline text-white" /> <span class="text-white">{{ $t('Video') }}</span>
                  </div>
                  <b-button class="btn-remove-uploaded-item" @click="removeUploadedItem(index)">
                    ×
                  </b-button>
                </div>
              </div>
            </div>
          </b-form>
        </b-overlay>
        <template #modal-footer>
          <div class="w-100">
            <small class="text-danger float-left">
              * {{ $t("Video is about 30s to 1 minute. Maximum is 5 files") }}<br>
              * {{ $t("Upload your product photo/video to receive a discount coupon") }}
            </small>
            <b-button
              v-if="canReview"
              type="submit"
              variant="success"
              size="md"
              class="float-right"
              :disabled="disableSubmit"
              @click="submit()"
            >
              {{ $t('Send') }}
            </b-button>
          </div>
        </template>
      </b-modal>
    </template>
    <b-modal
      v-if="storeDetail.product_review_coupon && form.assets.length > 0"
      :id="'thank-you-modal-' + item.id"
      :ref="'thank-you-modal-' + item.id"
      size="md"
      centered
      hide-header
      hide-footer
    >
      <b-button class="btn-close-thanks-modal" @click="$refs['thank-you-modal-' + item.id].hide()">
        ×
      </b-button>
      <div class="d-flex flex-column">
        <div class="text-center my-2">
          <h3 class="modal-heading text-wrap mb-0 mt-2">
            {{ storeDetail.name }}
          </h3>
        </div>
        <p class="text-center text-wrap my-2 thanks-text">
          {{ storeDetail.product_review_thank_you_message }}
        </p>
        <h1 class="modal-heading text-center text-dark text-break my-3 coupon-text">
          {{ storeDetail.product_review_coupon }}
        </h1>
        <a :href="localePath(shopNowPath)" class="btn btn-success text-uppercase mt-3 mb-2 border-radius-none">
          {{ $t('Show now') }}
        </a>
      </div>
    </b-modal>
  </div>
</template>
<script>
export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['item', 'orderStatus', 'pos', 'customerName'],
  data () {
    return {
      form: {
        order_product_id: this.item.id,
        print_quality_rating: 5,
        product_quality_rating: 5,
        customer_support_rating: 5,
        comment: '',
        assets: []
      },
      average_rating: 0,
      loading: false,
      errorMessages: [],
      uploadErrorMessages: [],
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'wmv', 'avi', 'webm', 'mkv'],
      allowedTypes: ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/mov', 'video/wmv', 'video/avi', 'video/webm', 'video/mkv']
    }
  },
  computed: {
    options () {
      return JSON.parse(this.item.options)
    },
    commentState () {
      return this.form.comment.length > 300
    },
    disableSubmit () {
      return this.commentState || this.errorMessages.length > 0 || this.loading
    },
    reviewFor () {
      const hash = this.$route.hash
      return hash === '#review' ? null : hash.replace('#review-', '')
    },
    storeDetail () {
      return this.$store.state.storeInfo
    },
    shopNowPath () {
      let customerFirstName = this.$toSlug(this.customerName).split('-')[0]

      if (customerFirstName && customerFirstName.length > 0) {
        customerFirstName = customerFirstName[0].toUpperCase() + customerFirstName.substring(1)
      } else {
        customerFirstName = 'Your_Name'
      }
      return (parseInt(this.storeDetail.id) === 1 ? '/custom-name/' + customerFirstName + '-unisex_standard_tshirt' : '/collection') + '?discount=' + this.storeDetail.product_review_coupon
    },
    canReview () {
      return this.orderStatus === 'completed'
    }
  },
  created () {
    this.average_rating = this.item.product_review && this.item.product_review.average_rating > 0 ? this.item.product_review.average_rating : 0
  },
  mounted () {
    if (this.average_rating === 0) {
      if (this.reviewFor && parseInt(this.reviewFor) === this.item.id) {
        this.$refs['product-review-modal-' + this.item.id].show()
      } else if (this.reviewFor === null && this.pos === 0) {
        this.$refs['product-review-modal-' + this.item.id].show()
      }
    }
  },
  methods: {
    getVariant (item) {
      if (item.options && Object.keys(JSON.parse(item.options)).length > 0) {
        return `${Object.values(JSON.parse(item.options)).toString().replace(',', ' / ').toUpperCase()}`
      } else {
        return item.product_name
      }
    },
    async submit () {
      const $this = this
      this.loading = true
      this.errorMessages = []
      this.uploadErrorMessages = []

      await this.$store.dispatch('productReview/createProductReview', this.form).then((response) => {
        this.loading = false
        if (response.success) {
          this.average_rating = response.data.average_rating
          this.$toast.success(this.$t('Thanks for your review!'))
          this.$router.push({ hash: null })

          if (this.storeDetail.product_review_coupon && this.form.assets.length > 0) {
            this.$refs['thank-you-modal-' + this.item.id].show()
          }
        } else {
          if (typeof response.message === 'string') {
            return this.errorMessages.push(response.message)
          }
          Object.values(response.message).forEach(function (k) {
            Object.values(k).forEach(function (m) {
              $this.errorMessages.push(m)
            })
          })
        }
      }).catch((error) => {
        return this.errorMessages.push(error)
      })
    },
    showModal () {
      this.$router.push({ hash: '#review-' + this.item.id })
    },
    resetForm () {
      this.form = {
        order_product_id: this.item.id,
        print_quality_rating: 5,
        product_quality_rating: 5,
        customer_support_rating: 5,
        comment: '',
        assets: []
      }

      this.loading = false
      this.errorMessages = []
      this.uploadErrorMessages = []
      this.$router.push({ hash: null })
    },
    async uploadFile (event) {
      const files = event.target.files
      const $this = this
      const uploadMaxSize = 50

      this.uploadErrorMessages = []
      this.loading = true

      if ((this.form.assets.length + files.length) > 5) {
        this.$refs.uploadInput.reset()
        this.loading = false
        return this.uploadErrorMessages.push(this.$t('Upload limit 5 file'))
      }

      files.forEach(function (item) {
        const extension = item.name.substr(item.name.lastIndexOf('.') + 1).toLowerCase()
        const type = item.type.toLowerCase()

        if (item.size > uploadMaxSize * 1024 * 1024) {
          $this.$refs.uploadInput.reset()
          $this.loading = false
          return $this.uploadErrorMessages.push($this.$t('The upload file may not be greater than MB', { size: uploadMaxSize }))
        }

        if (!$this.allowedExtensions.includes(extension) || !$this.allowedTypes.includes(type)) {
          $this.$refs.uploadInput.reset()
          $this.loading = false
          return $this.uploadErrorMessages.push($this.$t('Supported file formats') + ': ' + $this.allowedExtensions.join(', ') + '.')
        }
      })

      if (this.uploadErrorMessages.length === 0) {
        const uploadFilePromise = Object.values(files).map(async (item) => {
          if (item.type.match('video')) {
            const thumbnail = await $this.getVideoCover(item)
            const videoFileName = $this.parseFileInfo(item.name)
            thumbnail.name = videoFileName.name + '.jpeg'
            return {
              type: 'video',
              thumb: await $this.$preSignedUploader(thumbnail),
              url: await $this.$preSignedUploader(item)
            }
          }

          return {
            type: 'image',
            url: await $this.$preSignedUploader(item)
          }
        })

        await Promise.all(uploadFilePromise).then((result) => {
          result.map((item) => {
            if (item.type === 'video') {
              return $this.form.assets.push({
                type: item.type,
                thumb: item.thumb.Key,
                url: item.url.Key
              })
            }

            return $this.form.assets.push({
              type: item.type,
              url: item.url.Key
            })
          })
        }).catch(() => {
          return $this.uploadErrorMessages.push(this.$t('Please try again later.'))
        })
      }
      this.$refs.uploadInput.reset()
      this.loading = false
    },
    async getVideoCover (file, seekTo = 0.0) {
      return await new Promise((resolve) => {
        const video = document.createElement('video')
        video.setAttribute('src', URL.createObjectURL(file))
        video.load()
        video.addEventListener('loadedmetadata', () => {
          setTimeout(() => {
            video.currentTime = seekTo
          }, 200)
          video.addEventListener('seeked', () => {
            const canvas = document.createElement('canvas')
            canvas.width = video.videoWidth
            canvas.height = video.videoHeight
            const ctx = canvas.getContext('2d')
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
            ctx.canvas.toBlob((blob) => {
              return resolve(blob)
            }, 'image/jpeg', 1)
          })
        })
      })
    },
    parseFileInfo (fileName) {
      const els = fileName.split('.')
      return {
        name: els.slice(0, -1).join('.'),
        ext: els.pop()
      }
    },
    removeUploadedItem (index) {
      this.form.assets.splice(index, 1)
    }
  }
}
</script>
<style scoped>
  .upload-files-box {
    margin-left: -0.5rem !important;
    margin-right: -0.5rem !important;
  }
  .upload-file-item img {
    object-fit: cover;
  }
  .upload-file-item .btn-remove-uploaded-item {
    position: absolute;
    margin: 0;
    padding: 0;
    top: -4px;
    right: -7px;
    width: 21px;
    height: 21px;
    color: #545b62;
    font-size: 18px;
    font-weight: 800;
    line-height: 0.9 !important;
    background-color: #ffffff;
    border: 2px solid #4e555b;
    border-radius: 50% !important;
  }
  .btn-close-thanks-modal {
    position: absolute;
    margin: 0;
    padding: 0;
    top: -10px;
    right: -10px;
    width: 29px;
    height: 29px;
    color: #545b62;
    font-size: 28px;
    font-weight: 800;
    line-height: 0.75 !important;
    background-color: #ffffff;
    border: 4px solid #4e555b;
    border-radius: 50% !important;
  }
  .coupon-text {
    font-size: 2.7rem;
    line-height: 1;
  }
  .thanks-text {
    font-size: 1rem;
  }
  @media (min-width: 768px) {
    .coupon-text {
      font-size: 3.1rem;
    }
  }
  @media (min-width: 992px) {
    .coupon-text {
      font-size: 3.4rem;
    }
    .thanks-text {
      font-size: 1.1rem;
    }
  }

  @media (max-width: 991px) {
    .review-box {
      margin-top: 0.5rem;
      position: relative !important;
      text-align: end !important;
    }
  }
</style>
