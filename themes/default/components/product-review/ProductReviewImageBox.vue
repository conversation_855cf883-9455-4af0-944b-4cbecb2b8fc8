<template>
  <div class="all-image-box row">
    <CoolLightBox
      :items="items.map(image => $imgUrl(image.src, image.type === 'video' ? 'product-review-video' : 'product-review-image'))"
      :index="index"
      @close="index = null"
    />
    <a v-for="(u, i) in items.slice(0, 8)" :key="i" class="col-3 col-md-15 image-item" @click.prevent="index = i">
      <div class="image-box position-relative rounded">
        <div v-if="items.length > 8 && i === 7" class="position-absolute top-0 w-100 h-100 more-image-overlay d-flex justify-content-center align-items-center text-white rounded">
          +{{ items.length - (i + 1) }}
        </div>
        <img :src="$imgUrl(u.thumb, 'product-review-thumb')" :alt="name" onerror="this.onerror=null;this.src=`${$config.publicPath}/images/no-image.webp`;" loading="lazy" class="img-fluid rounded h-100">
        <div v-if="u.type === 'video'" class="position-absolute bottom-0 w-100 px-1 rounded-bottom border border-top-0" style="background-color: rgba(0,0,0,.54)">
          <i class="icon-sen-play-circle-outline text-white" /> <span class="text-white">{{ $t('Video') }}</span>
        </div>
      </div>
    </a>
  </div>
</template>
<script>
import CoolLightBox from 'vue-cool-lightbox'
import 'vue-cool-lightbox/dist/vue-cool-lightbox.min.css'

export default {
  components: { CoolLightBox },
  props: {
    files: {
      type: Array,
      required: true,
      default: null
    },
    name: {
      type: String,
      required: true,
      default: null
    }
  },
  data () {
    return {
      index: null
    }
  },
  computed: {
    items () {
      return JSON.parse(JSON.stringify(this.files))
    }
  }
}
</script>
