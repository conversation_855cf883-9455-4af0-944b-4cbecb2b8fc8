export default {
  computed: {
    storeInfo,
    generalInfo,
    cartTotalQuantity,
    isHomePage,
    isCampaignPage,
    currentDomain,
    userCountry,
    isSearching
  },
  data () {
    return {
      isSticky: false,
      isOpenSearchBox: false,
      isShowHeader: false,
      searchKeyword: ''
    }
  },
  watch: {
    $route () {
      document.body.classList.remove('scroll-down')
    }
  },
  methods: {
    searchAction,
  },
  mounted
}

function cartTotalQuantity () {
  return this.$store.getters['cart/getTotalQuantity']
}

function mounted () {
  this.$store.dispatch('cart/getCartDataFromStorage')

  document.querySelector(':root').style.setProperty('--primary-color', this.storeInfo.default_color && this.storeInfo.default_color.toLowerCase() !== '#ffffff' && this.storeInfo.default_color !== '#000000' ? this.storeInfo.default_color : '#4BB232')
  if (this.storeInfo.style) {
    document.body.classList.add(this.storeInfo.style)
  }

  const body = document.body
  const _html = document.documentElement
  const scrollUp = 'scroll-up'
  const scrollDown = 'scroll-down'
  const pseudoScroll = 'pseudo-scroll'
  const bodyfixfixed = 'bodyfixfixed'
  let lastScroll = 0
  let tracker = false
  const _pet = window.innerHeight
  const hWindown = Math.max(body.scrollHeight, body.offsetHeight, _html.clientHeight, _html.scrollHeight, _html.offsetHeight)
  const _dri = getMobileOperatingSystem()
  body.classList.add(scrollDown, pseudoScroll)
  window.addEventListener('scroll', () => {
    const currentScroll = window.pageYOffset

    if (!tracker) {
      tracker = true
      setTimeout(() => {
        tracker = false
      }, 100)
      body.classList.remove(pseudoScroll)
      if (currentScroll <= 0 || (currentScroll < lastScroll && body.classList.contains(scrollDown))) {
        body.classList.remove(scrollDown)
        body.classList.add(scrollUp)
      } else if (currentScroll > lastScroll && !body.classList.contains(scrollDown)) {
        body.classList.remove(scrollUp)
        body.classList.add(scrollDown)
      }
      if (_dri === 'iOS') {
        if (((currentScroll + _pet) >= hWindown)) {
          body.classList.remove(bodyfixfixed)
          // box_input.classList.remove('fixfixed')
          // box_input.style.transform = 'translate(0px, 0px)';
        } else if (currentScroll <= 0) {
          body.classList.add(bodyfixfixed)
          // box_input.classList.add('fixfixed')
          // box_input.style.transform = 'translate(0px, -'+_tranStion+'px)';
        } else if (currentScroll > lastScroll) {
          body.classList.remove(bodyfixfixed)
          // box_input.classList.remove('fixfixed')
          // box_input.style.transform = 'translate(0px, 0px)';
        } else {
          body.classList.add(bodyfixfixed)
          // box_input.classList.add('fixfixed')
          // box_input.style.transform = 'translate(0px, -'+_tranStion+'px)';
        }
      }
    }
    lastScroll = currentScroll
  })

  if (!this.userCountry) {
    this.$store.dispatch('deliverToLocation/initDeliverTo')
  }
}

function storeInfo () {
  return this.$store.state.storeInfo
}

function generalInfo () {
  return this.$store.state.generalInfo
}

function searchAction () {
  if (this.hideNavbar) {
    this.hideNavbar()
  }
  if (!this.searchKeyword) {
    return this.$router.push(this.localePath({
      path: '/collection'
    }))
  }

  if (this.searchKeyword !== this.$route.query?.s) {
    this.$store.dispatch('setSearchingState', true)
  }

  const query = {
    s: this.searchKeyword,
    sort: 'relevant'
  }

  this.$router.push(this.localePath({
    path: '/search',
    query
  }))
}

function isHomePage () {
  return this.$route?.name?.includes('index')
}

function isCampaignPage () {
  return this.$route?.name?.includes('campaign')
}

function currentDomain () {
  return process.browser ? window.top.location.hostname : null
}

// function hasClass (element, cls) {
//   return (' ' + element.className + ' ').includes(' ' + cls + ' ')
// }

function getMobileOperatingSystem () {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera
  // Windows Phone must come first because its UA also contains "Android"
  if (/windows phone/i.test(userAgent)) {
    return 'Windows Phone'
  }
  if (/android/i.test(userAgent)) {
    return 'Android'
  }
  // iOS detection from: http://stackoverflow.com/a/9039885/177710
  if (/iPad|iPhone|iPod|Macintosh/.test(userAgent) && !window.MSStream) {
    return 'iOS'
  }
  return 'unknown'
}

function userCountry () {
  const preferredCountry = this.$getPreferredCountryText()
  return preferredCountry || this.$store.getters['deliverToLocation/getDeliverToLocation']
}

function isSearching () {
  return this.$store.getters.getHeaderSearchingState
}
