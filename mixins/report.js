export default {
  name: 'Report',
  data () {
    return {
      currentTab: 0,
      campaignSlug: this.$route.query.campaign || '',
      fileUpload: [],
      isFileLoading: false,
      fileErrorMessage: '',
      form1: {
        userType: 1,
        firstName: '',
        lastName: '',
        address1: '',
        address2: '',
        state: '',
        country: '',
        email: '',
        emailConfirm: '',
        phone: '',
        legalName: '',
        additionalInfo: {
          specificConcern: '',
          originalWork: '',
          otherInfo: ''
        },
        acceptStatement: false,
        attachFile: []
      },
      countryArray: this.$store.state.generalInfo && this.$store.state.generalInfo.countries,
      form2: {
        hate: false,
        sexual: false,
        violence: false,
        misleading: false,
        illegal: false
      }
    }
  },
  computed: {
    breadCrumbsList
  },
  methods: {
    submit,
    updateFile,
    resetForm
  }
}

function breadCrumbsList () {
  return [{
    text: this.$t('Report'),
    to: this.localePath('/report')
  }]
}

function submit () {
  const formData = {}
  formData.campaign_slug = this.campaignSlug

  if (this.currentTab === 0) {
    if (!this.form1.acceptStatement) {
      // todo: show notification
      return
    }

    formData.reason = 'copyright'
    formData.email = this.form1.email
    formData.name = `${this.form1.firstName} ${this.form1.lastName}`
    formData.phone = this.form1.phone
    formData.address = this.form1.address1
    formData.address_2 = this.form1.address2
    formData.city = this.form1.city
    formData.state = this.form1.state
    formData.postcode = this.form1.postcode
    formData.country = this.form1.country
    formData.additional_info = {
      report_by: this.form1.userType === 1 ? 'right_owner' : 'agent',
      legal_name: this.form1.legalName,
      specific_concern: this.form1.additionalInfo.specificConcern,
      original_work: this.form1.additionalInfo.originalWork,
      other_info: this.form1.additionalInfo.otherInfo,
      attach_files: this.form1.attachFile
    }
  } else {
    const reasons = []

    if (this.form2.hate) {
      reasons.push('hate')
    }

    if (this.form2.sexual) {
      reasons.push('sexual')
    }

    if (this.form2.violence) {
      reasons.push('violence')
    }

    if (this.form2.misleading) {
      reasons.push('misleading')
    }

    if (this.form2.illegal) {
      reasons.push('illegal')
    }

    formData.reason = reasons
  }
  this.isFileLoading = true
  this.$store.dispatch('sendReportForm', formData).then(({ success, message }) => {
    this.isFileLoading = false
    if (success) {
      this.resetForm()
      this.$toast.success(this.$t('Send report success'))
    } else {
      this.$toast.error(`Error: ${message}`)
    }
  }).catch((error) => {
    this.$toast.error(`Error: ${error.message}`)
    this.isFileLoading = false
  })
}

function updateFile (e) {
  if (e.target.files.length && e.target.files.length <= 5) {
    this.warningForm = false
    const promiseUploadFile = Object.keys(e.target.files).map(item => this.$preSignedUploader(e.target.files[item]))
    this.isFileLoading = true
    Promise.all(promiseUploadFile).then((result) => {
      this.isFileLoading = false
      this.form1.attachFile = result.map(item => item.Key)
    }).catch(() => {
      this.isFileLoading = false
      this.fileErrorMessage = 'Please try again later'
    })
  } else if (e.target.files.length > 5) {
    this.fileErrorMessage = 'Upload limit 5 file'
    this.fileUpload = []
  }
}

function resetForm () {
  this.campaignSlug = ''
  this.form1.userType = 1
  this.form1.email = ''
  this.form1.firstName = ''
  this.form1.lastName = ''
  this.form1.address1 = ''
  this.form1.address2 = ''
  this.form1.state = ''
  this.form1.country = ''
  this.form1.email = ''
  this.form1.emailConfirm = ''
  this.form1.phone = ''
  this.form1.legalName = ''
  this.form1.additionalInfo.specificConcern = ''
  this.form1.additionalInfo.originalWork = ''
  this.form1.additionalInfo.otherInfo = ''
  this.form1.acceptStatement = false
  this.form1.attachFile = []

  this.form2.hate = false
  this.form2.sexual = false
  this.form2.violence = false
  this.form2.misleading = false
  this.form2.illegal = false
}
