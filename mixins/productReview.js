export default {
  data () {
    return {
      reviewSummary: {
        summary: {
          average_rating: 0,
          review_count: 0,
          five_star_count: 0,
          four_star_count: 0,
          three_star_count: 0,
          two_star_count: 0,
          one_star_count: 0
        },
        files: []
      },
      reviews: [],
      filter: 'helpful',
      page: 1,
      loading: false
    }
  },
  computed: {
    reviewDisplay () {
      return this.$store.state.storeInfo.product_review_display
    }
  },
  watch: {
    currentProduct (product) {
      this.page = 1
      this.filter = 'helpful'
      if (this.reviewDisplay !== 'disable') {
        this.getReviewSummary(product.campaign_id, product.template_id)

        if (!this.isShowModal) {
          this.getReviews(product.campaign_id, product.template_id)
        }
      }
    }
  },
  methods: {
    getReviewSummary,
    getReviews,
    changePage,
    changeFilter
  }
}

function getReviewSummary (campaignId, templateId) {
  this.$store.dispatch('productReview/getReviewSummary', { campaignId, templateId }).then((response) => {
    this.reviewSummary = Object.freeze((response && response.reviewSummary) || {})
  })
}

function getReviews (campaignId, templateId) {
  this.loading = true
  this.$store.dispatch('productReview/getReviews', { campaignId, templateId, filter: this.filter, page: this.page }).then((response) => {
    this.reviews = Object.freeze((response && response.data) || {})
    this.loading = false
  })
}

function changePage (page) {
  this.page = page
  this.getReviews(this.currentProduct.campaign_id, this.currentProduct.template_id)
  document.getElementById('productReviewBox').scrollIntoView()
}

function changeFilter (filter) {
  this.filter = filter
  this.page = 1
  this.getReviews(this.currentProduct.campaign_id, this.currentProduct.template_id)
}
