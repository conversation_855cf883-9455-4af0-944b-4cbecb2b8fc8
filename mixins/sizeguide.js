export default {
  computed: {
    currentSizeguide,
    sizeGuideList
  },
  methods: {
    openSizeGuild
  }
}

function currentSizeguide () {
  if (this.$store.state.generalInfo && this.$store.state.generalInfo.size_guides && this.currentProduct && this.currentOptions && this.currentOptions.size) {
    return this.$store.state.generalInfo.size_guides.find(item =>
      item.product_id === this.currentProduct?.template_id && item.size.toLowerCase() === this.currentOptions.size.toLowerCase()
    )
  }
  return false
}

function sizeGuideList () {
  return this.$store.state.generalInfo?.size_guides.filter(item =>
    item.product_id === this.currentProduct?.template_id
  )
}

function openSizeGuild (product) {
  this.$tracking.customTracking({ event: 'interact', action: 'open_size_guide' })
  this.$store.commit('campaign/UPDATE_CURRENT_SIZEGUIDE_TEMPLATE', {
    name: product.name,
    size_chart: product && product.attributes && JSON.parse(product.attributes).size_chart,
    sizeGuideList: this.sizeGuideList
  })
}
