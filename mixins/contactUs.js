import Vue from 'vue'
import Vuelidate from 'vuelidate'
import { required, email } from 'vuelidate/lib/validators'
import lscache from 'lscache'
Vue.use(Vuelidate)

export default {
  // eslint-disable-next-line vue/require-prop-types
  data () {
    return {
      successForm: false,
      warningForm: false,
      submitted: false,
      isFileLoading: false,
      message: '',
      fileUpload: [],
      contactForm: {
        type: '',
        fullName: '',
        email: '',
        order: '',
        message: '',
        attachFile: []
      },
      optionsSelect: [
        'Where is my order?',
        'Change my order',
        'Return my order',
        'Payment/Discount issue',
        'Item not as describe',
        'Item not received',
        'Other questions'
      ]
    }
  },
  validations: {
    contactForm: {
      email: { required, email },
      message: {
        required,
        checkMessage
      }
    }
  },
  methods: {
    handleSubmit,
    updateFile,
    showError
  },
  computed: {
    lastOrder () {
      if (!process.browser) {
        return null
      }

      return lscache.get('lastOrder')
    },
    trackLastOrderUrl () {
      if (this.lastOrder) {
        return `/order/status/${this.lastOrder.order_token}?utm_source=page&utm_medium=contact-us`
      }

      return '/order/track'
    }
  },
  mounted
}

async function handleSubmit (e) {
  this.submitted = true
  this.warningForm = false

  // stop here if form is invalid
  this.$v.$touch()

  if (this.$v.$invalid) {
    return
  }

  let token = null

  try {
    token = await this.$recaptcha.getResponse()
  } catch (e) {
    this.showError('Please check the reCAPTCHA box')
    return
  }

  try {
    this.isFileLoading = true
    await this.$store.dispatch('sendContactForm', {
      name: this.contactForm.fullName,
      subject: this.contactForm.type,
      email: this.contactForm.email,
      order_number: this.contactForm.order,
      message: this.contactForm.message,
      attached_files: this.contactForm.attachFile,
      token
    }).then(async (json) => {
      this.isFileLoading = false
      if (!json.success) {
        throw json
      }
      this.submitted = false
      this.warningForm = false
      this.successForm = true // show success message
      this.contactForm.message = '' // clear message
      this.contactForm.attachFile = [] // clear message
      this.fileUpload = [] // clear message
      this.$v.$reset()
      await this.$recaptcha.reset()
    }).catch((error) => {
      this.isFileLoading = false
      throw error
    })

    await this.$tracking.trackEvent({
      event: 'contact',
      options: {
        name: this.contactForm.fullName,
        email: this.contactForm.email,
        subject: this.contactForm.type,
        order_number: this.contactForm.order,
        message: this.contactForm.message
      }
    })
  } catch (error) {
    this.showError('Please try again later')
  }
}

function mounted () {
  const userInfo = lscache.get('userInfo')

  this.contactForm.fullName = userInfo ? userInfo.name : ''
  this.contactForm.email = this.$route.query.email || userInfo?.email || ''

  const { order_number: orderNumber, customer_email: customerEmail, customer_name: customerName } = this.$route.query
  this.contactForm.order = orderNumber || ''
  if (customerName) {
    this.contactForm.fullName = customerName
  }
  if (customerEmail) {
    this.contactForm.email = customerEmail
  }
}

function checkMessage (value) {
  return value && value.trim().length >= 10
}

function updateFile (e) {
  if (e.target.files.length && e.target.files.length <= 5) {
    this.warningForm = false
    const promiseUploadFile = Object.keys(e.target.files).map(item => this.$preSignedUploader(e.target.files[item]))
    this.isFileLoading = true
    Promise.all(promiseUploadFile).then((result) => {
      this.isFileLoading = false
      this.contactForm.attachFile = result.map(item => item.key || item.Key)
    }).catch(() => {
      this.isFileLoading = false
      this.showError('Please try again later')
    })
  } else if (e.target.files.length > 5) {
    this.showError('Upload limit 5 file')
    this.fileUpload = []
  }
}

function showError (message) {
  this.warningForm = true
  this.message = this.$t(message)
  window.scrollTo(0, 0)
}
