
import sizeGuide from '~/mixins/sizeguide'

export default {
  name: 'ModalBundleProductMixin',
  mixins: [sizeGuide],
  // eslint-disable-next-line vue/require-prop-types
  props: ['currentProduct'],
  data () {
    return {
      campaignData: this.currentProduct,
      isModal: true,
      isShowModalBundle: false,
      isCustom: false,
      optionList: {},
      currentOptions: {},
      updateData: 0
    }
  },
  computed: {
    currentVariant,
    currentPrice,
    currentOldPrice
  },
  watch: {
    currentProduct: resetData,
    isShowModalBundle: resetData
  },
  methods: {
    saveBundleDiscountOption,
    resetData
  }
}

function resetData () {
  if (this.isShowModalBundle && this.currentProduct) {
    this.campaignData = this.currentProduct
    this.optionList = { ...this.currentProduct.options }
    this.currentOptions = { ...this.currentProduct.currentOptions }
  }
}

function currentVariant () {
  if (this.isShowModalBundle && this.currentProduct) {
    const optionKeys = Object.keys((this.currentProduct && this.currentProduct.options) || {})
    const currentVariantKey = optionKeys.map(item => this.currentOptions[item].replace(/-/g, '_')).join('-')
    return this.currentProduct.variantsList.find(item => item.variant_key === currentVariantKey)
  } else {
    return false
  }
}

function currentPrice () {
  if (this.currentVariant && this.currentVariant.price) {
    return this.currentVariant.price
  } else {
    return this.currentProduct.price
  }
}

function currentOldPrice () {
  if (this.currentVariant && this.currentVariant.price) {
    return this.currentVariant.old_price > this.currentVariant.price ? this.currentVariant.old_price : 0
  } else {
    return this.currentProduct.old_price > this.currentProduct.price ? this.currentProduct.old_price : 0
  }
}

function saveBundleDiscountOption () {
  const optionKeys = Object.keys(this.currentProduct.options)
  const currentVariantKey = optionKeys.map(item => this.currentOptions[item].replace(/-/g, '_')).join('-')
  const currentVariant = this.currentProduct.variantsList.find(item => item.variant_key === currentVariantKey)
  this.$emit('updateBundleProduct',
    this.currentProduct,
    {
      currentOptions: this.currentOptions,
      currentVariant,
      currentVariantKey
    })
  this.updateData++
  if (this.currentProduct.full_printed && this.currentOptions.size) {
    this.$nextTick(() => {
      this.resetData()
    })
  }
}
