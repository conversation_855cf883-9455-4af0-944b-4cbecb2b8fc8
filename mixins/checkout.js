/* eslint-disable no-unreachable */
import lscache from 'lscache'
import cloneDeep from 'lodash/cloneDeep'
import debounce from 'lodash/debounce'
import { email, required } from 'vuelidate/lib/validators'
import Vue from 'vue'
import Vuelidate from 'vuelidate'
import { API_LIST, DISCOUNT_ERROR_TYPE, TRACKING_PLATFORMS } from '~/helpers/variableConst'
import { Mailcheck } from '~/helpers/suggestMistypedEmail'
import addressForm from '~/mixins/addressForm'
import { getCurrentTimeInUTC } from '~/helpers/function'
Vue.use(Vuelidate)
export default {
  // eslint-disable-next-line vue/require-prop-types
  mixins: [addressForm],
  props: ['order', 'gateways', 'shippingMethods'],
  data () {
    let selectedGateway = this.gateways && this.gateways[0]?.gateway
    if (!selectedGateway || selectedGateway === 'stripe') {
      selectedGateway = 'stripe-card'
    }

    return {
      userInfo: {
        name: this.order.customer_name || '',
        email: this.order.customer_email || '',
        address: this.order.address || '',
        address_2: this.order.address_2 || '',
        city: this.order.city || '',
        state: this.order.state || '',
        zipcode: this.order.postcode || '',
        phone: this.order.customer_phone || '',
        country: this.order.country || 'US',
        subscribed: this.order.country === 'US' ? 1 : 0,
        house_number: this.order.house_number || '',
        mailbox_number: this.order.mailbox_number || ''
      },
      orderNote: this.order.order_note || '',
      phoneInput: this.order.customer_phone,
      shippingFee: this.order.shipping_method || (this.shippingMethods && this.shippingMethods[0]?.name) || '',
      selectedGateway,
      stripePaymentMethod: 'stripe',
      listGateways: cloneDeep(this.gateways),
      listBanks: [],
      currentBank: false,
      showCountry: false,
      isShowCountrySelect: false,
      submitted: false,
      promotionsList: [],
      trackerUpdateEmail: false,
      trackerUpdateName: false,
      trackerUpdateOrder: false,
      serverEmailValidate: true,
      isTempEmail: false,
      isShowModalConfirmEmail: false,
      paypalConfirm: false,
      isShowModelCreditCardDiscount: false,
      debouncedRetrackKlaviyoEvent: null,

      payWithCardAble: false,
      timer: 600,
      paymentFailedLog: '',
      countErrorStripe: 0,
      countErrorStripeEWallet: 0,
      stripe: {},
      brandCard: '',
      publishableKey: '',
      elementsStripe: {},
      card: {},
      elementBanks: [],
      banks: [
        {
          name: 'iDEAL',
          payment_method: 'ideal',
          currency: 'EUR',
        },
        {
          name: 'Bancontact',
          payment_method: 'bancontact',
          currency: 'EUR'
        },
        {
          name: 'Sofort',
          payment_method: 'sofort',
          currency: 'EUR',
          // required: ['country'],
          countries: {
            EUR: [
              'AT',
              'BE',
              'DE',
              'IT',
              'NL',
              'ES'
            ]
          }
        },
        {
          name: 'Klarna',
          payment_method: 'klarna',
          currency: 'EUR',
          required: ['country'],
          countries: {
            EUR: [
              'AT',
              'BE',
              'FI',
              'FR',
              'DE',
              'IE',
              'IT',
              'NL',
              'ES'
            ],
            USD: [
              'US'
            ]
          }
        }
      ],
      errorMessage: '',
      clientSecrets: [],
      billingDetails: {
        country: ''
      },
      discountApply: '',
      checkDiscount: false,
      isShowModalTazapay: false,

      loading: false,
      isDisabled: false,

      tipList: [0, 0.02, 0.05, 0.1],
      currentTip: 0, // Customer value
      currentTipIndex: false,
      isCustomTipFocus: false,
      customTipTimeout: null,
      isPhoneValid: false,
      warningAddress: false,
      mailcheck: null,
      isPaypalSmartCheckout: false,
      noPaymentGatewayConfig: false,
      checkPaypalError: 0,
      userTriggerShippingMethod: false,
      userTriggerTipSection: false,

      postMessageHandlerTable,
      iframes: {
        paypal: null,
        stripeCard: null,
        stripeEwallet: null,
        stripeBanks: null,
      },
      paypalIframeFullScreen: false,
      stripeData: {},
      stripeBankDetail: {},
      stripeBankEl: null,
      stripeBankReloadCount: 0,

      // For handling stripe error
      reloadGatewayIfEncountered: [
        'payment_intent_authentication_failure',
        'payment_intent_payment_attempt_expired',
        'payment_intent_payment_attempt_failed',
        'payment_method_customer_decline',
        'payment_method_provider_decline',
        'payment_method_provider_timeout',
        'setup_attempt_failed',
        'setup_intent_authentication_failure',
        'setup_intent_setup_attempt_expired',
      ],
      weekendArray: [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday'
      ],
      extraInfo: {
        days: [],
        instructions: '',
        access: '',
      },
      discountErrorType: 0
    }
  },
  computed: {
    showPhoneInput,
    selectedGatewayId,
    stripeGatewayId,
    orderToken,
    timerText,
    currentTipText: {
      get () {
        if (this.isCustomTipFocus) {
          return this.currentTip
        }
        return this.$formatPriceByRate(this.currentTip, 1, this.order.currency_code)
      },
      set (value) {
        this.currentTip = value
      }
    },
    currentUrl () {
      return process.browser ? window.top.location.href : ''
    },
    currency () {
      return this.$store.state.currency.code
    },
    paymentDomain () {
      return this.$store.state.order.paymentDomain
    },
    hasMistypedEmail,
    isDev () {
      if (!process.browser) {
        return ['dev', 'development', 'local'].includes(this.$config.appEnv)
      }

      const domain = window.top.location.hostname
      return domain === 'localhost' || domain.endsWith('.dev.senprints.net')
    },
    countryDisabledCheckout () {
      return this.$store.getters['generalInfo/getDisabledCountryCheckout']
    },
    isDisabledButtonCoupon () {
      return !!this.loading || (this.discountApply === '' && !this.order.discount_code) || this.isDisabled
    },
    computedPaypalGateway () {
      return this.listGateways.find(item => item.gateway === 'paypal') || {}
    },
    computedStripeGateway () {
      return this.listGateways.find(item => ['stripe', 'stripe-card'].includes(item.gateway))
    },
    isCountryUsingAlphanumericZipcode () {
      const countries = ['CA', 'GB', 'NL', 'IE', 'AU', 'MT', 'CH', 'IT', 'BR', 'AR', 'BE', 'PT', 'SE']
      return countries.includes(this.userInfo.country)
    },
    isNotOrderService () {
      /**
       * This will hide/disable some elements in the checkout page
       * to make the page more concise
       *
       * Elements will be hidden:
       * - 'Return to cart'
       * - Countdown
       * - Shipping
       * - Insurance
       * - Discount
       * - Tip
       * - Product thumbnail
       */
      return this.order.type !== 'service'
    },
  },
  watch: {
    selectedGateway,
    shippingFee,
    'userInfo.name': userInfoNameChange,
    'userInfo.address': userInfoChange,
    'userInfo.address_2': userInfoChange,
    'userInfo.city': userInfoChange,
    'userInfo.state': userInfoChange,
    'userInfo.zipcode': userInfoChange,
    'userInfo.phone': userInfoChange,
    'userInfo.subscribed': userInfoChange,
    'userInfo.houseNumber': userInfoChange,
    'userInfo.mailboxNumber': userInfoChange,
    'order.fulfill_status': getIntentOrder,
    'order.total_amount': priceChange
  },
  mounted,
  beforeDestroy () {
    document.removeEventListener('visibilitychange', this.handleVisibilityChange)
  },
  methods: {
    generateAndMountIframe,
    attachPostMessageHandler,
    postMessageHandler,
    initPaypalSmartCheckoutIframe,
    initStripeIframe,

    checkSelectedGatewayIsPartOfStripe,
    checkSelectedGatewayIsBank,
    getStripePaymentName,
    updateOrder,
    updateEmail,
    retrackKlaviyoEvent,
    userInfoNameChange,
    userInfoChange,
    updateCheckoutCountry,
    updateCheckoutDiscount,
    useCreditCardDiscount,
    payWithPaypal,
    payWithTazapay,
    payWithBank,
    paypalDiscount,
    handleSubmit,
    getIntentOrder,
    getStripeEWallet,
    getIntentOrderAddition,
    handleError,
    payWithCard,
    payWithEWallet,
    handlePayWithStripe,
    getReturnUrlStripe,
    payWithMomo,
    testOrder,
    showError,
    resetOrder,
    runTimer,
    removeItem,
    updateTip,
    updateDeliveryInsurance,
    selectBank: selectedGateway,
    initPaypalSmartCheckout,
    splitName,
    getOrderDescription,
    initTazaPay,
    onCloseModalTazapay,
    handleVisibilityChange,
    paypalGetOrderId,
    resetTip (isRecalculateTipIndex = false) {
      if (isRecalculateTipIndex) {
        const tipPercent = (this.order.tip_amount / this.order.total_product_amount).toFixed(2)
        this.currentTipIndex = this.tipList.findIndex(item => item === parseFloat(tipPercent))
      }
      this.currentTip = this.$formatPriceByRate(this.order.tip_amount, this.order.currency_rate, this.order.currency_code, true)
    },
    onCustomTipInputFocusOut () {
      this.isCustomTipFocus = false
      this.customTipTimeout = setTimeout(this.resetTip, 200)
    },
    onUpdateCustomTip () {
      if (this.customTipTimeout) {
        clearTimeout(this.customTipTimeout)
      }
      this.updateTip(this.$convertPrice(this.currentTip || 0, this.currency, this.$store.state.storeInfo.default_currency).toFixed(2), true)
    },
    getClass (inputType) {
      const posIndex = this.currentCountryFormMapPosition.indexOf(inputType.startsWith('address') ? 'address' : inputType)
      const orderClass = posIndex >= 0 ? `order-${posIndex + 1}` : ''
      let widthClass = ''
      let paddingClass = ''
      if (!this.currentCountryFormMap) {
        switch (inputType) {
          case 'city':
            widthClass = 'col-12 col-sm-4'
            paddingClass = 'pr-sm-1'
            break
          case 'postCode':
            paddingClass = 'pl-1'
            widthClass = 'col-6 col-sm-4'
            break
          case 'state':
            widthClass = 'col-6 col-sm-4'
            paddingClass = 'px-sm-1'
            break
          case 'address_1':
          case 'address_2':
            widthClass = 'col-12'
            break
        }
      } else {
        const width = this.currentCountryFormMap?.[inputType]?.width ?? 100
        const nextKey = this.currentCountryFormMapPosition[posIndex + 1]
        const nextWidth = this.currentCountryFormMap?.[nextKey]?.width ?? 100
        switch (width) {
          case 30:
            widthClass = 'col-4'
            if (nextWidth >= 60) {
              paddingClass = 'pr-1'
            } else {
              paddingClass = 'px-1'
            }
            break
          case 40:
            widthClass = 'col-5'
            break
          case 50:
            widthClass = 'col-6'
            if (nextWidth === 50) {
              paddingClass = 'pr-1'
            } else {
              paddingClass = 'pl-1'
            }
            break
          case 60:
            widthClass = 'col-7'
            paddingClass = 'pl-1'
            break
          case 70:
            widthClass = 'col-8'
            paddingClass = 'pl-1'
            break
          case 100:
            widthClass = 'col-12'
            break
        }
      }
      return `${paddingClass} ${widthClass} ${orderClass}`
    },
  },
  head,
  validations () {
    const userInfo = {
      name: {
        required,
        separateName (value) {
          return !this.$store.state.order.isSeparateName || /^(\S+)\s+\S(.*)/.test(value.trim())
        }
      },
      email: { required, email },
      state: { checkState },
    }
    if (this.isRequired('address_1')) {
      userInfo.address = { required }
    }
    if (this.isRequired('city')) {
      userInfo.city = { required }
    }
    if (this.isRequired('postCode')) {
      userInfo.zipcode = { required }
    }
    if (this.isRequired('houseNumber', false)) {
      userInfo.house_number = { required }
    }

    return {
      userInfo
    }
  }
}
function checkState (value) {
  if (!this.countryState || this.countryState.find(item => item.value === value)) {
    return true
  }
  return !this.isRequired('state')
}

function head () {
  const script = []

  // prevent load more than one time (when redirect from other page)
  if (typeof Stripe === 'undefined') {
    script.push({
      hid: 'stripe-js',
      src: 'https://js.stripe.com/v3/',
      body: true
    })
  }
  const stripeGateway = this.listGateways.find(item => item.gateway === 'stripe' || item.gateway === 'stripe-card')
  const paypalGateway = this.listGateways.find(item => item.gateway === 'paypal')

  const notHasStripe = !stripeGateway || !stripeGateway.id

  if (notHasStripe) {
    if (paypalGateway && paypalGateway.clientId) {
      let src = `https://www.paypal.com/sdk/js?client-id=${paypalGateway.clientId}&currency=USD&enable-funding=venmo`

      if (paypalGateway.paypal_merchant_id) {
        src += `&merchant-id=${paypalGateway.paypal_merchant_id}`
      }

      script.push({
        src,
        hid: 'paypal-sdk',
        'data-partner-attribution-id': 'SensePrints_Ecom' // BN Code
      })
    } else {
      this.noPaymentGatewayConfig = true
    }
  }

  // check if has tazapay
  if (this.listGateways.some(item => item.gateway === 'tazapay')) {
    const tazapaySdkJs = this.isDev
      ? 'https://js-sandbox.tazapay.com/v1.0-sandbox.js'
      : 'https://js.tazapay.com/v1.0.js'

    // Tazapay
    script.push({
      src: tazapaySdkJs,
      hid: 'tazapay-sdk',
      body: true
    })
  }

  if (script.length > 0) {
    return { script }
  }
}

function initTazaPay (clientToken) {
  // is SDK loaded?
  if (typeof window.tazapay === 'undefined' || !clientToken) {
    return
  }

  const options = {
    clientToken, // Use the token obtained at step2.
    callbacks: {
      onPaymentSuccess: async () => {
        const orderToken = this.orderToken

        this.$nuxt.$loading.start()
        this.loading = true
        const endpoint = `/public/order/callback/tazapay/${orderToken}`
        const { success } = await this.$axios.$get(endpoint)

        this.$nuxt.$loading.finish()

        if (success) {
          const url = `/order/thank-you/${orderToken}`
          await this.$router.push(this.localePath(url))
        } else {
          this.$toast('Transaction failed. Please try again')
          this.loading = false
        }
      }
      // onPaymentFail: () => { console.log('fail') },
      // onPaymentMethodSelected: () => { console.log('onPaymentMethodSelected') }, // optional
      // onPaymentCancel: () => { console.log('onPaymentCancel') } // optional
    },
    style: {} // optional, for customising your integration
  }

  try {
    window.tazapay.checkout(options)
  } catch (error) {
    // ignore
  }
}

const buildKlaviyoData = ({ order, email, name }) => {
  if (!order || !order.products || !email) {
    return {}
  }
  const itemNames = order.products.map(item => item.campaign_title)
    .filter((value, index, self) => self.indexOf(value) === index)
  const categories = order.products.map(item => item.product_name)
    .filter((value, index, self) => self.indexOf(value) === index)
  const items = order.products.map((item) => {
    return {
      ProductID: item.product_id || 0,
      ProductName: item.campaign_title || '',
      Quantity: item.quantity || 0,
      ItemPrice: item.price || 0,
      RowTotal: item.quantity * item.price,
      ProductURL: item.product_url,
      ImageURL: item.thumb_url,
      ProductCategories: [item.product_name || '']
    }
  })
  const accessToken = order.access_token || ''
  return {
    items,
    itemNames,
    categories,
    accessToken,
    email,
    name,
    time: getCurrentTimeInUTC()
  }
}

function mounted () {
  setTimeout(() => {
    const email = this.userInfo.email || ''
    const name = this.userInfo.name || ''
    this.$tracking.trackEvent({
      event: 'initiate_checkout',
      options: {
        order_token: this.order.order_number || this.order.id,
        content_ids: [...this.order.products.map(item => item.campaign_id)],
        content_name: [...this.order.products.map(item => item.campaign_title)].join(','),
        content_category: [...this.order.products.map(item => item.product_name)].join(','),
        content_value: [...this.order.products.map(item => item.price)],
        content_quantity: [...this.order.products.map(item => item.quantity)],
        content_type: 'product',
        num_items: this.order.total_quantity,
        currency: 'USD',
        country: this.order.country,
        value: this.order.total_amount,
        klaviyoData: buildKlaviyoData({ order: this.order, email, name })
      }
    })
  }, 500)

  // Create debounced function once during mount
  this.debouncedRetrackKlaviyoEvent = debounce(this.retrackKlaviyoEvent, 1000)

  this.$store.dispatch('cart/getCartDataFromStorage')

  this.runTimer()

  const userInfo = lscache.get('userInfo')

  if (userInfo) {
    Object.keys(this.userInfo).forEach((item) => {
      if (!this.userInfo[item] && userInfo[item]) {
        this.userInfo[item] = userInfo[item]
      }
    })
  }

  if (this.order.discount_code && this.order.total_discount) {
    this.discountApply = this.order.discount_code
  }
  // else {
  //   this.discountApply = this.$store.state.cart.discount || ''
  // }
  const campaignIds = this.order.products.map(item => item.campaign_id)
  this.$store.dispatch('campaign/getPromotion', { campaignIds }).then(({ success, data }) => {
    if (success) {
      this.promotionsList = data
    }
  })

  const discountCodeCookie = this.$cookies.get('discount_code')

  if ((this.$route.query.discount || discountCodeCookie) && this.$route.params.discount !== this.discountApply) {
    this.discountApply = this.$route.query.discount || discountCodeCookie
    this.updateCheckoutDiscount()
  } else {
    setTimeout(this.getIntentOrder, 500)
  }

  if (discountCodeCookie) {
    this.$cookies.remove('discount_code')
  }

  if (this.$route.query.cart_key) {
    lscache.set('orderKey', {
      orderToken: this.$route.params.token,
      cartKey: this.$route.query.cart_key
    })

    const abandonedTrackUrl = `/public/order/callback/abandoned/track?cartKey=${this.$route.query.cart_key}`
    this.$httpWrite('GET', abandonedTrackUrl)
  }
  const visitInfo = this.$getVisitInfo()
  if (visitInfo && (this.$route.query.utm_campaign === 'abandoned_email' || this.$route.query.utm_campaign === 'abandoned_sms')) {
    return this.$store.dispatch('order/updateOrderData', {
      order_token: this.orderToken,
      visit_info: visitInfo
    }).then((result) => {
      if (!result.success) {
        this.$toast.error(`Error: ${result.message}`)
      }
      return result
    })
  }

  this.mailcheck = new Mailcheck()

  this.$nextTick(() => {
    if (this.$refs[(this.userInfo.email) ? 'name' : 'email']) {
      this.$refs[(this.userInfo.email) ? 'name' : 'email'].focus()
    }
    this.resetTip(true)
  })

  document.addEventListener('visibilitychange', this.handleVisibilityChange)
}

async function handleVisibilityChange () {
  if (!document.hidden) {
    this.loading = true
    await this.$store.dispatch('order/getOrderData', this.$route.params.token)
    this.loading = false
  }
}

// convert from PHP function (backend): \App\Traits\OrderDescription::getOrderDescription
function getOrderDescription () {
  // Order #orderNumber:
  // #productName1|#options1|#options2|#quantity|#price
  // #productName2|#options1|#options2|#quantity|#price
  // Subtotal: #subtotal
  // Shipping Fee (#country): #shippingFee
  // Discount: #discount
  // Insurance Fee: #insuranceFee
  // Tax: #tax
  // Tip: #tip
  // Total: #total
  const { order } = this

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',

    // These options are needed to round to whole numbers if that's what you want.
    // minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    // maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
  })

  try {
    const separateLine = '. '
    let description = `Order ${order.order_number || order.id}`

    description += separateLine + `Subtotal: ${order.total_product_amount}`

    if (order.total_shipping_amount) {
      description += separateLine + `Shipping Fee (${order.country}): ${formatter.format(order.total_shipping_amount)}`
    }

    if (order.total_discount) {
      description += separateLine + `Discount: ${formatter.format(order.total_discount)}`
    }

    if (order.insurance_fee) {
      description += separateLine + `Insurance Fee: ${formatter.format(order.insurance_fee)}`
    }

    if (order.total_tax_amount) {
      description += separateLine + `Tax: ${formatter.format(order.total_tax_amount)}`
    }

    if (order.tip_amount) {
      description += separateLine + `Tip: ${formatter.format(order.tip_amount)}`
    }

    // to display amount from DB
    description += separateLine + `Total: ${formatter.format(order.total_amount)}`

    // check if > 127 chars
    // https://developer.paypal.com/docs/api/orders/v2/#orders_create!path=purchase_units/description&t=request
    if (description.length > 127) {
      description = description.substring(0, 124) + '...'
    }

    return description
  } catch (e) {
    return `Order token: ${order.access_token}`
  }
}

// computed
function showPhoneInput () {
  // return this.$store.state.storeInfo?.checkout_phone

  // always show phone input
  // ref: https://senprints.atlassian.net/browse/SDV-2177
  return true
}

function selectedGatewayId () {
  if (!this.listGateways) {
    return ''
  }

  if (this.selectedGateway === 'stripe-bank' && this.stripeBankDetail?.paymentGatewayId) {
    return this.stripeBankDetail.paymentGatewayId
  }

  const _this = this
  const result = this.listGateways.find((item) => {
    if (_this.checkSelectedGatewayIsPartOfStripe() || _this.checkSelectedGatewayIsBank()) {
      return item.name === 'Stripe'
    } else {
      return item.gateway === this.selectedGateway
    }
  })

  return result ? result.id : ''
}

function stripeGatewayId () {
  if (!this.listGateways) {
    return ''
  }

  if (this.selectedGateway === 'stripe-bank' && this.stripeBankDetail?.paymentGatewayId) {
    return this.stripeBankDetail.paymentGatewayId
  }

  const result = this.listGateways.find(item => item.gateway === 'stripe' || item.gateway === 'stripe-card')

  return result ? result.id : ''
}

function orderToken () {
  return this.$route.params.token
}

function timerText () {
  const timer = parseInt(this.timer, 10)
  let minutes = Math.floor(timer / 60)
  let seconds = timer - minutes * 60

  if (minutes < 10) {
    minutes = `0${minutes}`
  }

  if (seconds < 10) {
    seconds = `0${seconds}`
  }

  return `${minutes}:${seconds}`
}

function paypalDiscount () {
  return parseFloat(this.$store.state.generalInfo.paypal_discount || 0)
}

// method
async function handleSubmit () {
  this.$tracking.customTracking({ event: 'checkout_place_order_button_click' })
  this.submitted = true
  // stop here if form is invalid
  this.$v.$touch()
  if (this.$v.$invalid) {
    if (this.$v.userInfo.email.$error) {
      this.$refs.email.focus()
      return false
    }
    if (this.$v.userInfo.name.$error) {
      this.$refs.name.focus()
      return false
    }
    if (this.$v.userInfo.address.$error) {
      this.$refs.address.focus()
      return false
    }
    if (this.$v.userInfo.city.$error) {
      this.$refs.city.focus()
      return false
    }
    if (this.countryState && this.$v.userInfo.state.$error) {
      this.$refs.state.focus()
      return false
    }
    if (this.$v.userInfo.zipcode.$error) {
      this.$refs.zipcode.focus()
      return false
    }
    window.scrollTo(0, 0)
    return false
  }
  if ((!this.userInfo.phone && this.isPhoneNumberRequired) || (!this.isPhoneValid && this.isPhoneNumberRequired)) {
    this.$refs.phone.focus()
    return false
  }

  if (!this.serverEmailValidate) {
    this.isShowModalConfirmEmail = true
    return false
  }

  if (this.paypalDiscount() && !this.paypalConfirm && this.selectedGateway === 'paypal') {
    this.isShowModelCrediCardDiscount = true
    return false
  }

  this.loading = 'place_order'

  const { success } = await this.updateOrder(true)
  if (!success) {
    this.loading = false
    return false
  }
  if (this.isPaypalSmartCheckout) {
    return true
  }
  switch (this.selectedGateway) {
    case 'stripe-card':
      await this.payWithCard()
      break
    case 'stripe-ewallet':
      await this.payWithEWallet()
      break
    case 'cod':
      await this.testOrder()
      break
    case 'paypal':
      await this.payWithPaypal()
      break
    case 'momo':
      await this.payWithMomo()
      break
    case 'tazapay':
      await this.payWithTazapay()
      break
    case 'other':
      await this.payWithBank()
      break
  }
}

function getStripePaymentName (payment = '') {
  return 'stripe-' + payment
}

function checkSelectedGatewayIsPartOfStripe () {
  return this.selectedGateway.includes(this.getStripePaymentName())
}

function checkSelectedGatewayIsBank (value) {
  return this.selectedGateway === 'other'
}

function updateOrder (verifyAddress = false) {
  this.loading = true
  const userInfo = {
    ...this.userInfo
  }
  if (!this.isHasField('state')) {
    userInfo.state = ''
  }
  if (!this.isHasField('address_2')) {
    userInfo.address_2 = ''
  }
  if (!this.isHasField('houseNumber', false)) {
    userInfo.house_number = ''
  }
  if (!this.isHasField('mailboxNumber', false)) {
    userInfo.mailbox_number = ''
  }
  const payload = {
    order_token: this.orderToken,
    user_info: userInfo,
    email: this.userInfo.email,
    country: this.order.country || this.userInfo.country,
    payment_gateway_id: this.selectedGatewayId,
    payment_method_name: this.checkSelectedGatewayIsPartOfStripe() || this.checkSelectedGatewayIsBank() ? this.stripePaymentMethod : this.selectedGateway,
    payment_failed_log: this.paymentFailedLog,
    verify_address: verifyAddress,
    order_note: this.orderNote,
  }

  return this.$store.dispatch('order/updateOrderData', payload).then((result) => {
    if (result.success) {
      lscache.set('userInfo', this.userInfo)
    } else {
      const keys = Object.keys(result.message)
      if (keys.length) {
        for (const key of keys) {
          this.$toast.error(`Error: ${result.message[key].toString()}`)
        }
      } else {
        this.$toast.error(`Error: ${result.message}`)
      }
    }
    this.loading = false
    return result
  }).catch(() => {
    this.loading = false
  })
}

function updateEmail () {
  this.$v.$touch()
  this.serverEmailValidate = true
  clearTimeout(this.trackerUpdateEmail)
  if (this.$v.userInfo.email.$error) {
    return
  }

  const email = this.userInfo.email
  if (!email.includes('@') || email.endsWith('@')) {
    return
  }

  this.debouncedRetrackKlaviyoEvent()

  if (this.order.fraud_status === 'trusted') {
    this.trackerUpdateEmail = setTimeout(() => {
      this.$store.dispatch('order/confirmEmail', email).then((result) => {
        if (result && result.success === false) {
          this.serverEmailValidate = false
        }
      })

      this.$store.dispatch('order/updateOrderData', {
        email,
        order_token: this.orderToken
      }).then((result) => {
        this.isTempEmail = (!result.success && result.message.email) || false
      })
    }, 1000)
  }
}

function retrackKlaviyoEvent () {
  // Validate email with regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(this.userInfo.email)) {
    return
  }
  // track event 'Started Checkout' for Klaviyo
  const email = this.userInfo.email || ''
  const name = this.userInfo.name || ''
  this.$tracking.trackEvent({
    event: 'initiate_checkout',
    options: {
      order_token: this.order.order_number || this.order.id,
      content_ids: [...this.order.products.map(item => item.campaign_id)],
      content_name: [...this.order.products.map(item => item.campaign_title)].join(','),
      content_category: [...this.order.products.map(item => item.product_name)].join(','),
      content_value: [...this.order.products.map(item => item.price)],
      content_quantity: [...this.order.products.map(item => item.quantity)],
      content_type: 'product',
      num_items: this.order.total_quantity,
      currency: 'USD',
      country: this.order.country,
      value: this.order.total_amount,
      klaviyoData: buildKlaviyoData({ order: this.order, email, name })
    },
    platform: TRACKING_PLATFORMS.KLAVIYO
  })
}

function updateCheckoutCountry (country) {
  this.loading = true
  if (country === 'US' && this.selectedGateway === 'other') {
    this.selectedGateway = this.listGateways[0].gateway
  }
  this.$store.dispatch('order/updateOrderData', {
    country,
    order_token: this.orderToken,
    payment_gateway_id: this.selectedGatewayId,
    subscribed: country === 'US' ? 1 : 0
  }).then((json) => {
    if (json.success) {
      // update visit_info
      const visitInfo = this.$getVisitInfo()
      visitInfo.country = country
      this.userInfo.country = country
      this.userInfo.subscribed = country === 'US' ? 1 : 0
      lscache.set('visitInfo', visitInfo)
      lscache.set('userInfo', this.userInfo)
      this.$store.commit('order/UPDATE_ORDER_DATA', { order: { country: this.userInfo.country } })
      this.loading = false
      if (this.showPhoneInput) {
        this.$refs.phone.choose(country)
      }
    }
  }).catch(() => {
    this.loading = false
  })
}

function useCreditCardDiscount () {
  this.selectedGateway = 'stripe'
  this.$store.dispatch('order/updateOrderData', {
    order_token: this.orderToken,
    credit_card_discount: true,
    payment_gateway_id: this.selectedGatewayId,
  }).then((data) => {
    this.checkDiscount = true
    if (data.success) {
      this.$store.commit('order/UPDATE_ORDER_DATA', {
        order: {
          discount_code: this.discountApply,
          payment_gateway_id: this.selectedGatewayId
        }
      })
    }
  })
}

function updateCheckoutDiscount () {
  if (this.loading || (this.discountApply === '' && !this.order.discount_code) || this.isDisabled) {
    return
  }

  this.loading = 'apply-coupon'
  this.$store.dispatch('order/updateOrderData', {
    discount_code: this.discountApply,
    order_token: this.orderToken,
    payment_gateway_id: this.selectedGatewayId,
    ref: 'apply_discount_code'
  }).then((data) => {
    console.log('get update order data : ', data)
    if (data?.message === 'Order is using others discount code') {
      this.discountErrorType = DISCOUNT_ERROR_TYPE.USING_OTHER_DISCOUNT
    } else {
      this.discountErrorType = DISCOUNT_ERROR_TYPE.NONE
    }
    this.checkDiscount = true
    if (data.success) {
      this.$store.commit('order/UPDATE_ORDER_DATA', {
        order: {
          discount_code: data.data?.order?.discount_code ?? this.discountApply,
          payment_gateway_id: this.selectedGatewayId
        }
      })
    }
    setTimeout(this.getIntentOrder, 500)
    this.loading = false
  }).catch(() => {
    this.loading = false
  })
}

function userInfoNameChange (newValue) {
  this.userInfoChange(newValue)
  clearTimeout(this.trackerUpdateName)
  this.userInfo.name = newValue
  this.trackerUpdateName = setTimeout(() => {
    this.retrackKlaviyoEvent()
  }, 1000)
}

function userInfoChange (newValue) {
  clearTimeout(this.trackerUpdateOrder)
  this.$v.$touch()
  if (this.$v.$invalid) {
    return
  }

  this.trackerUpdateOrder = setTimeout(() => {
    this.updateOrder()
  }, 1000)
}

async function priceChange () {
  this.loading = true
  if (this.selectedGateway === 'other') {
    this.clientSecrets = []
    this.getIntentOrderAddition(this.currentBank)
  } else {
    await this.getIntentOrder()
  }
  this.loading = false
}

async function selectedGateway (newValue) {
  this.loading = true
  const _this = this

  if (newValue === 'paypal' && this.order.payment_discount) {
    await this.$store.dispatch('order/updateOrderData', {
      order_token: this.orderToken,
      payment_gateway_id: this.selectedGatewayId,
      payment_method_name: this.selectedGateway
    }).then((data) => {
      if (data.success) {
        _this.$store.commit('order/UPDATE_ORDER_DATA', {
          order: {
            payment_gateway_id: _this.selectedGatewayId
          }
        })
      }
    })
  }

  if (this.checkSelectedGatewayIsBank() && this.currentBank) {
    const bank = this.currentBank

    // prefill
    for (const [key, val] of Object.entries(this.billingDetails)) {
      if (val === '') {
        this.billingDetails[key] = this.userInfo[key]
      }
    }

    // prefill country, if invalid will select first
    const countries = bank.countries
    if (countries) {
      let firstCountry = ''
      let checkExists = false
      countries.forEach((country, index) => {
        if (index === 0) {
          firstCountry = country
        }
        if (country.code === _this.billingDetails.country) {
          checkExists = true
        }
      })

      if (!checkExists) {
        _this.billingDetails.country = firstCountry.code
      }
    }
    this.stripePaymentMethod = bank.gateway

    this.getIntentOrderAddition(bank)
  }
  this.loading = false
}

function shippingFee (newValue) {
  this.userTriggerShippingMethod = true
  this.loading = `shipping_fee_${newValue}`
  this.$store.dispatch('order/updateOrderData', {
    shipping_method: newValue,
    order_token: this.orderToken
  }).then((data) => {
    if (data.success) {
      this.$store.commit('order/UPDATE_ORDER_DATA', { order: { shipping_method: newValue } })
    }
    this.loading = false
  }).catch(() => {
    this.loading = false
  })
}

async function getIntentOrder (errorPaypal = false) {
  if (!this.stripeGatewayId && this.computedPaypalGateway.checkout_domain) {
    this.isPaypalSmartCheckout = true
    this.$nextTick(this.initPaypalSmartCheckoutIframe)
    return
  }
  if (!this.stripeGatewayId && !errorPaypal) {
    this.isPaypalSmartCheckout = true
    this.$nextTick(this.initPaypalSmartCheckout)
    return
  }
  if (this.stripeGatewayId) {
    if (!this.selectedGateway) {
      this.selectedGateway = 'stripe-card'
    }

    if (this.countErrorStripe > 7) {
      this.handleError('Can not init stripe', false)
      return
    }

    if (typeof window.Stripe === 'undefined') {
      setTimeout(this.getIntentOrder, 1000)
      return
    }
    this.clientSecrets = []
    this.payWithCardAble = false
    if (!this.orderToken || !this.stripeGatewayId) {
      this.countErrorStripe++
      setTimeout(this.getIntentOrder, 1000)
      return
    }

    try {
      this.isDisabled = true
      const { success, data } = await this.$axios.$get(this.$masterUrl(API_LIST.API_STRIPE_GET_INTENT_ORDER_ADDITION), {
        params: {
          order_token: this.orderToken,
          payment_methods: 'card',
          gateway_id: this.stripeGatewayId
        }
      })
      this.isDisabled = false
      if (!success) {
        this.countErrorStripe++
        setTimeout(this.getIntentOrder, 1000)
        return
      }
      const _this = this

      const isUseIframe = !!this.computedStripeGateway.checkout_domain
      if (isUseIframe) {
        this.stripeData = data
        this.$nextTick(this.initStripeIframe)
      }

      // eslint-disable-next-line no-undef
      this.stripe = Stripe(data.publishableKey)
      this.publishableKey = data.publishableKey

      // reset list gateway
      const oldSelectedGateway = this.selectedGateway
      let temp = {}
      const gateways = []
      let checkPaypalExists = false
      let stripeGateway = null
      this.gateways.forEach((gateway) => {
        if (gateway.gateway === 'stripe') {
          stripeGateway = gateway
          temp = cloneDeep(gateway)
          temp.gateway = 'stripe-card'
          gateways.push(temp)
          temp = cloneDeep(gateway)
          temp.gateway = 'stripe-ewallet'
          gateways.push(temp)
        } else if (gateway.gateway === 'paypal') {
          checkPaypalExists = gateway
        } else {
          gateways.push(gateway)
        }
      })
      this.listGateways = gateways

      this.$nextTick(() => {
        try {
          _this.clientSecrets.card = data.clientSecret
          // Create Card
          if (!isUseIframe) {
            const element = _this.stripe.elements({
              fonts: [
                {
                  family: 'Montserrat',
                  cssSrc: 'https://fonts.googleapis.com/css?family=Montserrat:400,500'
                }
              ],
              clientSecret: data.clientSecret,
              locale: _this.$i18n.locale
            })

            const style = {
              base: {
                color: '#32325d',
                fontFamily: 'Montserrat, sans-serif', // set integrated font family
                fontSmoothing: 'antialiased'
              }
            }

            // cardNumber
            _this.card = element.create('cardNumber', {
              placeholder: `${_this.$t('Card number')} *`,
              showIcon: true,
              style
            })
            _this.card.mount('#card-payment-number')
            // cardExpiry
            const cardExpiry = element.create('cardExpiry', {
              placeholder: `${_this.$t('Expiration date')} (${_this.$t('MM/YY')}) *`,
              style
            })
            cardExpiry.mount('#card-payment-expiry')
            // cardCvc
            const cardCvc = element.create('cardCvc', {
              placeholder: `${_this.$t('Security code')} *`,
              style
            })
            cardCvc.mount('#card-payment-cvc')

            _this.card.on('change', function (event) {
              _this.brandCard = event.brand
              _this.handleError(event.error?.message)
            })
            _this.card.on('focus', function () {
              _this.selectedGateway = 'stripe-card'
            })
            _this.getStripeEWallet(data.clientSecret)
          }

          // off other_payments option
          if (!stripeGateway.options?.other_payments) {
            this.payWithCardAble = true
            if (checkPaypalExists) {
              this.listGateways.push(checkPaypalExists)
            }
            return
          }
          let object = {}
          let currency = 'EUR'
          _this.listBanks = []

          _this.banks.forEach(function (bank) {
            object = cloneDeep(bank)
            object.id = stripeGateway.id
            object.gateway = _this.getStripePaymentName(object.name)

            currency = 'EUR'
            if (object.name === 'Klarna') {
              if (stripeGateway.options.klarna_currency === null) {
                return
              }
              currency = stripeGateway.options.klarna_currency
            }
            object.currency = currency

            // get countries by selected currency
            if (object.countries && object.countries[currency]) {
              object.countries = _this.countryArray.filter(el => object.countries[currency].includes(el.code))
            }

            _this.listBanks.push(object)
          })
          if (checkPaypalExists) {
            gateways.push(checkPaypalExists)
          }
          _this.listGateways = gateways

          const checkOldSelectedGateWayExists = _this.listGateways.find(el => el.gateway === oldSelectedGateway)
          if (checkOldSelectedGateWayExists) {
            _this.selectedGateway = oldSelectedGateway
          }

          _this.payWithCardAble = true
        } catch (error) {
          console.log(error)
        }
      })
    } catch (e) {
      this.isDisabled = false
    }
  }
}

function getStripeEWallet (clientSecret) {
  if (this.countErrorStripeEWallet > 3) {
    this.handleError('Can not init stripe ewallet', false)
    return
  }

  try {
    const appearance = {
      variables: {
        fontFamily: 'Montserrat, sans-serif',
        borderRadius: '0.3rem'
      }
    }
    this.elementsStripe = this.stripe.elements({
      clientSecret,
      appearance,
      locale: this.$i18n.locale
    })

    // Create eWallet
    const paymentElement = this.elementsStripe.create('payment')
    paymentElement.mount('#payment-element')

    const _this = this
    // update payment method when changed
    paymentElement.on('focus', function () {
      _this.selectedGateway = 'stripe-ewallet'
    })
    paymentElement.on('change', function (event) {
      if (_this.checkSelectedGatewayIsPartOfStripe()) {
        if (event.value.type !== 'card') {
          _this.stripePaymentMethod = _this.getStripePaymentName(event.value.type)
        }
      }
    })
  } catch (e) {
    this.isDisabled = false
  }
}

async function getIntentOrderAddition (bank) {
  const currencyCode = bank.currency

  // if haven't created transaction by this currency
  if (!this.clientSecrets[currencyCode]) {
    // get all payment methods have same currency at once
    const paymentMethods = []
    this.listBanks.forEach((each) => {
      if (each.currency === currencyCode) {
        paymentMethods.push(each.payment_method)
      }
    })

    this.loading = 'place_order'

    if (!this.orderToken || !this.stripeGatewayId) {
      this.handleError(`missing ${!this.orderToken ? 'orderToken' : 'stripeGatewayId'}`)
      return
    }

    const { success, data, message } = await this.$axios.$get(this.$masterUrl(API_LIST.API_STRIPE_GET_INTENT_ORDER_ADDITION), {
      params: {
        order_token: this.orderToken,
        payment_methods: paymentMethods.length ? paymentMethods.join(',') : '',
        gateway_id: this.stripeGatewayId,
        currency_code: currencyCode
      }
    })
    this.loading = false
    if (!success) {
      this.handleError(message)
      return
    }
    this.$set(this.clientSecrets, currencyCode, data.clientSecret)

    this.stripeBankDetail = data

    if (!this.stripeBankEl) {
      this.stripeBankEl = window.Stripe(this.stripeBankDetail?.publishableKey || this.publishableKey)
    }
  }

  // create component
  const bankName = this.getStripePaymentName(bank.name)
  if (bank.name_component) {
    const options = {
      style: {
        base: {
          padding: '10px 12px',
          color: '32325d',
          fontSize: '16px',
          '::placeholder': {
            color: '#aab7c4'
          }
        }
      },
      placeholder: this.$t('Choose a bank')
    }

    const _this = this
    this.elementBanks[bankName] = (this.stripeBankEl ?? this.stripe).elements({
      clientSecret: _this.clientSecrets[currencyCode],
      locale: _this.$i18n.locale
    }).create(bank.name_component, options)

    this.$nextTick(() => {
      _this.elementBanks[bankName].mount(`#${bankName}`)
    })
  }
}

function onCloseModalTazapay () {
  this.$nuxt.$loading.finish()
  this.loading = false
}

async function payWithTazapay () {
  this.$nuxt.$loading.start()
  this.loading = 'place_order'
  this.isShowModalTazapay = true

  const { success, data, message } = await this.$axios.$post('/public/order/create/tazapay', {
    order_token: this.orderToken
  })

  this.$nuxt.$loading.finish()

  if (success) {
    this.initTazaPay(data.token)
  } else {
    this.isShowModalTazapay = false
    this.loading = false

    if (message) {
      this.$toast.error(`Error: ${message}`)
    } else {
      this.$toast.error('Error: Something went wrong')
    }

    this.handleError(message)
  }
}

async function payWithPaypal () {
  this.loading = 'place_order'
  if (this.paymentDomain) {
    const domain = window.top.location.hostname
    const path = `/payment.html?token=${this.orderToken}&ref=${encodeURIComponent(window.btoa(domain))}`
    const isDev = domain === 'localhost' || domain.endsWith('.dev.senprints.net')

    if (isDev) {
      window.top.location.href = path
    } else {
      window.top.location.href = `https://${this.paymentDomain}${path}`
    }
    return
  }

  // create order
  const { success, data } = await this.$axios.$post('/public/order/create/paypal', {
    order_token: this.orderToken
  })

  if (success) {
    // redirect to Paypal
    window.top.location.href = data
  } else {
    this.loading = false
    this.handleError(data)
  }
}

function handleError (errorMessage, showError = true, errors = {}) {
  if (!errorMessage) {
    return
  }
  this.loading = false
  this.paymentFailedLog = errorMessage

  // Show error to your customer
  if (showError) {
    this.showError(errorMessage)
  }
  const message = errors || JSON.stringify(errorMessage)
  // const storeInfo = this.$store.state.storeInfo
  // const updateCheckoutLogException = (this.$config?.appRegion === 'us' || !this.$config?.appRegion) ? (storeInfo?.enable_distributed_checkout === 1 ? API_LIST.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION : API_LIST.API_CHECKOUT_LOG_EXCEPTION) : API_LIST.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION
  const updateCheckoutLogException = API_LIST.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION

  this.$axios.$post(updateCheckoutLogException, {
    message,
    token: this.orderToken
  })
  this.$tracking.newCustomTracking('payment-error', null, null, {
    message,
    token: this.orderToken
  })
}

function getReturnUrlStripe () {
  const url = new URL(window.location.href)

  let returnUrl = url.origin + '/checkout/stripe'
  returnUrl += '?order_token=' + this.orderToken
  returnUrl += '&publishable_key=' + this.publishableKey

  return returnUrl
}

async function payWithCard () {
  this.loading = 'place_order'

  if (this.computedStripeGateway.checkout_domain && this.iframes.stripeCard) {
    this.iframes.stripeCard.contentWindow.postMessage({ name: 'stripe_confirm_card', args: [] }, '*')
    return
  }

  const response = await this.stripe.confirmCardPayment(this.clientSecrets.card, {
    payment_method: {
      card: this.card,
      billing_details: {
        name: this.userInfo.name,
        email: this.userInfo.email,
        phone: this.userInfo.phone,
        address: {
          line1: this.userInfo.address,
          line2: this.userInfo.address_2,
          city: this.userInfo.city,
          postal_code: this.userInfo.zipcode,
          state: this.userInfo.state,
          country: this.userInfo.country,
        }
      }
    }
  })

  this.handlePayWithStripe(response)
}

async function payWithEWallet () {
  this.loading = 'place_order'

  const returnUrl = this.getReturnUrlStripe()

  if (this.computedStripeGateway.checkout_domain && this.iframes.stripeEwallet) {
    this.iframes.stripeEwallet.contentWindow.postMessage({ name: 'stripe_confirm_ewallet', args: [returnUrl] }, '*')
    return
  }

  const response = await this.stripe.confirmPayment({
    elements: this.elementsStripe,
    confirmParams: {
      return_url: returnUrl
    },
    redirect: 'if_required'
  })

  this.handlePayWithStripe(response)
}

async function handlePayWithStripe (response) {
  const errors = {}
  if (response.error) {
    for (const [key, val] of Object.entries(response.error)) {
      if ([
        'code',
        'decline_code',
        'type',
        'message'
      ].includes(key)) {
        errors[key] = val
      }
    }
    if (this.reloadGatewayIfEncountered.includes(response.error?.code)) {
      this.getIntentOrder() // reload the gateway
    }

    this.handleError(response.error.message, true, errors)
    return
  }

  if (response.paymentIntent.status === 'requires_action') {
    const result = await this.stripe.confirmCardPayment(this.clientSecret)
    await this.handlePayWithStripe(result)
    return
  }

  this.$store.dispatch('cart/resetData')
  window.location.href = this.localePath({ path: `/order/thank-you/${this.orderToken}` })
}

function payWithBank () {
  this.loading = 'place_order'
  const returnUrl = this.getReturnUrlStripe()
  const bankName = this.currentBank.gateway
  const bank = this.listBanks.find(el => this.getStripePaymentName(el.name) === bankName)
  this.loading = false

  const maxReloadCount = 3
  if (typeof window.Stripe === 'undefined' && this.stripeBankReloadCount <= maxReloadCount) {
    this.stripeBankReloadCount += 1
    setTimeout(payWithBank, 1000)
    return
  }
  console.log('stripe bank initiated') // TODO: rm this

  if (!this.stripeBankEl) {
    this.stripeBankEl = window.Stripe(this.stripeBankDetail?.publishableKey || this.publishableKey)
  }

  switch (bankName) {
    case 'stripe-Sofort':
      this.stripeBankEl.confirmSofortPayment(this.clientSecrets[bank.currency], {
        payment_method: {
          sofort: {
            country: this.billingDetails.country
          }
        },
        return_url: returnUrl
      })
      break
    case 'stripe-iDEAL':
      this.stripeBankEl.confirmIdealPayment(this.clientSecrets[bank.currency], {
        payment_method: {
          ideal: {},
          billing_details: {
            name: this.userInfo.name
          }
        },
        return_url: returnUrl
      })
      break
    case 'stripe-Bancontact':
      this.stripeBankEl.confirmBancontactPayment(this.clientSecrets[bank.currency], {
        payment_method: {
          billing_details: {
            name: this.userInfo.name
          }
        },
        return_url: returnUrl
      })
      break
    case 'stripe-Klarna':
      this.stripeBankEl.confirmKlarnaPayment(this.clientSecrets[bank.currency], {
        payment_method: {
          billing_details: {
            email: this.userInfo.email,
            address: {
              country: this.billingDetails.country
            }
          }
        },
        return_url: returnUrl
      })
      break
  }
}

async function payWithMomo () {
  this.loading = 'place_order'

  // create order
  const { success, data } = await this.$axios.$post('/public/order/create/momo', {
    order_token: this.orderToken
  })

  if (success) {
    // redirect to momo
    window.top.location.href = data
  } else {
    this.loading = false
    this.handleError(data)
  }
}

async function testOrder () {
  // create order
  const { success } = await this.$axios.$post('/public/order/create/cod', {
    order_token: this.orderToken
  })

  if (success) {
    window.location.href = this.localePath({ path: `/order/thank-you/${this.orderToken}` })
  }
  this.loading = false
}

function showError (errorMsgText) {
  this.loading = false
  this.errorMessage = errorMsgText
  const elmnt = document.getElementById('payment-info')
  elmnt.scrollIntoView()
  const vm = this
  setTimeout(function () {
    vm.errorMessage = ''
  }, 10000)
}

function resetOrder () {
  this.timer = 600
  this.runTimer()
  this.$store.dispatch('order/getOrderData', this.$route.params.token)
}

function runTimer () {
  if (this.timer === 0) {
    clearTimeout(this.timeout)
    return
  }

  this.timer -= 1

  this.timeout = setTimeout(this.runTimer, 1000)
}

async function removeItem (index) {
  const cloneProducts = this.$store.state.cart.products.map(item => JSON.parse(JSON.stringify(item)))
  cloneProducts.splice(index, 1)
  this.$store.commit('cart/UPDATE_CART_DATA', { products: cloneProducts })

  if (!cloneProducts.length) {
    await this.getIntentOrder()
    this.$router.push('/cart')
    return
  }

  const result = await this.$store.dispatch('order/createOrder')
  if (result && result.success) {
    this.$nuxt.refresh()
    await this.getIntentOrder()
  } else {
    this.$toast.error(`Error: ${result.message && result.message.content}`)
  }
}

function updateTip (tip, isRecalculateTipIndex = false) {
  if ((Number(tip) || 0) === this.order.tip_amount || !!this.loading || this.isDisabled) {
    return
  }
  this.loading = 'tip'
  this.$store.dispatch('order/updateOrderData', {
    tip_amount: Number(tip) || 0,
    order_token: this.orderToken
  }).then((json) => {
    if (json.success) {
      this.resetTip(isRecalculateTipIndex)
    }
    this.loading = false
  }).catch(() => {
    this.loading = false
  })
}

function updateDeliveryInsurance (value) {
  if (this.loading || this.isDisabled) {
    return
  }
  this.loading = true
  this.$store.dispatch('order/updateOrderData', {
    delivery_insurance: value,
    order_token: this.orderToken
  }).then((json) => {
    this.loading = false
  }).catch(() => {
    this.loading = false
  })
}

function hasMistypedEmail () {
  if (!this.mailcheck) {
    return false
  }
  const run = this.mailcheck.run({
    email: this.userInfo.email
  })
  if (!this.userInfo.email || !run || this.userInfo.email === run.full) {
    return false
  }
  return run.full
}

// https://stackoverflow.com/a/67971387
function splitName (name = '') {
  const [firstName, ...lastName] = name.split(' ').filter(Boolean)
  return {
    firstName,
    lastName: lastName.join(' ')
  }
}

async function initPaypalSmartCheckout () {
  if (this.order.fulfill_status === 'no_ship') {
    return
  }
  if (typeof window.paypal === 'undefined') {
    setTimeout(() => {
      this.initPaypalSmartCheckout()
    }, 1000)
    return
  }
  try {
    const paypalButtonContainer = this.$refs.paypalButtonContainer
    while (paypalButtonContainer.firstElementChild) {
      paypalButtonContainer.firstElementChild.remove()
    }

    // eslint-disable-next-line no-undef
    await paypal.Buttons({
      // Sets up the transaction when a payment button is clicked
      createOrder: this.paypalGetOrderId,
      onClick: async (data, actions) => {
        this.$nuxt.$loading.start()
        const check = await this.handleSubmit()
        this.$nuxt.$loading.finish()

        if (check) {
          return actions.resolve()
        } else {
          return actions.reject()
        }
      },
      // Finalize the transaction after payer approval
      onApprove: (data) => {
        this.postMessageHandlerTable.paypal_verify_transaction({ thisArg: this, args: [data] })
      },

      onCancel: () => {
        this.loading = false
        this.$nuxt.$loading.finish()
      },

      onError: () => {
        this.loading = false
        this.$nuxt.$loading.finish()
      }
    }).render('#paypal-button-container')
  } catch (error) {
    this.checkPaypalError++
    if (this.checkPaypalError <= 8) {
      setTimeout(() => {
        this.initPaypalSmartCheckout()
      }, 1000)
    } else {
      this.isPaypalSmartCheckout = false
      this.getIntentOrder(true)
    }
  }
}

async function paypalGetOrderId () {
  try {
    const { success, data } = await this.$axios.$post('/public/order/create/paypal', {
      order_token: this.orderToken,
      return_order_id: true
    })

    if (success) {
      return data // PayPal Order ID
    }

    this.loading = false
    this.$toast.error('Could not initiate PayPal Checkout. Please try again.')
  } catch (error) {
    this.loading = false
    this.$toast.error('Could not initiate PayPal Checkout.')
  }
}

function generateAndMountIframe ({ src = '', classes = [], container = undefined, autoResize = false }) {
  const iframe = document.createElement('iframe')
  iframe.src = src

  if (classes) {
    iframe.classList.add(...classes)
  }
  if (container) {
    container.innerHTML = ''
    container.appendChild(iframe)
  }

  if (autoResize && iframe.contentWindow) {
    iframe.contentWindow.onresize = () => {
      iframe.contentWindow?.document.body.clientHeight && iframe.setAttribute('style', `height: calc(${iframe.contentWindow.document.body.clientHeight}px + 1rem);`)
    }
  }

  return iframe
}

async function initPaypalSmartCheckoutIframe () {
  if (this.order.fulfill_status === 'no_ship') {
    return
  }
  if (this.listGateways > 1 && this.computedPaypalGateway.length) { return }

  this.attachPostMessageHandler()
  await this.$nextTick()

  let embedPath = '/embed/paypal.html'
  if (this.computedPaypalGateway?.checkout_domain && !this.isDev) {
    embedPath = (!this.computedPaypalGateway.checkout_domain.startsWith('https://') ? 'https://' : '') + `${this.computedPaypalGateway.checkout_domain}${embedPath}`
  }
  let classes = ['w-100', 'h-100', 'position-absolute']
  if (this.order && this.order.device === 'mobile') {
    classes = classes.filter(el => el !== 'position-absolute')
  }
  this.iframes.paypal = this.generateAndMountIframe({
    src: embedPath,
    classes,
    container: this.$refs.paypalButtonContainer
  })
}
// eslint-disable-next-line
async function initStripeIframe () {
  if (this.order.fulfill_status === 'no_ship') {
    return
  }

  this.attachPostMessageHandler()
  await this.$nextTick()

  const stripeIframeClasses = ['w-100', 'border-0']
  let stripeIframeAddr = '/embed/stripe.html'
  if (this.computedStripeGateway?.checkout_domain && !this.isDev) {
    stripeIframeAddr = (!this.computedStripeGateway.checkout_domain.startsWith('https://') ? 'https://' : '') + `${this.computedStripeGateway.checkout_domain}${stripeIframeAddr}`
  }

  this.iframes.stripeCard = generateAndMountIframe({
    src: stripeIframeAddr,
    classes: stripeIframeClasses,
    container: this.$refs.stripeIframeContainer?.[0],
    autoResize: true,
  })
  this.iframes.stripeEwallet = generateAndMountIframe({
    src: stripeIframeAddr + '?ewallet=true',
    classes: stripeIframeClasses,
    container: this.$refs.stripeIframeEWalletContainer?.[0],
    autoResize: true,
  })

  if (!this.selectedGateway) {
    this.selectedGateway = 'stripe-card'
  }
}

// Post message handlers
const postMessageHandlerTable = {
  paypal_set_frame_state: ({ thisArg, source, args = [false] }) => {
    const isFullScreen = !args[0]
    thisArg.paypalIframeFullScreen = isFullScreen
    source.postMessage({ name: 'set_button_container_state', args: [isFullScreen] }, '*')
  },
  paypal_get_client_id: ({ thisArg, source }) => {
    source.postMessage({ name: 'init_paypal', args: [thisArg.computedPaypalGateway.clientId, thisArg.computedPaypalGateway.paypal_merchant_id] }, '*')
  },
  handle_error: ({ thisArg, args = [] }) => {
    thisArg.paypalIframeFullScreen = false
    thisArg.handleError(args?.[0]?.error?.message)
  },
  paypal_transaction_cancelled: ({ thisArg }) => {
    thisArg.paypalIframeFullScreen = false
    thisArg.$nuxt.$loading.finish()
  },
  paypal_submit_checkout: async ({ thisArg, source }) => {
    thisArg.$nuxt.$loading.start()
    const isValid = await thisArg.handleSubmit()
    thisArg.$nuxt.$loading.finish()

    source.postMessage({
      name: 'set_submitted_checkout',
      args: [isValid]
    }, '*')
  },
  paypal_verify_transaction: async ({ thisArg, args = [] }) => {
    const data = args[0]
    thisArg.$nuxt.$loading.start()

    const orderToken = thisArg.orderToken

    const endpoint = `/public/order/callback/paypal/${orderToken}?token=${data.orderID}`
    const { success } = await thisArg.$axios.$get(endpoint)

    thisArg.$nuxt.$loading.finish()

    if (success) {
      const url = `/order/thank-you/${orderToken}`
      await thisArg.$router.push(thisArg.localePath(url))
    } else {
      thisArg.loading = false
      thisArg.$toast('Transaction failed. Please try again')
    }
  },
  paypal_retrieve_create_order_data: async ({ thisArg, source }) => {
    const data = { orderId: await thisArg.paypalGetOrderId() }
    source.postMessage({
      name: 'set_create_order_data',
      args: [data]
    }, '*')
  },
  stripe_retrieve_data: ({ thisArg, source }) => {
    if (!Object.keys(thisArg.stripeData).length) { return }
    const i18n = {
      locale: thisArg.$i18n.locale,
      card_number: thisArg.$i18n.t('Card number'),
      exp_date: thisArg.$i18n.t('Expiration date'),
      mm_yy: thisArg.$i18n.t('MM/YY'),
      cvc: thisArg.$i18n.t('Security code')
    }
    const returnUrl = thisArg.getReturnUrlStripe()

    source.postMessage({
      name: 'stripe_set_data',
      args: [thisArg.stripeData, i18n, returnUrl],
    }, '*')
  },
  stripe_focus_card: ({ thisArg, args = [] }) => {
    let type = args[0]
    const allowType = ['card', 'banks', 'ewallet']
    if (!allowType.includes(type)) { return }

    type = (type === 'banks') ? 'other' : type
    thisArg.selectedGateway = `stripe-${type}`
  },
  stripe_card_on_change: ({ thisArg, args = [] }) => {
    const event = args[0]

    if (event.brand) {
      thisArg.brandCard = event.brand
    }
    if (event.error) {
      thisArg.handleError(event.error?.message)
    }
  },
  stripe_set_container_height: ({ thisArg, args = [] }) => {
    const [height, frameType] = args
    /**
       * Eslint stupid af.
       * 'eslint-disable' & 'eslint-enable' is required for this block.
       * do not remove any of them
       * eslint rule effecting: multiline-ternary
       */
    /* eslint-disable */
    const iframeSetHeight =
      (frameType === 'card') ? thisArg.iframes.stripeCard :
      (frameType === 'ewallet') ? thisArg.iframes.stripeEwallet :
      (frameType === 'banks') ? thisArg.iframes.stripeBanks
      : null
    /* eslint-enable */

    if (iframeSetHeight) {
      iframeSetHeight.setAttribute('style', `height: calc(${(frameType === 'ewallet') ? 285 : height}px + 1rem);`)
    }
  },
  stripe_create_order_succeed: ({ thisArg }) => {
    thisArg.$store.dispatch('cart/resetData')
    window.location.href = thisArg.localePath({ path: `/order/thank-you/${thisArg.orderToken}` })
  },
}
function attachPostMessageHandler () {
  if (window.addEventListener) {
    window.addEventListener('message', this.postMessageHandler)
  } else {
    // @ts-ignore
    window.attachEvent('onmessage', this.postMessageHandler)
  }
}
function postMessageHandler (messageEvent) {
  const { data: event } = messageEvent

  if (
    typeof event !== 'object' ||
    !event.name ||
    !event.args || !Array.isArray(event.args)
  ) { return }

  if (Object.hasOwnProperty.call(this.postMessageHandlerTable, event.name)) {
    this.postMessageHandlerTable[event.name]({ thisArg: this, source: messageEvent.source, args: event.args })
  }
}
