import lscache from 'lscache'

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['order', 'gateways', 'shippingMethods', 'statement_descriptor'],
  data () {
    return {
      products: this.order?.products,
      storeDetail: this.$store.state.storeInfo
    }
  },
  computed: {
    relatedCart,
    currentShippingMethod,
    currentCountry
  },
  mounted () {
    if ((this.order.type === 'regular' ||
      this.order.type === 'custom') &&
      (!this.order.paid_at ||
        (this.order.paid_at &&
          (new Date(this.order.paid_at).getTime() + 300000) > (new Date().getTime())
        )
      )
    ) {
      setTimeout(() => {
        this.$tracking.trackEvent({
          event: 'purchase',
          options: {
            order_token: this.order.order_number || this.order.id,
            content_ids: [...this.order.products.map(item => item.campaign_id)],
            content_name: [...this.order.products.map(item => item.campaign_title)].join(','),
            content_category: [...this.order.products.map(item => item.product_name)].join(','),
            content_value: [...this.order.products.map(item => item.price)],
            content_quantity: [...this.order.products.map(item => item.quantity)],
            content_type: 'product',
            num_items: this.order.total_quantity,
            currency: 'USD',
            country: this.order.country,
            value: this.order.total_amount
          }
        })
      }, 500)
    }

    // related cart
    const listProductIdCart = {
      type: 'post_sale',
      filter: this.products.filter(item => item.product_id !== null).map((item) => {
        return {
          product_id: item.product_id,
          campaign_id: item.campaign_id,
          template_id: item.template_id
        }
      })
    }

    // save last order
    lscache.set('lastOrder', {
      order_number: this.order.order_number,
      order_token: this.order.access_token
    })

    if (listProductIdCart.filter.length) {
      if (this.storeDetail.smart_remarketing && this.order.id) {
        listProductIdCart.type = 'post_sale'
        listProductIdCart.order_id = parseInt(this.order.id)
        listProductIdCart.source = 'thank_you'
      }

      this.$store.dispatch('cart/postRelatedCart', listProductIdCart)
    }
    this.$store.dispatch('cart/resetData')
    // save coupon
    if (this.storeDetail && this.storeDetail.promotion_title && this.storeDetail.discount_code) {
      this.$store.commit('cart/UPDATE_CART_DATA', { discount: this.storeDetail.discount_code })
    }

    if (this.order.address_verified === 'invalid' && this.$refs.modalConfirmAddress) {
      this.$refs.modalConfirmAddress.isShowModal = true
    }

    const orderKey = lscache.get('orderKey')
    if (orderKey && orderKey.cartKey) {
      this.$store.dispatch('order/updateCartKey', orderKey)
      lscache.set('orderKey', false)
    }
  },
  created () {
    if (process.browser) {
      const lastOrder = localStorage.getItem('lastOrder')
      if (this.order.order_number !== lastOrder) {
        if (this.storeDetail.id === 1) {
          const shareSaleTag = document.createElement('img')
          shareSaleTag.src = `https://www.shareasale.com/sale.cfm?tracking=${this.order.order_number}&amount=${this.order.total_product_amount}&merchantID=116236&transtype=sale`
          shareSaleTag.width = 1
          shareSaleTag.height = 1
          document.head.appendChild(shareSaleTag)
        }

        const googleAdsThankyou = this.$store.state.storeInfo?.tracking_code?.google_ads_gtag_thank_you
        if (googleAdsThankyou) {
          const googleAdsThankyouTag = document.createElement('script')
          googleAdsThankyouTag.type = 'text/javascript'
          googleAdsThankyouTag.text = `gtag('event', 'conversion', {'send_to': '${googleAdsThankyou}','value': ${this.order.total_amount},'currency': 'USD','transaction_id': ''})`
          document.head.appendChild(googleAdsThankyouTag)
        }

        localStorage.setItem('lastOrder', this.order.order_number)
      }
    }
  }
}

function relatedCart () {
  return this.$store.state.cart.relatedCart
}

function currentShippingMethod () {
  return this.shippingMethods ? this.shippingMethods.find(item => item.name === this.order.shipping_method) : null
}

function currentCountry () {
  if (!this.order.country) {
    return ''
  }

  return this.$store.state.generalInfo?.countries.find(country => country.code === this.order.country)
}
