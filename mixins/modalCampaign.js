import campaignMixin from '~/mixins/campaign'

export default {
  name: 'ModalCampaignMixin',
  mixins: [campaignMixin],
  data () {
    return {
      isModal: true,
      isShown: false
    }
  },
  computed: {
    isShowModal: {
      get () {
        return this.$store.state.campaign.isShowModal
      },
      set (value) {
        this.$store.commit('campaign/UPDATE_SHOW_MODAL', value)
      }
    },
    campaignSlug
  },
  watch: {
    isShowModal,
    isShown
  }
}

function isShown (value) {
  if (value && this.campaignData && (this.campaignData.personalized === 1 || this.campaignData.personalized === 2)) {
    this.resetCanvasData()
  }
}

function campaignSlug () {
  return this.$store.state.campaign.currentModalCampaignSlug
}

function isShowModal (value) {
  if (value) {
    this.resetOption()
  }
}
