import sanitizeHtml from 'sanitize-html'
import { fabric } from 'fabric'
// import campaignSeo from './campaignSeo'
import lscache from 'lscache'
import campaignCustom from './campaignCustom'
import campaignBundle from './campaignBundle'
import sizeGuide from './sizeguide'
import productReviewMixin from './productReview'
import { USD_CODE } from '~/helpers/variableConst'
import { getLastUserBehavior, setUserBehavior } from '~/helpers/storage'
import { isMissingRequiredOption, getFilterImages } from '~/helpers/campaign'

export default {
  mixins: [campaignCustom, campaignBundle, sizeGuide, productReviewMixin],
  data () {
    return {
      isShowPrice: false,
      isCheckQuantity: true,
      optionError: '',
      typeSubmit: 0,
      quantity: 1,

      isLoadingAddToCart: false,
      related: [],
      relatedCart: [],

      filterImages: [],
      currentOptions: {},
      currentProduct: false,
      promotionsList: [],
      productStats: false,

      isAddToCartProcess: false,

      selectSizeSubmit: true,
      dynamicBaseCostIndex: 0,
      isLoadedProduct: true,
      isLoadedCustomOption: true,
      extraCustomGroupFee: 0,
      allowCustomCampaignAddToCart: true
    }
  },
  computed: {
    campaignSlug,
    title,
    breadCrumbsList,
    currentCampaign,
    campaignData,
    currentVariant,
    currentPrice,
    currentOldPrice,
    productList,
    variantsList,
    optionList,
    dataDescription,
    dataProductDetail,
    campaignDescription,
    cartTotalQuantity,
    isLoadingUploadImage,
    thumbnail,
    productSlug,
    isCampaignDropdownType
  },
  methods: {
    getfilterImages,
    updateOption,
    updateQuantity,
    addToCart,
    resetOption,
    setDynamicBaseCostIndex,
    updateAllowCustomCampaignAddToCart,
    updateCustomOptionGroupFee,
    resetBundleProduct
  },
  head () {
    if (!this.campaignData || !this.currentProduct) {
      return {}
    }

    const meta = []
    const link = []
    const script = []

    if (this.campaignData && this.campaignData.personalized === 2) {
      link.push({
        rel: 'stylesheet',
        href: 'https://apis.personalbridge.com/style.css'
      })

      script.push({
        src: this.$config.personalBridgeUrl,
        body: true
      })
    }

    // Open Graph tags
    meta.push({
      hid: 'og:type',
      property: 'og:type',
      content: 'product'
    })

    meta.push({
      hid: 'og:title',
      property: 'og:title',
      content: this.title
    })

    meta.push({
      hid: 'og:description',
      property: 'og:description',
      content: this.campaignDescription
    })

    meta.push({
      hid: 'og:url',
      property: 'og:url',
      content: `https://${this.$store.state.storeInfo.domain}/${this.campaignSlug}`
    })

    meta.push({
      hid: 'og:image',
      property: 'og:image',
      content: this.thumbnail
    })

    meta.push({
      hid: 'og:image:alt',
      property: 'og:image:alt',
      content: this.title
    })

    meta.push({
      hid: 'og:site_name',
      property: 'og:site_name',
      content: this.campaignData.store_name || this.$store.state.storeInfo.store_name
    })

    // Product specific OG tags
    meta.push({
      hid: 'product:brand',
      property: 'product:brand',
      content: this.campaignData.store_name
    })

    meta.push({
      hid: 'product:availability',
      property: 'product:availability',
      content: (this.currentVariant && this.currentVariant.out_of_stock) ? 'out of stock' : 'in stock'
    })

    meta.push({
      hid: 'product:condition',
      property: 'product:condition',
      content: 'new'
    })

    meta.push({
      hid: 'product:price:amount',
      property: 'product:price:amount',
      content: this.currentProduct.price
    })

    meta.push({
      hid: 'product:price:currency',
      property: 'product:price:currency',
      content: this.currentProduct.currency_code
    })

    // Add rating if available
    if (this.reviewSummary?.summary?.review_count > 0 && this.reviewDisplay !== 'disable') {
      meta.push({
        hid: 'product:rating:value',
        property: 'product:rating:value',
        content: this.reviewSummary.summary.average_rating
      })

      meta.push({
        hid: 'product:rating:scale',
        property: 'product:rating:scale',
        content: '5'
      })

      meta.push({
        hid: 'product:rating:count',
        property: 'product:rating:count',
        content: this.reviewSummary.summary.review_count
      })
    }

    // Twitter Card tags
    meta.push({
      hid: 'twitter:card',
      name: 'twitter:card',
      content: 'summary_large_image'
    })

    meta.push({
      hid: 'twitter:title',
      name: 'twitter:title',
      content: this.title
    })

    meta.push({
      hid: 'twitter:description',
      name: 'twitter:description',
      content: this.campaignDescription
    })

    meta.push({
      hid: 'twitter:image',
      name: 'twitter:image',
      content: this.thumbnail
    })

    meta.push({
      hid: 'twitter:image:alt',
      name: 'twitter:image:alt',
      content: this.title
    })

    // Additional image properties for better display
    if (this.thumbnail) {
      meta.push({
        hid: 'og:image:width',
        property: 'og:image:width',
        content: '1200'
      })

      meta.push({
        hid: 'og:image:height',
        property: 'og:image:height',
        content: '630'
      })

      meta.push({
        hid: 'og:image:type',
        property: 'og:image:type',
        content: 'image/webp'
      })
    }

    // Canonical link
    link.push({
      hid: 'canonical',
      rel: 'canonical',
      href: `https://${this.$store.state.storeInfo.domain}/${this.campaignSlug}`
    })

    const headConfig = {
      title: this.title,
      meta,
      link
    }

    // Only add script if there are scripts to add
    if (script.length > 0) {
      headConfig.script = script
    }

    return headConfig
  },
  watch: {
    campaignSlug: resetData
  },
  created: resetOption,
  mounted
}

function mounted () {
  this.isShowPrice = true
  setTimeout(async () => {
    await resetData.call(this)
    if (this.$route.query.addToCart) {
      if (this.campaignData.personalized) {
        const checkInterval = setInterval(() => {
          if (this.finishLoading && this.$route.query.addToCart) {
            this.addToCart(1)
            clearInterval(checkInterval)
          }
        }, 1000)
      } else {
        this.addToCart(1)
      }
    }
  }, 200)

  if (this.optionList && this.optionList.size && this.optionList.size.length === 1) {
    this.currentOptions.size = this.optionList.size[0]
    this.$router.push(this.localePath({ path: this.$route.path, query: { ...this.$route.query, ...this.currentOptions } }))
  }

  // reset countdown
  if (this.campaignData && this.campaignData.show_countdown && this.campaignData.end_time) {
    const countdownPeriods = [0, 0, 15 * 60 * 1000, 60 * 60 * 1000, 24 * 60 * 60 * 1000, 7 * 24 * 60 * 60 * 1000]
    const countdownPeriod = countdownPeriods[this.campaignData.show_countdown]
    const visitInfo = this.$getVisitInfo()
    if (!visitInfo.visitTime) {
      visitInfo.visitTime = new Date().getTime()
    }
    let visitTime = visitInfo.visitTime
    let endTime = Date.parse(new Date(this.campaignData.end_time))
    if (endTime < visitTime) {
      endTime = endTime % countdownPeriod
      if (visitTime % countdownPeriod > endTime) {
        visitTime += countdownPeriod
      }
      visitTime = parseInt(visitTime / countdownPeriod)
      endTime = visitTime * countdownPeriod + endTime
      this.campaignData.end_time = endTime
    }
  }

  // enable touch scroll
  const defaultOnTouchStartHandler = fabric.Canvas.prototype._onTouchStart
  fabric.util.object.extend(fabric.Canvas.prototype, {
    _onTouchStart (e) {
      if (this.allowTouchScrolling) {
        return
      }
      // otherwise call the default behavior
      defaultOnTouchStartHandler.call(this, e)
    }
  })

  // fix scale too large
  if (fabric.devicePixelRatio > 2) {
    fabric.devicePixelRatio = 2
  }

  if (this.campaignData?.personalized === 0 && this.currentProduct?.full_printed === 5) {
    this.allowCustomCampaignAddToCart = false
  }
}

function campaignSlug () {
  return this.$store.state.campaign.currentCampaignSlug
}

function productSlug () {
  return this.$store.state.campaign.currentProductSlug
}

function breadCrumbsList () {
  return [{
    text: this.title,
    to: this.localePath(`/category/${this.campaignSlug}`)
  }]
}

function thumbnail () {
  const color = this.$route.query.color
  return this.$imgUrl(((this.currentProduct && (this.currentProduct.thumb_url || this.currentProduct.full_path))), 'share', 'webp', color ? this.$colorVal(color) : '') || ''
}

function currentCampaign () {
  const currentCampaign = this.$store.getters['campaign/getCampaignBySlug'](this.campaignSlug)
  if (currentCampaign && currentCampaign.campaignData) {
    currentCampaign.campaignData = this.$correctTestPriceCampaignAtCampaignDetail(currentCampaign.campaignData)
    if (currentCampaign.campaignData.personalized > 0 && !currentCampaign.campaignData.options?.group) {
      currentCampaign.campaignData.options = {
        group: {
          limit: 1,
          extra_custom_fee: 0,
          name: null
        },
        options: []
      }
    }
  }
  return currentCampaign
}

function campaignData () {
  return this.currentCampaign && this.currentCampaign.campaignData
}

function title () {
  const enableProductNameAfter = this.$store.state.storeInfo.enable_product_name_after ?? 0

  if (this.currentProduct && this.currentProduct.name && parseInt(enableProductNameAfter) === 1) {
    return `${this.campaignData?.name} ${this.currentProduct.name}`
  }

  return this.campaignData?.name
}

function productList () {
  if (this.campaignData?.products) {
    return this.campaignData?.products
  } else {
    return []
  }
}

function variantsList () {
  if (this.isLoadedProduct && this.currentCampaign) {
    const response = this.currentCampaign.variants[this.productSlug]
    if (response) {
      return response
    } else {
      return []
    }
  }
  return []
}

function getfilterImages () {
  this.filterImages = Object.freeze(getFilterImages(this.currentCampaign, this.currentProduct, this.currentOptions.color))
}

function optionList () {
  if (this.currentProduct && this.currentProduct.options) {
    const optionList = JSON.parse(this.currentProduct.options)
    // eslint-disable-next-line no-prototype-builtins
    if (optionList.hasOwnProperty('custom_options')) {
      if (!this.currentProduct.template_custom_options) {
        this.currentProduct.template_custom_options = optionList.custom_options
      }
      delete optionList.custom_options
    }
    return optionList
  }
  return {}
}

function dataDescription () {
  return sanitizeHtml(this.campaignData.description || '')
}

function campaignDescription () {
  if (!this.campaignData.description) {
    return this.campaignData.name
  }

  return this.campaignData.description.replace(/<[^>]+>/g, '').replace(/\s\s+/g, ' ')
}

function dataProductDetail () {
  if (this.currentProduct) {
    return sanitizeHtml(this.currentProduct.description || '')
  }

  return sanitizeHtml(this.campaignData.description || '')
}

function cartTotalQuantity () {
  return this.$store.getters['cart/getTotalQuantity']
}

function currentVariant () {
  this.setDynamicBaseCostIndex()
  const variantKey = Object.keys(this.optionList).map(option => this.optionList[option].length === 1 ? this.optionList[option][0] : this.currentOptions[option]).toString().replace(/(-|\s)/g, '_').replace(/,/g, '-')
  // const variantList = this.$store.state.campaign.productVariants[this.currentProduct.id]
  let variantList = lscache.get('product-variants') ? lscache.get('product-variants')[this.currentProduct.id] ?? [] : []

  if (this.isLoadedProduct && variantList) {
    variantList = variantList.filter(variant => variant.variant_key === variantKey && variant.product_id === this.currentProduct.id)
    const userCountry = this.$userCountryForPricing()
    return this.$getBaseCostOfTemplateDependOnUserCountry(userCountry, this.$store.state.generalInfo.countries, variantList)
  } else {
    return []
  }
}

function currentPrice () {
  if (this.currentVariant?.price) {
    return this.currentVariant.price + this.dynamicBaseCostIndex + this.extraCustomFee || this.currentProduct.customFeePrice || 0
  } else {
    return this.currentProduct.price + this.dynamicBaseCostIndex + (this.extraCustomFee || this.currentProduct.customFeePrice || 0)
  }
}

function currentOldPrice () {
  if (this.currentVariant && this.currentVariant.price) {
    return this.currentVariant.old_price > this.currentVariant.price ? (this.currentVariant.old_price + this.dynamicBaseCostIndex + this.extraCustomFee) : 0
  } else {
    return this.currentProduct.old_price > this.currentProduct.price ? (this.currentProduct.old_price + this.dynamicBaseCostIndex + this.extraCustomFee) : 0
  }
}
// methods

async function updateOption (query, submit) {
  if (!process.browser) {
    return
  }
  this.isLoadedProduct = false
  this.isLoadedCustomOption = false
  if (query.product) {
    const product = this.productList.find(product => this.$toSlug(product.name) === query.product)
    if (product && product.full_printed === 5) {
      this.customFeePrice = 0
    }
    this.resetOption(product)
    this.currentProduct.videos = product.videos

    getRelatedCampaign.call(this)
  } else {
    Object.keys(query).forEach((option) => {
      setUserBehavior(option, query[option], this.currentProduct?.name)
    })
  }
  this.currentOptions = { ...this.currentOptions, ...query }
  if (query.color) {
    this.getfilterImages()
  }
  if (submit) {
    this.addToCart()
  }
  if (!this.isModal) {
    this.$router.push(this.localePath({ query: { ...this.$route.query, ...this.currentOptions } }))
  }
  this.$nextTick(() => {
    if (this.currentProduct.full_printed !== 5) {
      this.allowCustomCampaignAddToCart = true
    }
    if ((query.size && ![4, 5, 0].includes(this.currentProduct.full_printed)) || (query.product && (this.campaignData.personalized === 1 || this.campaignData.personalized === 2))) {
      this.resetCanvasData()
    }
  })
  this.isLoadedCustomOption = true
  await this.$store.dispatch('campaign/getVariantsProduct', {
    productId: this.currentProduct.id,
    sellerId: this.campaignData?.seller_id ?? this.$store.state.storeInfo.seller_id
  })

  this.isLoadedProduct = true
}

function updateQuantity (quantity) {
  this.$tracking.customTracking({ event: 'interact', action: 'change_quantity' })
  this.quantity = Number(quantity)
}

async function addToCart (type, confirmDesign) {
  if (!type) {
    type = this.typeSubmit
  } else {
    this.typeSubmit = type
  }

  if (type === 2) {
    this.isCheckQuantity = true
  }

  const checkOptions = Object.keys(this.optionList).find((item, index) => {
    return !this.currentOptions[item] || this.currentOptions[item] === 'false'
  })

  if (checkOptions) {
    if (checkOptions === 'size') {
      this.$refs.modalSelectSize.isShowModalSelectSize = true
      this.selectSizeSubmit = true
      return
    } else {
      return this.$toast.error(this.$t('Please choose {option}', { option: checkOptions }))
    }
  }
  if (!confirmDesign) {
    let hasPersonalized = false

    if (this.campaignData.personalized || this.currentProduct?.full_printed === 5) {
      if (this.campaignData.personalized === 1 && this.filterDesigns && this.filterDesigns.length) {
        hasPersonalized = true
        if (!this.currentDesign || !this.isShowDesign) {
          this.changeCurrentDesign(this.filterDesigns[0])
        }
        if (!this.filterDesigns.every(design => design.loaded)) {
          return setTimeout(() => {
            this.addToCart()
          }, 200)
        }
        const checkInputText = this.customTextList && this.customTextList.length && this.customTextList.find(item => !item.data.text)
        if (checkInputText) {
          this.selectedCustom = checkInputText
          this.optionError = 'customText'
          if (this.trackerTimeoutoptionError) {
            clearTimeout(this.trackerTimeoutoptionError)
          }

          this.trackerTimeoutoptionError = setTimeout(() => {
            this.optionError = ''
          }, 2000)

          this.$nextTick(() => {
            if (window.innerWidth > 768 || this.isModal) {
              document.getElementsByClassName('require-input')[0]?.focus()
            } else {
              document.getElementById('bottomCustomTextInput')?.focus()
            }
          })

          return
        }

        if (this.customImageList && this.customImageList.length && !this.currentFileUploadUrl) {
          this.optionError = 'customImage'
          this.selectedCustom = this.customImageList[0]
          if (this.trackerTimeoutoptionError) {
            clearTimeout(this.trackerTimeoutoptionError)
          }

          this.trackerTimeoutoptionError = setTimeout(() => {
            this.optionError = ''
          }, 2000)
          return
        }

        if (this.isImageLowQuanlity && !this.isConfirmQuantityImage) {
          this.isShowModalConfirmQuantityImage = true
          this.isAddToCartProcess = true
          return
        }
      } else if (this.campaignData.personalized === 2 && this.filterDesigns && this.filterDesigns.length) {
        if (!this.currentDesign) {
          this.changeCurrentDesign(this.filterDesigns[0])
        }
        hasPersonalized = true
        const designData = this.currentDesign.designData
        const validated = await window.pbsdk.validate(`design_${designData && designData.campaign_id}_${designData && designData.print_space}`)
        if (!validated) {
          try {
            document.getElementsByClassName('ant-form-explain')[0].parentNode.parentNode.parentNode.scrollIntoView()
          } catch (error) {
            return
          }
          return
        }
      } else if (this.campaignData.personalized === 3 || this.currentProduct?.full_printed === 5) {
        const customOptions = this.currentProduct?.template_custom_options?.options || this.campaignData?.options?.options
        const commonOptions = this.currentProduct?.common_options?.options || this.campaignData?.common_options?.options
        const customerCustomOptions = this.customerCustomOptions
        if (isMissingRequiredOption(customOptions, commonOptions, customerCustomOptions)) {
          this.$toast.error(this.$t('Please fill in at least one option'))
          const invalidInput = document.getElementsByClassName('is-invalid')
          if (invalidInput.length) {
            invalidInput[0].scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            })
          }
          return
        }
      }
    }
    if (type === 3) {
      if (this.bundleDiscount.products.find(item => item.personalized)) {
        hasPersonalized = true
      }
      const filtered = this.bundleDiscount.products.filter((item, index) => item.isSelected && index < 3)
      const checkBundleCustom = await this.checkProductUnfinished(filtered)
      if (checkBundleCustom) {
        this.openBundleProduct(checkBundleCustom)
        return
      }
    }

    if (hasPersonalized) {
      return this.getConfirmDesignImage(type === 3)
    }
  }
  this.isLoadingAddToCart = true
  const result = await this.$store.dispatch('cart/addProduct', {
    product: this.currentProduct,
    campaign: this.campaignData,
    options: this.currentOptions,
    optionList: this.optionList,
    quantity: this.quantity,
    filterImages: this.filterImages,
    variant: this.currentVariant,
    isCheckQuantity: this.isCheckQuantity,
    filterDesigns: this.filterDesigns,
    customerCustomOptions: this.customerCustomOptions,
    extraCustomFee: this.extraCustomFee
  })

  if (result.success) {
    // for modalcampaign
    this.$store.commit('cart/UPDATE_CART_ITEM_BY_ID', {
      cartItemId: result.cartItemId,
      data: {
        dynamic_base_index: this.dynamicBaseCostIndex
      }
    })
    this.isShowModal = false
    this.$store.commit('updateLoadingUploadImage', true)
    if ((this.campaignData.personalized === 1 || this.campaignData.personalized === 2) && this.filterDesigns && this.filterDesigns.length) {
      window.loadingImage1 = this.getMockupUpload(this.filterDesigns).then((mockupUpload) => {
        if (mockupUpload) {
          const updateData = {}
          this.filterDesigns.forEach((design, index) => {
            this.filterDesigns[index].fileUpload = mockupUpload[index].key || mockupUpload[index].Key
            updateData[`thumb_${design.designData.print_space}_url`] = mockupUpload[index].Key
          })
          updateData.thumb_url = mockupUpload[0].Key
          this.$store.commit('cart/UPDATE_CART_ITEM_BY_ID', { cartItemId: result.cartItemId, data: updateData })
        }
        this.$store.commit('updateLoadingUploadImage', false)
        return mockupUpload
      }).catch(() => {
        this.$store.commit('updateLoadingUploadImage', false)
      })
    } else {
      this.$store.commit('updateLoadingUploadImage', false)
    }
    if (type === 1) {
      this.isLoadingAddToCart = false
      if (window.innerWidth < 768) {
        return this.$router.push(this.localePath({ path: '/cart' }))
      }
      this.$refs.modalAddToCart.isShowModalAddToCart = true
    } else if (type === 2) {
      if (window.loadingImage1) {
        await window.loadingImage1
      }
      this.$store.dispatch('order/createOrder')
      this.isLoadingAddToCart = false
    } else if (type === 3) {
      this.isLoadingAddToCart = false
      this.$store.commit('updateLoadingUploadImage', true)
      this.$store.dispatch('cart/addProductBundleDiscount', {
        bundleDiscount: this.bundleDiscount,
        campaignBundleId: this.campaignData.id,
        productBundleId: this.currentProduct.id
      }).then((result2) => {
        if (result2 && result2.success) {
          const productCustom = this.bundleDiscount.products.filter((product, index) => product.personalized && product.isSelected && index < 3)
          window.loadingImage2 = Promise.all(productCustom.map((product) => {
            if ((product.personalized === 1 || product.personalized === 2) && product.filterDesigns && product.filterDesigns.length) {
              return this.getMockupUpload(product.filterDesigns)
            } else {
              return false
            }
          })).then((productMockupUpload) => {
            productCustom.forEach((product, pIndex) => {
              const mockupUpload = productMockupUpload[pIndex]
              if (mockupUpload) {
                const updateData = {}
                product.filterDesigns.forEach((design, index) => {
                  product.filterDesigns[index].fileUpload = mockupUpload[index].key || mockupUpload[index].Key
                  updateData[`thumb_${design.designData.print_space}_url`] = mockupUpload[index].Key
                })
                updateData.thumb_url = mockupUpload[0].Key
                this.$store.commit('cart/UPDATE_CART_ITEM_BY_ID', { cartItemId: result2.cartItemIdList[pIndex], data: updateData })
              }
            })
            this.$store.commit('updateLoadingUploadImage', false)
            return productMockupUpload
          }).catch(() => {
            this.$store.commit('updateLoadingUploadImage', false)
            this.isLoadingAddToCart = false
          })
          return this.$router.push(this.localePath({ path: '/cart' }))
        } else {
          this.$store.commit('updateLoadingUploadImage', false)
          this.isLoadingAddToCart = false
          this.$toast.error(result2.message)
        }
      }).catch((error) => {
        this.$store.commit('updateLoadingUploadImage', false)
        this.isLoadingAddToCart = false
        this.$toast.error(error.message)
      })
    }
  } else {
    this.$store.commit('updateLoadingUploadImage', false)
    this.isLoadingAddToCart = false
    this.$toast.error(result.message)
  }
}

// life-cycle
function resetOption (product) {
  if (!(this.campaignData?.products?.length)) {
    return
  }
  const newOption = {}

  if (!this.isModal && !product && this.$route.query.product && this.productList.find(product => this.$toKey(product.name) === this.$toKey(this.$route.query.product))) {
    newOption.product = this.$route.query.product
  }

  if (product) {
    this.currentProduct = product
  } else if (newOption.product) {
    this.currentProduct = this.productList.find(product => this.$toKey(product.name) === this.$toKey(newOption.product)) || this.productList[0]
  } else if (this.campaignData?.default_product_id) {
    this.currentProduct = this.productList.find(product => product.id === this.campaignData.default_product_id) || this.productList[0]
  } else {
    this.currentProduct = this.productList[0]
  }
  if (!this.currentProduct) {
    return
  }

  const optionList = JSON.parse(this.currentProduct.options)
  // eslint-disable-next-line no-prototype-builtins
  if (optionList.hasOwnProperty('custom_options') && !this.currentProduct.hasOwnProperty('template_custom_options')) {
    this.currentProduct.template_custom_options = optionList.custom_options
    delete optionList.custom_options
  }
  if (optionList && Object.keys(optionList).length > 0) {
    Object.keys(optionList).forEach((item, index) => {
      const lastOption = getLastUserBehavior(item, this.currentProduct.name)
      if (item === 'size' || item === 'color') {
        if (lastOption && optionList[item].includes(lastOption.replace(/-/g, ' '))) {
          newOption[item] = lastOption
        } else if (index === 0 && this.currentProduct.default_option) {
          newOption[item] = this.currentProduct.default_option.replace(/ /g, '-')
        } else {
          newOption[item] = optionList[item][0]
        }
        return
      }
      if (optionList[item]?.length === 1) {
        newOption[item] = `__${optionList[item][0].replace(/ /g, '-')}`
      } else if (!this.isModal && !product && this.$route.query[item] && optionList[item].includes(this.$route.query[item].replace(/-/g, ' '))) {
        newOption[item] = this.$route.query[item]
      } else if (lastOption && optionList[item].includes(lastOption.replace(/-/g, ' '))) {
        newOption[item] = lastOption
      } else if (index === 0 && this.currentProduct.default_option) {
        newOption[item] = this.currentProduct.default_option.replace(/ /g, '-')
      } else {
        newOption[item] = optionList[item][0].replace(/ /g, '-')
      }
    })
  }
  if (newOption?.color?.startsWith('__')) {
    newOption.color = newOption.color.slice(2)
  }

  this.currentOptions = newOption
  if (this.currentCampaign?.images) {
    setTimeout(() => {
      const listImgPreLoad = this.currentCampaign.images.filter(image => image.product_id === this.currentProduct.id || image.product_id === this.currentProduct.template_id)
      listImgPreLoad.forEach((image) => {
        this.$preloadImg(image.file_url, 'full_hd')
      })
    }, 3000)
  }

  this.getfilterImages()
}

// helper
function getRelatedCampaign () {
  if (this.currentProduct.related && this.currentProduct.relatedCart) {
    this.related = this.currentProduct.related
    this.relatedCart = this.currentProduct.relatedCart
    return
  }
  this.relatedCart = []
  const listProductIdCart = {
    filter: []
  }

  const itemProduct = {}

  if (this.currentProduct) {
    itemProduct.product_id = this.currentProduct.id
    itemProduct.campaign_id = this.currentProduct.campaign_id

    if (this.currentProduct.template_id) {
      itemProduct.template_id = this.currentProduct.template_id
    }
  }

  listProductIdCart.filter.push(itemProduct)
  listProductIdCart.type = 'cart'

  const relatedParam = {}
  relatedParam.filter = []
  const paramFilter = {}
  paramFilter.campaign_id = this.campaignData && this.campaignData.id

  if (this.currentProduct) {
    paramFilter.product_id = this.currentProduct.id

    if (this.currentProduct.template_id) {
      paramFilter.template_id = this.currentProduct.template_id
    }
  }

  relatedParam.filter.push(paramFilter)
  relatedParam.type = 'related'

  this.$store.dispatch('cart/getRelatedCart', relatedParam).then((res) => {
    this.related = Object.freeze((res && res.data) || [])
    this.currentProduct.related = this.related
  })
  if (listProductIdCart.filter.length > 0) {
    this.$store.dispatch('cart/getRelatedCart', listProductIdCart).then((res) => {
      this.relatedCart = Object.freeze((res && res.data) || [])
      this.currentProduct.relatedCart = this.relatedCart
    })
  }
}

async function resetData () {
  this.isCheckQuantity = true
  this.isShowDesign = false
  this.customImage = false
  this.currentFileUploadUrl = false
  this.isLoadingAddToCart = false
  this.fileUpload = false
  this.$nextTick(() => {
    if (this.campaignSlug) {
      if (this.campaignData.personalized) {
        this.getCampaignCustom(this.campaignSlug)
      }

      if (this.campaignData?.id) {
        const price = this.currentPrice || 0
        this.$tracking.trackEvent({
          event: 'view_content',
          options: {
            content_ids: [this.campaignData?.id],
            content_name: this.campaignData?.name,
            content_category: this.currentProduct?.name,
            content_value: [price],
            content_quantity: [this.quantity],
            content_type: 'product',
            currency: 'USD',
            value: this.$formatPriceNoUnit(price * (Number(this.quantity) || 1), this.currentProduct.currency_code, 'USD', true),
            klaviyoData: {
              slug: this.currentCampaign?.campaignData?.slug || '',
              image_url: this.campaignData?.thumb_url || '',
              product_id: this.currentProduct.id || '',
            }
          },
          campaignId: this.campaignData?.id
        })
        // this.getProductsSimilar(this.campaignData?.id)
      }
      const intervalTracking = setInterval(() => {
        if (window.SP_VISIT_ACTIVITY >= 1) {
          this.$store.dispatch('campaign/getPromotion', { campaignSlug: this.campaignSlug, campaignIds: [this.campaignData?.id] }).then(({ success, data }) => {
            if (success) {
              this.promotionsList = Object.freeze(data)
            }
          })

          getRelatedCampaign.call(this)
          clearInterval(intervalTracking)
          this.$store.dispatch('campaign/getProductStats', { campaignSlug: this.campaignSlug, campaign_id: String(this.campaignData?.id), params: this.$route.query }).then(({ success, data }) => {
            if (success && data && data.length) {
              const object = {}
              data.forEach((item) => {
                object[item.type] = item.count
              })
              this.productStats = Object.freeze(object)
            }
          })

          this.getBundleDiscount()
        }
      }, 500)

      setTimeout(() => {
        if (intervalTracking) {
          clearInterval(intervalTracking)
        }
      }, 15000)
    }
  })
  await this.$store.dispatch('campaign/getVariantsProduct', {
    productId: this.currentProduct.id,
    sellerId: this.campaignData?.seller_id ?? this.$store.state.storeInfo.seller_id
  })
}

function isLoadingUploadImage () {
  return this.$store.state.isLoadingUploadImage
}

function setDynamicBaseCostIndex () {
  // const productVariants = this.$store.state.campaign.productVariants[this.currentProduct.id]
  const productVariants = lscache.get('product-variants') ? lscache.get('product-variants')[this.currentProduct.id] : []

  if (this.currentCampaign && productVariants) {
    const currentCampData = this.currentCampaign
    currentCampData.market_location = this.currentCampaign.campaignData.market_location
    currentCampData.pricing_mode = this.currentCampaign.campaignData.pricing_mode ?? null
    const dynamicBaseIndex = this.$getDynamicBaseCostIndex({
      currentCampaign: currentCampData,
      currentOption: this.currentOptions,
      optionList: this.optionList,
      variants: productVariants,
      variantsCurrency: this.currentProduct.currency_code ?? USD_CODE,
      currentProduct: this.currentProduct
    })
    this.dynamicBaseCostIndex = dynamicBaseIndex
  }
}

function updateAllowCustomCampaignAddToCart (value) {
  this.allowCustomCampaignAddToCart = value
}

function updateCustomOptionGroupFee (value) {
  if (this.currentBundleDiscount) {
    this.currentBundleDiscount.customFeePrice = parseFloat(value)
  }
  if (this.currentProduct) {
    this.currentProduct.customFeePrice = parseFloat(value)
  }
  this.customFeePrice = parseFloat(value)
}

function resetBundleProduct (productIds) {
  this.getBundleDiscount(productIds, true)
}

function isCampaignDropdownType () {
  return this.$store.state.storeInfo.product_select_type === 'dropdown' || this.$store.state.storeInfo.store_type === 'express_listing'
}
