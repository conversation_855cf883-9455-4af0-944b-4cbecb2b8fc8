
export default {
  head () {
    const script = getTrackingScript(this.$store.state.storeInfo)
    const link = getCommonLink.call(this)
    const style = []
    const tags = this.$headTagFilter(this.$store.state.storeInfo.head_tags, this.$route.path)

    if (tags) {
      tags.forEach((tag) => {
        switch (tag.tag) {
          case 'script':
            script.push(this.createTagObj(tag))
            break

          case 'script_src': {
            const adp = (typeof tag?.additional_properties === 'string') ? JSON.parse(tag.additional_properties) : ({})
            script.push({
              src: tag.code,
              async: adp?.async,
              defer: adp?.defer,
              body: (tag.position === 'body'),
            })
            break
          }

          case 'style':
            style.push(this.createTagObj(tag))
            break
        }
      })
    }

    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true })
    for (const lnk of i18nHead.link) {
      lnk.href = `https://${this.$store.state.storeInfo.domain || this.$store.state.storeInfo.currentDomain}${lnk.href}`
    }
    link.push(...i18nHead.link)
    const rawHeadTags = this.$store.getters['storeInfo/getStoreHeadTags'].head || []
    let headTags = rawHeadTags.map((tag) => {
      const match = tag.code.match(/<(\w+)\s+(.+?)>/)
      if (match) {
        const attributes = match[2]
        const tagObject = {}
        const attrMatches = attributes.match(/(\w+)="([^"]*?)"/g)
        if (attrMatches) {
          attrMatches.forEach((attr) => {
            const [key, value] = attr.split('=')
            tagObject[key] = value.replace(/"/g, '')
          })
        }
        return tagObject
      }
      return null
    }).filter(Boolean)
    headTags = this.$headTagFilter(headTags)
    const meta = [...i18nHead.meta, ...headTags]
    return {
      meta,
      script,
      link,
      style,
      __dangerouslyDisableSanitizers: ['script', 'style']
    }
  },
  watch: {
    $route () {
      if (window.gtag) {
        window.gtag('set', 'page_path', this.$route.fullPath)
      }
      /* eslint-disable camelcase */
      this.$tracking.trackEvent({ event: 'page_view' })
      const { utm_source, source, utm_campaign, utm_medium, seid } = this.$route.query
      if (utm_source || source || utm_campaign || utm_medium || seid) {
        this.$updateAdsSource(this.$route.query)
      }
    }
  },
  mounted () {
    this.$tracking.initTracking().then(() => {
      this.$tracking.trackEvent({ event: 'page_view' })
      const lastLanguage = this.$i18n.getLocaleCookie()
      if (!lastLanguage) {
        const defaultLanguage = this.$store.state.storeInfo?.default_language
        if (defaultLanguage) {
          let updateLanguage = this.$i18n.defaultLocale
          if (this.$i18n.localeCodes.includes(defaultLanguage)) {
            updateLanguage = defaultLanguage
          }
          this.$router.push(this.switchLocalePath(updateLanguage))
          this.$i18n.setLocale(updateLanguage)
        }
      } else {
        this.$router.push(this.switchLocalePath(lastLanguage))
      }
    })

    if (this.$route.query.discount) {
      this.$store.commit('cart/UPDATE_CART_DATA', { discount: this.$route.query.discount })
    }

    if (!window.SP_LISTENED_EVENT) {
      this.listenClickEvent()
      this.listenInputsChange()
      this.listenMouseMove()
      this.listenScroll()
      this.listenFileUploadEvent()
      this.listenCustomInputChange()
      //      this.listenChangeUrl()
      window.SP_LISTENED_EVENT = true
      if (typeof window.SP_VISIT_ACTIVITY === 'undefined') {
        window.SP_VISIT_ACTIVITY = 0
      }
    }
  },
  methods: {
    track (event, action, elementName, data = {}) {
      window.SP_VISIT_ACTIVITY++
      this.$tracking.retrackEvent()
      let campaignId = null
      if (this.$route.name.startsWith('campaign___')) {
        const currentCampaign = this.$store.state.campaign.campaignList[this.$store.state.campaign.currentCampaignSlug]?.campaignData
        campaignId = currentCampaign?.id
      }
      if (campaignId) {
        data.campaignId = campaignId
      }
      this.$tracking.newCustomTracking(event, action, elementName, data)
    },
    listenFileUploadEvent () {
      window.addEventListener('sp_file_uploaded', (e) => {
        this.track('file_uploaded', '', '', { value: e.detail }) // file path will be supplied in "detail" field
      })
    },
    listenCustomInputChange () {
      window.addEventListener('sp_custom_input_change', (e) => {
        this.track('custom_input_change', '', '', { value: e.detail })
      })
    },
    listenClickEvent () {
      window.addEventListener('click', (e) => {
        this.track('click', this.getActionName(e.target), this.getElementName(e.target))
      })
    },
    listenInputsChange () {
      window.addEventListener('change', (e) => {
        this.track('input_change', this.getActionName(e.target), this.getElementName(e.target), { value: e.target.value })
      })
    },
    listenMouseMove () {
      let mouseMoveEndedInterval
      document.addEventListener('mousemove', (e) => {
        if (mouseMoveEndedInterval) {
          clearInterval(mouseMoveEndedInterval)
        }

        mouseMoveEndedInterval = setTimeout(() => {
          this.track('mouse_move', this.getActionName(e.target), this.getElementName(e.target), { point: [e.clientX, e.clientY] })
        }, 200)
      })
    },
    listenScroll () {
      let scrollTimeoutInterval
      document.addEventListener(
        'scroll',
        (e) => {
          // handle scroll event
          // console.log(document.body.scrollTop, document.body.scrollLeft);

          if (scrollTimeoutInterval) {
            clearTimeout(scrollTimeoutInterval)
          }
          // handle scroll event
          // console.log(document.body.scrollTop, document.body.scrollLeft);

          scrollTimeoutInterval = setTimeout(() => {
            this.track('scroll', null, null, { point: [window.scrollX, window.scrollY] })
          }, 200)
        },
        { passive: true }

      )
    },
    listenChangeUrl () {
      window.addEventListener('popstate', function (event) {
        // Log the state data to the console
        // console.log(event.state, window.location.href)
        this.track('visit', window.location.href)
      })
    },
    addVisitEvent () {
      this.track('visit')
    },
    getElementAttribute (elm, attributes) {
      for (const attributeName of attributes) { // You can use `let` instead of `const` if you like
        let attribute = elm.getAttribute(attributeName)

        if (attribute != null) {
          attribute = attribute.replace(/^\s+|\s+$/gm, '') || ''
        }

        if (attribute != null && attribute.length > 0) {
          return attribute
        }
      }

      const parent = elm.parentElement
      if (parent == null) {
        return ''
      } else {
        return this.getElementAttribute(parent, attributes)
      }
    },
    getActionName (elm) {
      return this.getElementAttribute(elm, ['sp-action'])
    },
    getElementName (elm) {
      const maxLength = 50
      let text = elm.textContent
      if (text.length > maxLength) {
        text = text.slice(0, maxLength) + '...'
      }
      if (text.length !== 0) {
        return text.replace(/^\s+|\s+$/gm, '')
      } else {
        return this.getElementAttribute(elm, ['sp-name', 'name', 'id', 'title', 'alt'])
      }
    },
    createXPathFromElement (elm) {
      const allNodes = document.getElementsByTagName('*')
      let segs
      for (segs = []; elm && elm.nodeType === 1; elm = elm.parentNode) {
        if (elm.hasAttribute('id')) {
          let uniqueIdCount = 0
          for (let n = 0; n < allNodes.length; n++) {
            if (allNodes[n].hasAttribute('id') && allNodes[n].id === elm.id) { uniqueIdCount++ }
            if (uniqueIdCount > 1) { break }
          };
          if (uniqueIdCount === 1) {
            segs.unshift('id("' + elm.getAttribute('id') + '")')
            return segs.join('/')
          } else {
            segs.unshift(elm.localName.toLowerCase() + '[@id="' + elm.getAttribute('id') + '"]')
          }
        } else if (elm.hasAttribute('class')) {
          segs.unshift(elm.localName.toLowerCase() + '[@class="' + elm.getAttribute('class') + '"]')
        } else {
          let i, sib
          for (i = 1, sib = elm.previousSibling; sib; sib = sib.previousSibling) {
            if (sib.localName === elm.localName) { i++ }
          };
          segs.unshift(elm.localName.toLowerCase() + '[' + i + ']')
        };
      };
      return segs.length ? '/' + segs.join('/') : null
    },
    createTagObj (tag) {
      // ignore errors to keep the storefront running
      const injectTag = {
        innerHTML: `try {${tag.code}} catch(e) {/* noop */}`
      }

      if (tag.tag === 'style') {
        injectTag.innerHTML = tag.code
      }

      if (tag.position === 'body') {
        injectTag.body = true
      }

      return injectTag
    }

  }
}

function getTrackingScript (storeInfo) {
  const tiktokPixel = storeInfo?.tracking_code?.tiktok_pixel
  const pinterestTagId = storeInfo?.tracking_code?.pinterest_tag_id
  const snapPixel = storeInfo?.tracking_code?.snapchat_pixel
  const quoraPixel = storeInfo?.tracking_code?.quora_pixel
  const bingPixel = storeInfo?.tracking_code?.bing_pixel
  const klaviyoPublicKey = storeInfo?.tracking_code?.klaviyo_public_key
  const redditPixel = storeInfo?.tracking_code?.reddit_pixel

  const script = getGoogleScript(storeInfo)
  if (tiktokPixel) {
    script.push({
      type: 'text/javascript',
      innerHTML: `
        !function (w, d, t) {
        w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};
        ttq.load('${tiktokPixel}');
        }(window, document, 'ttq');
      `
    })
  }

  if (klaviyoPublicKey) {
    script.push({
      type: 'text/javascript',
      async: true,
      src: `//static.klaviyo.com/onsite/js/klaviyo.js?company_id=${klaviyoPublicKey}`,
      body: true
    })
  }

  if (pinterestTagId) {
    script.push({
      type: 'text/javascript',
      innerHTML: `
      !function(e){if(!window.pintrk){window.pintrk=function(){window.pintrk.queue.push(
        Array.prototype.slice.call(arguments))};var
        n=window.pintrk;n.queue=[],n.version="3.0";var
        t=document.createElement("script");t.async=!0,t.src=e;var
        r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(t,r)}}("https://s.pinimg.com/ct/core.js");
        pintrk('load', '${pinterestTagId}');
        pintrk('page');
      `
    })
  }

  if (snapPixel) {
    script.push({
      type: 'text/javascript',
      innerHTML: `
      (function(e,t,n){if(e.snaptr)return;var a=e.snaptr=function()
        {a.handleRequest?a.handleRequest.apply(a,arguments):a.queue.push(arguments)};
        a.queue=[];var s='script';r=t.createElement(s);r.async=!0;
        r.src=n;var u=t.getElementsByTagName(s)[0];
        u.parentNode.insertBefore(r,u);})(window,document,
        'https://sc-static.net/scevent.min.js');
        snaptr('init', '${snapPixel}');
      `
    })
  }

  if (quoraPixel) {
    script.push({
      type: 'text/javascript',
      innerHTML: `
      !function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
      qp('init', '${quoraPixel}');
      `
    })
  }

  if (bingPixel) {
    script.push({
      type: 'text/javascript',
      innerHTML: `(function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[] ,f=function(){var o={ti:"${bingPixel}", enableAutoSpaTracking: true}; o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")} ,n=d.createElement(t),n.src=r,n.async=1,n.onload=n .onreadystatechange=function() {var s=this.readyState;s &&s!=="loaded"&& s!=="complete"||(f(),n.onload=n. onreadystatechange=null)},i= d.getElementsByTagName(t)[0],i. parentNode.insertBefore(n,i)})(window,document,"script"," //bat.bing.com/bat.js","uetq");`
    })
  }

  if (redditPixel) {
    script.push({
      type: 'text/javascript',
      innerHTML: `
        !function(w,d){if(!w.rdt){var p=w.rdt=function(){p.sendEvent?p.sendEvent.apply(p,arguments):p.callQueue.push(arguments)};p.callQueue=[];var t=d.createElement("script");t.src="https://www.redditstatic.com/ads/pixel.js",t.async=!0;var s=d.getElementsByTagName("script")[0];s.parentNode.insertBefore(t,s)}}(window,document);
        rdt('init','${redditPixel}');`
    })
  }

  // facebook
  script.push({
    type: 'text/javascript',
    innerHTML: `
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
      `
  })
  return script
}

function getCommonLink () {
  const favicon = this.$store.state.storeInfo.favicon
  if (favicon) {
    return [
      {
        hid: 'icon',
        rel: 'icon',
        type: 'image/x-icon',
        href: this.$imgUrl(favicon, 'icon') || ''
      },
      {
        hid: 'apple-touch-icon',
        rel: 'apple-touch-icon',
        href: this.$imgUrl(favicon, 'icon-xl') || ''
      },
      {
        hid: 'shortcut-icon',
        rel: 'shortcut icon',
        href: this.$imgUrl(favicon, 'icon-lg') || ''
      },
      {
        hid: 'theme',
        rel: 'stylesheet',
        href: `${this.$config.publicPath || ''}/css/themes/default/index.min.css?v=1`
      }
    ]
  } else {
    return [
      {
        hid: 'theme',
        rel: 'stylesheet',
        href: `${this.$config.publicPath || ''}/css/themes/default/index.min.css?v=1`
      }
    ]
  }
}

function getGoogleScript (storeInfo) {
  const googleAdsGtag = storeInfo?.tracking_code?.google_ads_gtag
  const googleAnalytics = storeInfo?.tracking_code?.google_analytics
  const googloAdwordsId = storeInfo?.tracking_code?.google_adwords_id
  let trackingCodeAccountLevel = storeInfo?.trackingCode
  if (trackingCodeAccountLevel && typeof trackingCodeAccountLevel !== 'object') {
    trackingCodeAccountLevel = JSON.parse(trackingCodeAccountLevel)
  }

  const googleAnalyticsAccountLevel = trackingCodeAccountLevel?.google_analytics
  const googloAdwordsIdAccountLevel = trackingCodeAccountLevel?.google_adwords
  const googleTagAccountLevel = trackingCodeAccountLevel?.google_gtag
  const googleIds = [googleAdsGtag, googleAnalytics, googloAdwordsId, googleAnalyticsAccountLevel, googloAdwordsIdAccountLevel, googleTagAccountLevel].filter((item, index, array) => item && array.indexOf(item) === index)
  const script = []

  if (googleIds && googleIds.length) {
    let gtagScriptExists = false
    googleIds.forEach((item, index) => {
      if (!gtagScriptExists) {
        script.push({
          type: 'text/javascript',
          src: 'https://www.googletagmanager.com/gtag/js',
          async: true
        })
        script.push({
          type: 'text/javascript',
          innerHTML: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
            `
        })
        gtagScriptExists = true
      }
      script.push({
        type: 'text/javascript',
        innerHTML: ` gtag('config', '${item}');`
      })
    })
  }

  if (storeInfo.id === 1) {
    script.push({
      type: 'text/javascript',
      src: 'https://www.dwin1.com/19038.js',
      defer: 'defer'
    })
  }
  if (storeInfo.enable_crisp_support) {
    const host = storeInfo.domain
    const crispWebsiteId = 'c17a453b-7e4b-4914-9cb6-747a900f787f'
    script.push({
      type: 'text/javascript',
      innerHTML: `
          window.$crisp=[];
          window.CRISP_WEBSITE_ID="${crispWebsiteId}";
          (function(){d=document;s=d.createElement("script");s.src="https://client.crisp.chat/l.js";s.async=1;d.getElementsByTagName("head")[0].appendChild(s);})();
          (function(){
            function setSegmentPushed(crispUserId, value) {
              const data = {
                value: value,
                timestamp: Date.now()
              };
              localStorage.setItem(getKey(crispUserId), JSON.stringify(data));
            }

            function getKey(crispUserId) {
              return 'crisp_segment_pushed_${host}_' + crispUserId;
            }

            function getSegmentPushed(crispUserId) {
              const key = getKey(crispUserId);
              try {
                const stored = localStorage.getItem(key);
                if (!stored) return false;

                const data = JSON.parse(stored);

                return data.value;
              } catch (e) {
                localStorage.removeItem(key);
                return false;
              }
            }

            function logToBackend(message) {
              try {
                fetch('https://${host}/api/public/init-segment-live-chat', {
                  method: 'POST',
                  body: JSON.stringify({
                    crisp_data: message,
                    host: '${host}'
                  }),
                  headers: {
                    'Content-Type': 'application/json'
                  }
                })
              }
              catch (e) {
                console.log('error:pushing segment to backend', e);
              }
            }

            $crisp.push(["on", "message:sent", function (message) {
              const crispUserId = message.user.user_id;
              const segmentPushed = getSegmentPushed(crispUserId);
              if (!segmentPushed) {
                window.$crisp.push(["set", "session:segments", [["live_chat"]]])
                setSegmentPushed(crispUserId, true);
                logToBackend(message);
              }
            }]);
            $crisp.push(['on','chat:closed',()=>{ document.querySelector('.crisp-client.opened').classList.remove('opened'); }]);
            window.onload=()=>{d=document;
            s=d.createElement('span');s.innerHTML="Live Chat";s.onclick=()=>{document.querySelector('.crisp-client').classList.add('opened');$crisp.push(['do', 'chat:open']);};
            e=d.createElement('div');e.setAttribute('class', "livechatbtn");e.appendChild(s);d.body.appendChild(e);
            }
          })();
        `
    })
  }
  return script
}
