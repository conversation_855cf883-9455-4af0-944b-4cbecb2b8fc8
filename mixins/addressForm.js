import { COUNTRY_STATE_LIST } from '~/helpers/variableConst'

export default {
  data () {
    return {
      countryStateList: COUNTRY_STATE_LIST,
    }
  },
  methods: {
    getLabel (inputType) {
      return this.currentCountryFormMap?.[inputType]?.label
    },
    isRequired (inputType, defaultValue = true) {
      return this.currentCountryFormMap?.[inputType]?.required ?? defaultValue
    },
    isHasField (inputType, defaultValue = true) {
      if (!this.currentCountryFormMap) {
        return defaultValue
      }

      return Boolean(this.currentCountryFormMap?.[inputType])
    },
    checkPhone (string, { nationalNumber, valid, number } = {}) {
      if (string) {
        this.isPhoneValid = valid
        if (nationalNumber) {
          this.phoneInput = nationalNumber
          this.userInfo.phone = number
        } else {
          this.userInfo.phone = ''
        }
      } else {
        this.userInfo.phone = ''
      }
    }
  },
  computed: {
    address1Label () {
      const DEFAULT = `${this.$t('Address')} ${(this.$t('House number and street name'))}`
      const label = this.getLabel('address_1')
      const suffix = this.isRequired('address_1') ? ' *' : ''
      const text = label ? this.$t(label) : DEFAULT
      return text + suffix
    },
    address2Label () {
      const DEFAULT = this.$t('Apt / Suite / Other')
      const label = this.getLabel('address_2')
      return label ? this.$t(label) : DEFAULT
    },
    phoneLabel () {
      const DEFAULT = this.$t('Phone number')
      const label = this.getLabel('phoneNumber')
      const suffix = this.isPhoneNumberRequired ? ' *' : ` (${this.$t('Optional')})`
      const text = label ? this.$t(label) : DEFAULT
      return text + suffix
    },
    cityLabel () {
      const DEFAULT = this.$t('City')
      const label = this.getLabel('city')
      const suffix = this.isRequired('city') ? ' *' : ''
      const text = label ? this.$t(label) : DEFAULT
      return text + suffix
    },
    stateLabel () {
      const DEFAULT = `${this.stateText}`
      const suffix = this.isRequired('state') && this.countryState && this.countryState.length ? ' *' : ''
      const label = this.getLabel('state')
      const text = label ? this.$t(label) : DEFAULT
      return text + suffix
    },
    postCodeLabel () {
      const DEFAULT = this.zipcodeText
      const label = this.getLabel('postCode')
      const suffix = this.isRequired('postCode') ? ' *' : ''
      const text = label ? this.$t(label) : DEFAULT
      return text + suffix
    },
    houseNumberLabel () {
      const DEFAULT = this.$t('House number')
      const label = this.getLabel('houseNumber')
      const suffix = this.isRequired('houseNumber') ? ' *' : ''
      const text = label ? this.$t(label) : DEFAULT
      return text + suffix
    },
    mailboxLabel () {
      const DEFAULT = this.$t('Mailbox number')
      const label = this.getLabel('mailboxNumber')
      return label ? this.$t(label) : DEFAULT
    },
    currentCountryFormMap () {
      const code = this.currentCountry.code === 'GB' ? 'UK' : this.currentCountry.code
      return this.countryFormMap[code] ?? null
    },
    countryFormMap () {
      // Todo remove default
      const output = this.$store.state.generalInfo?.checkout_form_config
      if (!output) {
        console.log('Using default country form map')
        return countryFormMapDefault
      }
      return output
    },
    currentCountryFormMapPosition () {
      return this.currentCountryFormMap?.position ?? ['address', 'city', 'state', 'postCode']
    },
    stateText () {
      if (['AU'].includes(this.currentCountry.code)) {
        return this.$t('State/Territory')
      } else if (['DE', 'IE', 'GB'].includes(this.currentCountry.code)) {
        return this.$t('County')
      } else if (['US'].includes(this.currentCountry.code)) {
        return this.$t('Select state')
      } else {
        return this.$t('State/Province/Region')
      }
    },
    zipcodeText () {
      if (['IE'].includes(this.currentCountry.code)) {
        return this.$t('Eircode')
      } else if (['AU', 'DE', 'GB'].includes(this.currentCountry.code)) {
        return this.$t('Postcode')
      } else if (['CA', 'FR', 'ES'].includes(this.currentCountry.code)) {
        return this.$t('Postal code')
      } else if (['IN'].includes(this.currentCountry.code)) {
        return this.$t('Pincode')
      } else {
        return this.$t('Zip code')
      }
    },
    countryArray () {
      return this.$store.state.generalInfo && this.$store.state.generalInfo.countries
    },
    currentCountry () {
      if (!this.order.country) {
        return ''
      }
      return this.countryArray.find(country => country.code === this.userInfo.country)
    },
    countryState () {
      return this.countryStateList[this.currentCountry.code]
    },
    isPhoneNumberRequired () {
      // ref: https://senprints.atlassian.net/browse/SDV-2177
      return this.currentCountry.code !== 'US'
    },
  }
}

// phone number fixed: position - last, width - 100%
// no width = 100%
const countryFormMapDefault = {
  AU: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street address, P.O. Box, Company name, c/o',
      required: true
    },
    address_2: {
      label: 'Apt, Suite, Unit, Building, Floor',
      required: true
    },
    postCode: {
      label: 'Postcode',
      required: true
    },
    city: {
      label: 'City/Suburb',
      required: true
    },
    state: {
      label: 'State/Territory',
      required: true
    },
    position: ['address', 'postCode', 'city', 'state']
  },
  IT: {
    fullName: {
      label: 'Name, surname, company name, c/o',
      required: true
    },
    phoneNumber: {
      label: null,
      required: true
    },
    address_1: {
      label: 'Street address',
      required: true
    },
    address_2: {
      label: 'Staircase, floor, interior, company',
      required: false
    },
    postCode: {
      label: 'Zip code',
      required: true,
      width: 30,
    },
    city: {
      label: null,
      required: true,
      width: 70
    },
    state: {
      label: 'Province',
      required: true
    },
    deliveryNote: {
      required: false
    },
    position: ['address', 'postCode', 'city', 'state']
  },
  UK: {
    fullName: {
      label: null,
      required: true
    },
    phoneNumber: {
      label: '10 or 9 digit phone number',
      required: true
    },
    address_1: {
      label: 'Address line 1 (or Company Name)',
      required: true
    },
    address_2: {
      required: false
    },
    postCode: {
      label: 'Postcode',
      required: true
    },
    city: {
      label: 'Town/City',
      required: true
    },
    state: {
      label: 'County',
      required: false
    },
    deliveryNote: {
      required: false
    },
    position: ['postCode', 'address', 'city', 'state']
  },
  DE: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street name and number, pickup location',
      required: true
    },
    address_2: {
      label: 'Company name, P.O Box, c/o, Pakadoo PAK-ID, etc.',
      required: true
    },
    postCode: {
      label: 'PLZ',
      required: true,
      width: 30,
    },
    city: {
      label: 'Town/City',
      required: true,
      width: 70
    },
    deliveryNote: {
      required: false
    },
    position: ['address', 'postCode', 'city']
  },
  BE: {
    fullName: {
      required: true
    },
    phoneNumber: {
      label: 'Enter mobile number for SMS notifications',
      required: true
    },
    address_1: {
      label: 'Street Avenue',
      required: true
    },
    address_2: {
      label: 'Apartment, suite, unit, building, floor, etc.',
      required: true
    },
    postCode: {
      label: 'Postcode (4 digits)',
      required: true
    },
    city: {
      required: true
    },
    houseNumber: {
      required: true,
      width: 50
    },
    mailboxNumber: {
      required: false,
      width: 50
    },
    deliveryNote: {
      required: false
    },
    position: ['address', 'houseNumber', 'mailboxNumber', 'postCode', 'city']
  },
  NL: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street address, P.O box, company name, c/o',
      required: true
    },
    address_2: {
      label: 'Apartment, suite, unit, building, floor, etc.',
      required: true
    },
    postCode: {
      label: 'Zip code',
      required: true
    },
    city: {
      required: true
    },
    state: {
      label: 'State/Province/Region',
      required: true
    },
    position: ['address', 'city', 'state', 'postCode']
  },
  ES: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street name and number',
      required: true
    },
    address_2: {
      label: 'Apartment, suite, unit, building, floor, etc.',
      required: true
    },
    postCode: {
      label: 'Postal code',
      required: true,
      width: 30
    },
    city: {
      required: true,
      width: 70
    },
    state: {
      label: 'Province',
      required: true
    },
    deliveryNote: {
      required: false
    },
    position: ['address', 'postCode', 'city', 'state']
  },
  FR: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street address',
      required: true
    },
    address_2: {
      label: 'Apt, suite, unit, company name',
      required: false
    },
    postCode: {
      label: 'Zip code',
      required: true,
      width: 30,
    },
    city: {
      required: true,
      width: 70
    },
    deliveryNote: {
      required: false
    },
    position: ['fullName', 'address', 'postCode', 'city']
  },
  AT: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street name and number / stairwell (optional) / door number (optional)',
      required: true
    },
    address_2: {
      required: false
    },
    postCode: {
      label: 'PLZ',
      required: true,
      width: 30
    },
    city: {
      label: 'Town/City',
      required: true,
      width: 70
    },
    position: ['address', 'postCode', 'city']
  },
  NZ: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street address (Ex: 18-22 Swanson St)',
      required: true
    },
    address_2: {
      label: 'Unit 904',
      required: true
    },
    postCode: {
      label: 'Postcode',
      required: true
    },
    city: {
      label: 'Town or City (Ex: Auckland)',
      required: true
    },
    state: {
      label: 'Suburb (Ex: Auckland CBD)',
      required: true
    },
    position: ['address', 'postCode', 'state', 'city']
  },
  CA: {
    fullName: {
      required: true
    },
    phoneNumber: {
      required: true
    },
    address_1: {
      label: 'Street address or P.O. Box',
      required: true
    },
    address_2: {
      label: 'Apt, suite, unit, building',
      required: true
    },
    postCode: {
      label: 'Postal code',
      required: true
    },
    city: {
      label: null,
      required: true
    },
    state: {
      label: 'Province/Territory',
      required: true
    },
    deliveryNote: {
      required: false
    },
    position: ['address', 'city', 'state', 'postCode']
  },
}
