export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['bestSellerProduct', 'featuredProduct', 'newArrivalsProduct', 'otherFeatured'],
  computed: {
    cartTotalQuantity,
    collectionBanners,
    isClient,
  },
  data () {
    return {
      banners: this.$store.state.storeInfo.banners,
      listCarousel: [{
        name: this.$store.state.storeInfo.feature_text || this.$t('Featured'),
        products: this.featuredProduct
      }, ...this.otherFeatured || [], {
        name: this.$t('New arrivals'),
        products: this.newArrivalsProduct
      }, {
        name: this.$t('Best sellers'),
        products: this.bestSellerProduct
      }]
    }
  }
}

function cartTotalQuantity () {
  return this.$store.getters['cart/getTotalQuantity']
}

function collectionBanners () {
  return this.$store.state.storeInfo.collection_banners
}

function isClient () {
  return process.browser
}
