export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['apiQuery', 'title', 'pageType', 'apiQueryFilter'],
  data () {
    return {
      listingData: this.$store.state.listing.campaignByUrl[this.apiQuery] || {},
      categories: this.$store.state.generalInfo.categories,
      generalTemplates: this.$store.state.generalInfo.templates,
      listingDataFilter: this.$store.state.listing.filterByUrl[this.apiQueryFilter] || {},
      templateFilterList: [],
      filterCategories: [],
      currentPage: 1,
      currentColor: '',
      currentCategory: '',
      currentCollection: '',
      minPrice: 0,
      maxPrice: 0,
      currentMinPrice: 0,
      currentMaxPrice: 0,
      currentSortType: 0,
      currentSearchKeyword: 0,
      isShowPagination: false,
      breadCrumbsList: [],
      currentTemplate: ''
    }
  },
  computed: {
    cartTotalQuantity
  },
  watch: {
    apiQuery: resetData
  },
  created: resetData,
  mounted
}

function breadCrumbsList () {
  if (this.pageType === 'category') {
    const category = this.$route.params.slug
    return [{
      text: this.title,
      to: this.localePath(`/category/${category}`)
    }]
  }
  return [{
    text: this.title
  }]
}

function currentSortType () {
  if (this.$route.query.sort) {
    return this.$route.query.sort
  } else if (this.pageType === 'collectionSlug') {
    return 'featured'
  } else if (this.pageType === 'search') {
    return 'relevant'
  } else {
    return 'popular'
  }
}

function cartTotalQuantity () {
  return this.$store.getters['cart/getTotalQuantity']
}

function templateFilterList () {
  const templateIdsFilterGroup = this.listingDataFilter?.template_id_group || []
  return this.generalTemplates.filter(t => templateIdsFilterGroup.includes(t.id))
}

function filterCategories () {
  const categoryGroupFilter = this.listingDataFilter?.category_id_group || []
  let firstArr
  if (this.pageType === 'category') {
    const category = this.$route.params.slug
    firstArr = getCategoryBySlug(this.categories, category)
    if (!firstArr) {
      firstArr = []
    }
  } else {
    firstArr = this.categories
  }

  if (firstArr.length > 0) {
    firstArr = firstArr.filter(item => categoryGroupFilter.includes(item.id))
  }

  const filterCategories = []
  firstArr.forEach((item) => {
    const clonedItem = Object.assign({}, item)
    filterCategories.push(clonedItem)
    clonedItem.child_menu = clonedItem.child_menu.filter(el => categoryGroupFilter.includes(el.id))
  })
  return filterCategories
}

function getCategoryBySlug (categories, slug) {
  const result = categories.find(item => item.slug === slug)
  if (result) {
    return result.child_menu
  } else {
    let filter = false
    categories.every((category) => {
      const check = getCategoryBySlug(category.child_menu, slug)
      if (check) { filter = check }
      return check
    })
    return filter
  }
}

function currentTemplate () {
  if (this.pageType === 'tag') {
    const keyWord = this.$route.params.slug.split('-')
    if (keyWord[1]) {
      const productTemPlate = this.generalTemplates.find((item) => {
        return flatWord(item.name).includes(flatWord(keyWord[1]))
      })
      if (productTemPlate) {
        return productTemPlate.id
      }
    }
  }
  return this.$route.query.product || ''
}

function flatWord (word = '') {
  // eslint-disable-next-line no-useless-escape
  return word.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~\W]/g, '').toLowerCase()
}

function resetData () {
  this.listingData = this.$store.state.listing.campaignByUrl[this.apiQuery] || {}
  this.listingDataFilter = this.$store.state.listing.filterByUrl[this.apiQueryFilter] || {}
  this.currentPage = this.$route.query.page || 1
  this.minPrice = this.listingDataFilter?.min_price ? (Math.ceil(this.listingDataFilter?.min_price / 5) - 1) * 5 : 0
  this.maxPrice = this.listingDataFilter?.max_price ? (Math.ceil(this.listingDataFilter?.max_price / 5) + 1) * 5 : 50
  this.currentColor = (this.$route.query.color && this.$route.query.color.replace(/-/g, ' ')) || ''
  this.currentCategory = this.$route.query.sub_cat || this.$route.query.category || ''
  this.currentCollection = this.pageType === 'collectionSlug' ? this.$route.params.slug : this.$route.query.collection || ''
  this.currentMinPrice = (this.$route.query.price && this.$route.query.price.split('-')[0]) || 0
  this.currentMaxPrice = (this.$route.query.price && this.$route.query.price.split('-')[1]) || this.maxPrice
  this.currentSortType = currentSortType.call(this)
  this.currentSearchKeyword = this.$route.query.s || ''
  this.isShowPagination = (this.listingData.total / this.listingData.perPage) > 1
  this.currentTemplate = currentTemplate.call(this)
  this.breadCrumbsList = breadCrumbsList.call(this)
  this.filterCategories = filterCategories.call(this)
  this.templateFilterList = templateFilterList.call(this)
  this.$store.dispatch('setSearchingState', false)
}

function mounted () {
  const listProduct = this.listingData && this.listingData.campaigns
  if (listProduct && listProduct.length) {
    this.$tracking.trackEvent({
      event: 'view_item_list',
      options: {
        items: listProduct.map((item) => {
          return {
            item_id: item.id || item.campaign_id,
            item_name: item.campaign_name,
            item_category: item.name,
            price: item.price
          }
        })
      }
    })
  }
}
