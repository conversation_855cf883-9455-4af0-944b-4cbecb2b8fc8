
export default {
  // eslint-disable-next-line vue/require-prop-types
  data () {
    return {
      faqCategory: [],
      usefulLinkList: [{
        title: 'Track Order',
        url: '/order/track'
      }, {
        title: 'Shipping Policy',
        url: '/page/shipping-policy'
      }, {
        title: 'Return/Refund Policy',
        url: '/page/return-policy'
      }, {
        title: 'Contact Support',
        url: '/page/contact-us'
      }]
    }
  },
  computed: {
    breadCrumbsList,
    currentLanguage
  },
  watch: {
    currentLanguage: getCategoryList
  },
  created: getCategoryList
}

function getCategoryList () {
  this.$store.dispatch('getFaqList').then((result) => {
    if (result && result.success) {
      this.faqCategory = result.data.faq_category.filter(item => item.language === this.currentLanguage.code)
      if (!this.faqCategory || !this.faqCategory.lenght) {
        this.faqCategory = result.data.faq_category.filter(item => item.language === 'us' || item.language === 'en')
      }
      this.faqCategory.forEach((category) => {
        category.faqList = result.data.faq.filter(item => item.category_id === category.id)
      })
    }
  })
}

function breadCrumbsList () {
  return [{
    text: 'Faq',
    to: this.localePath('/page/faq')
  }]
}

function currentLanguage () {
  return this.$i18n.locales.find(item => item.code === this.$i18n.locale)
}
