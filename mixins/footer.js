import LanguageSelect from '~/themes/default/components/common/LanguageSelect.vue'
import CurrencySelect from '~/themes/default/components/common/CurrencySelect.vue'

export default {
  props: {
    isHomePage: {
      type: Boolean,
      default: false
    },
    isBlockedStore: {
      type: Boolean,
      default: false
    }
  },
  components: {
    LanguageSelect,
    CurrencySelect
  },
  computed: {
    currenctContactPhoneData,
    currentCountryCode,
    currentLanguage,
    currentDomain,
    currentCurrency,
    isOrderPage,
    menu1 () {
      if (this.isBlockedStore) {
        return {
          title: this.menu1Raw.title,
          menuData: this.menu1Raw.menuData.filter(item => item.showBlockedStore)
        }
      }
      return this.menu1Raw
    },
    menu2 () {
      if (this.isBlockedStore) {
        return {
          title: this.menu2Raw.title,
          menuData: this.menu2Raw.menuData.filter(item => item.showBlockedStore)
        }
      }
      return this.menu2Raw
    }
  },
  data () {
    const storeInfoFooterMenu = this.$store.state.storeInfo?.footerMenu || []
    const footerMenu = {
      menu1Raw: {
        title: 'Info & support',
        menuData: [{
          name: 'About us',
          url: '/page/about'
        }, {
          name: 'Track order',
          url: '/order/track',
          showBlockedStore: true
        }, {
          name: 'FAQs',
          url: '/page/faq'
        }, {
          name: 'Contact support',
          url: '/page/contact-us',
          showBlockedStore: true
        }]
      },
      menu2Raw: {
        title: 'Policies',
        menuData: [{
          name: 'Return policy',
          url: '/page/return-policy'
        }, {
          name: 'Shipping policy',
          url: '/page/shipping-policy'
        }, {
          name: 'Terms & conditions',
          url: '/page/terms-of-service'
        }, {
          name: 'Privacy policy',
          url: '/page/privacy'
        }, {
          name: 'DMCA',
          url: '/page/dmca'
        }]
      },
    }
    const footerMenuUrls = (() => {
      // url: [menu, indexInMenuData]
      const res = {}

      Object.entries(footerMenu).forEach(([menuKey, obj]) => {
        obj.menuData.forEach((menuItem, index) => {
          res[menuItem.url] = [menuKey, index]
        })
      })

      return res
    })()

    for (const item of storeInfoFooterMenu) {
      if (!item.url) { continue }

      const indexedFooterItem = footerMenuUrls[item.url]
      if (indexedFooterItem) {
        footerMenu[indexedFooterItem[0]].menuData[indexedFooterItem[1]] = item
        continue
      }

      footerMenu.menu1Raw.menuData.push(item)
    }

    return {
      socialsLink: this.$store.state.storeInfo?.socialsLink || '',
      storeContact: this.$store.state.storeInfo?.storeContact || '',
      menus: storeInfoFooterMenu,
      storeInfo: this.$store.state.storeInfo,

      ...footerMenu,

      emailNewsletter: '',
      contactPhoneData: [{
        countryCode: ['UK'],
        phone: '+44 (20) 376-937-62',
        name: this.$t('United Kingdom'),
        flags: [`${this.$config.publicPath}/images/footer-flag/uk.svg`]
      }, {
        countryCode: ['BE', 'BG', 'CZ', 'DK', 'DE', 'EE', 'IE', 'EL', 'ES', 'FR', 'HR', 'IT', 'CY', 'LV', 'LT', 'LU', 'HU', 'MT', 'NL', 'AT', 'PL', 'PT', 'RO', 'SI', 'SK', 'FI', 'SE'],
        phone: '+33 (9) 730-368-78',
        name: this.$t('EU'),
        flags: [`${this.$config.publicPath}/images/footer-flag/eu.svg`]
      }, {
        countryCode: ['US', 'CA'],
        phone: '+****************',
        name: this.$t('American and Canada'),
        flags: [`${this.$config.publicPath}/images/footer-flag/us.svg`, `${this.$config.publicPath}/images/footer-flag/ca.svg`]
      }, {
        countryCode: ['AU', 'NZ'],
        phone: '+61 (2) 8317-3292',
        name: this.$t('Australia and New Zealand'),
        flags: [`${this.$config.publicPath}/images/footer-flag/au.svg`, `${this.$config.publicPath}/images/footer-flag/nz.svg`]
      }, {
        countryCode: [],
        phone: '+****************',
        name: this.$t('Other country'),
        flags: [`${this.$config.publicPath}/images/footer-flag/world.svg`]
      }],
      contactPhoneData2: [{
        countryCode: ['UK'],
        phone: '+44 (20) 8089-3193',
        name: this.$t('United Kingdom'),
        flags: [`${this.$config.publicPath}/images/footer-flag/uk.svg`]
      }, {
        countryCode: ['BE', 'BG', 'CZ', 'DK', 'DE', 'EE', 'IE', 'EL', 'ES', 'FR', 'HR', 'IT', 'CY', 'LV', 'LT', 'LU', 'HU', 'MT', 'NL', 'AT', 'PL', 'PT', 'RO', 'SI', 'SK', 'FI', 'SE'],
        phone: '+33 (9) 7303-8418',
        name: this.$t('EU'),
        flags: [`${this.$config.publicPath}/images/footer-flag/eu.svg`]
      }, {
        countryCode: ['US', 'CA'],
        phone: '+****************',
        name: this.$t('American and Canada'),
        flags: [`${this.$config.publicPath}/images/footer-flag/us.svg`, `${this.$config.publicPath}/images/footer-flag/ca.svg`]
      }, {
        countryCode: ['AU', 'NZ'],
        phone: '+61 (2) 8372-0168',
        name: this.$t('Australia and New Zealand'),
        flags: [`${this.$config.publicPath}/images/footer-flag/au.svg`, `${this.$config.publicPath}/images/footer-flag/nz.svg`]
      }, {
        countryCode: [],
        phone: '+****************',
        name: this.$t('Other country'),
        flags: [`${this.$config.publicPath}/images/footer-flag/world.svg`]
      }],
      currentContactPhone: 0
    }
  },
  methods: {
    getSocialLink,
    encode,
    decode,
    getCSRF,
    subscribeEmail
  },
  watch: {
    currentCountryCode: updateCurrentContactPhone
  },
  mounted: updateCurrentContactPhone
}

function currenctContactPhoneData () {
  if (this.storeInfo.status === 'verified') {
    const data = this.contactPhoneData[this.currentContactPhone]
    if (data) {
      return data
    } else {
      return this.contactPhoneData[this.contactPhoneData.length - 1]
    }
  } else {
    const data = this.contactPhoneData2[this.currentContactPhone]
    if (data) {
      return data
    } else {
      return this.contactPhoneData2[this.contactPhoneData2.length - 1]
    }
  }
}

function updateCurrentContactPhone () {
  const currentCountryCode = this.currentCountryCode
  if (this.storeInfo.status === 'verified') {
    const index = this.contactPhoneData.findIndex(element => element.countryCode.includes(currentCountryCode))
    if (index === -1) {
      this.currentContactPhone = this.contactPhoneData.length - 1
    } else {
      this.currentContactPhone = index
    }
  } else {
    const index = this.contactPhoneData2.findIndex(element => element.countryCode.includes(currentCountryCode))
    if (index === -1) {
      this.currentContactPhone = this.contactPhoneData2.length - 1
    } else {
      this.currentContactPhone = index
    }
  }
}

function currentCountryCode () {
  return this.$store.state.userCountry
}

function currentLanguage () {
  return this.$i18n.locales.find(item => item.code === this.$i18n.locale)
}

function currentCurrency () {
  return this.$store.state.currency
}

function isOrderPage () {
  return this.$route?.name?.startsWith('order')
}

function currentDomain () {
  return process.browser ? window.top.location.hostname : null
}

function getSocialLink (link) {
  if (link.toString().startsWith('http')) {
    return link
  } else {
    return `https://${link}`
  }
}

function encode (str) {
  if (str && process.browser) {
    return btoa(str)
  }
  return ''
}

function decode (str) {
  if (str && process.browser) {
    if (this.$isBot()) {
      return ''
    }
    return atob(str)
  }
  return ''
}

function getCSRF () {
  this.$store.dispatch('geCSRFToken')
}

function subscribeEmail () {
  this.$store.dispatch('geCSRFToken')
    .then(() => this.$store.dispatch('subscribeEmail', this.emailNewsletter))
    .then((result) => {
      if (result && result.success) {
        this.$toast.success(this.$t('Thank you for your support!'))
      } else {
        this.$toast.error(`Error: ${result.message}`)
      }
    }).catch((error) => {
      this.$toast.error(`Error: ${error.message}`)
    })
}
