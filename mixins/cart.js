import lscache from 'lscache'
import { BUNDLE_DISCOUNT_VIEW_PLACE } from '~/helpers/variableConst'

export default {
  data () {
    return {
      breadCrumbsList: [{
        text: this.$t('Shopping bag'),
        to: this.localePath('/cart')
      }],
      email: '',
      country: '',
      userInfo: {},
      promotionsList: [],
      relatedCart: [],
      bundleDiscount: {},
      bundleDiscountProduct: false,
      uploadFileDesignUrl: [],
      isLoadingAddToCart: false,
      currentCustomOptions: false,
      currentCommonOptions: false,
    }
  },
  computed: {
    products,
    totalQuantity,
    isLoadingUploadImage,
    isLoadingCheckout
  },
  mounted,
  methods: {
    addBundleProduct,
    updateBundleProduct,
    showConfirmDesign,
    getBundleDiscount,
    getMoreBundleProduct,
    refillBundleProducts,
    handleRemoveItem,
    getBundleIdsBeforeFetching
  },
  watch: {
    products () {
      this.refillBundleProducts()
    }
  },
}

// computed
function products () {
  return this.$store.state.cart.products
}

function totalQuantity () {
  return this.$store.getters['cart/getTotalQuantity']
}

function isLoadingUploadImage () {
  return this.$store.state.isLoadingUploadImage
}

function isLoadingCheckout () {
  return this.$store.state.order.isLoadingCheckout
}

// life-cycle
function mounted () {
  this.userInfo = lscache.get('userInfo') || {}
  const visitInfo = this.$getVisitInfo()
  this.currency = lscache.get('currency')
  this.email = this.userInfo?.email || ''
  this.country = this.userInfo?.country || visitInfo?.country || ''

  const cartData = lscache.get('cartData')

  if (cartData && cartData.products && cartData.products.length) {
    // correct old data
    cartData.products.map((item) => {
      if (item.custom_options && typeof item.custom_options === 'string') {
        item.custom_options = JSON.parse(item.custom_options)
      }
    })

    const listProductIdCart = {
      type: 'cart',
      filter: cartData.products.map((item) => {
        return {
          product_id: item.product_id,
          campaign_id: item.campaign_id,
          template_id: item.template_id
        }
      })
    }
    // related cart
    const intervalTracking = setInterval(() => {
      if (window.SP_VISIT_ACTIVITY >= 1) {
        this.$store.dispatch('cart/postRelatedCart', listProductIdCart).then((result) => {
          if (result.success) {
            this.relatedCart = result.data
          }
        })
        const campaignIds = cartData.products.map(item => item.campaign_id)
        this.$store.dispatch('campaign/getPromotion', { campaignIds }).then(({ success, data }) => {
          if (success) {
            this.promotionsList = data
          }
        })
        this.refillBundleProducts(campaignIds)
        if (intervalTracking) {
          clearInterval(intervalTracking)
        }
      }
    }, 500)

    setTimeout(() => {
      if (intervalTracking) {
        clearInterval(intervalTracking)
      }
    }, 15000)
  }

  setTimeout(() => {
    this.$store.commit('updateLoadingUploadImage', false)
  }, 3000)
}

async function addBundleProduct (check) {
  if (!check && this.bundleDiscountProduct && (this.bundleDiscountProduct.personalized || this.bundleDiscountProduct.full_printed === 5)) {
    this.$refs.modalBundleCustomText.isShowModalBundle = true
    return
  }
  this.isLoadingAddToCart = true
  this.$store.commit('updateLoadingUploadImage', true)
  if (check && this.bundleDiscountProduct && (this.bundleDiscountProduct.personalized === 1 || this.bundleDiscountProduct.personalized === 2)) {
    this.bundleDiscountProduct.designThumb = await getMockupUpload.call(this, this.bundleDiscountProduct.filterDesigns[0])
  }

  const campaignBundle = this.products.filter(product => !product?.promotion && !product?.campaignBundleId)?.at(-1)

  const result = await this.$store.dispatch('cart/addProductBundleDiscount', {
    bundleDiscount: this.bundleDiscount,
    campaignBundleId: campaignBundle?.campaign_id ?? null,
    productBundleId: campaignBundle?.product_id ?? null
  })
  this.$store.commit('updateLoadingUploadImage', false)
  this.isLoadingAddToCart = false
  this.refillBundleProducts()
  if (result.success) {
    this.bundleDiscountProduct = false
  } else {
    this.$toast.error(result.message)
  }
}

function updateBundleProduct (product, newData) {
  Object.assign(product, newData)
  this.$nextTick(() => {
    this.bundleForceUpdate++
  })
}

function getMockupUpload (design) {
  return new Promise((resolve, reject) => {
    const designData = design.designData
    const designID = `design_${designData.product_id}_${designData.print_space}.png`
    if (design.mockup) {
      const canvasDom = design.mockup.getElement()
      if (canvasDom) {
        this.$getImageFileFromDom(canvasDom).then((blob) => {
          const file = new File([blob], designID, { type: 'image/png' })
          return this.$preSignedUploader(file)
        }).then((fileUrl) => {
          resolve(fileUrl && fileUrl.Key)
        }).catch((error) => {
          reject(error)
        })
      }
    }
  })
}

function showConfirmDesign () {
  if (this.bundleDiscountProduct && (this.bundleDiscountProduct.personalized === 1 || this.bundleDiscountProduct.personalized === 2) && this.bundleDiscountProduct.filterDesigns && this.bundleDiscountProduct.filterDesigns.length) {
    this.uploadFileDesignUrl = [...this.bundleDiscountProduct.filterDesigns.map(design => design.mockup.toDataURL('png'))]
    this.$refs.modalConfirmDesign.isShowModalConfirmDesign = true
  }
}

function getBundleDiscount (campaignIds = [], productIds = []) {
  try {
    const cartData = lscache.get('cartData')
    if (campaignIds.length === 0) {
      cartData.products.map((item) => {
        if (item.custom_options && typeof item.custom_options === 'string') {
          item.custom_options = JSON.parse(item.custom_options)
        }
      })
      campaignIds = cartData.products.map(item => item.campaign_id)
    }

    if (productIds.length === 0) {
      productIds = cartData.products.map(item => item.product_id)
    }
    const viewPlace = BUNDLE_DISCOUNT_VIEW_PLACE.CART
    const bundleIds = this.getBundleIdsBeforeFetching()

    this.$store.dispatch('campaign/getBundleDiscount', { campaignIds, viewPlace, productIds, isRefetch: false, bundleIds }).then(({ success, data }) => {
      if (success) {
        this.bundleDiscount = this.$correctTestPriceCampaignAtBundleDiscount(data)
        this.bundleDiscountProduct = this.bundleDiscount.products[0]
        if (this.bundleDiscountProduct) {
          const product = this.bundleDiscountProduct
          let optionsListFull = product.options
          if (typeof optionsListFull === 'string') {
            optionsListFull = JSON.parse(optionsListFull)
          }
          const optionKeys = Object.keys(optionsListFull)
          const currentOptions = {}
          optionKeys.forEach((item, index) => {
            currentOptions[item] = optionsListFull[item][0]
          })
          product.isSelected = true
          product.variantsList = this.bundleDiscount.variants.filter(variant => variant.product_id === product.id)
          product.currentVariantKey = optionKeys.map(item => optionsListFull[item][0]).join(' / ')
          product.existVariantList = this.$getExistVariantList(optionsListFull)
          product.currentVariant = product.variantsList.find(item => item.variant_key.replace(/_/g, ' ').replaceAll('-', ' / ') === product.currentVariantKey)
          product.currentOptions = currentOptions
          product.options = { ...optionsListFull }

          if (this.bundleDiscountProduct.personalized === 3) {
            const findCustomOption = this.bundleDiscount.custom_personalized_options.find(item => item.id === this.bundleDiscountProduct.campaign_id)
            if (findCustomOption && typeof findCustomOption.options === 'string') {
              this.currentCustomOptions = JSON.parse(findCustomOption.options)
            } else {
              this.currentCustomOptions = findCustomOption.options
            }
            if (findCustomOption && typeof findCustomOption.common_options === 'string') {
              this.currentCommonOptions = JSON.parse(findCustomOption.common_options)
            } else {
              this.currentCommonOptions = findCustomOption.common_options
            }
          }
          if (this.bundleDiscountProduct.personalized === 0 && product.full_printed === 5) {
            const customOptions = product.template_custom_options
            const commonOptions = product.common_options ?? null
            if (typeof customOptions === 'string') {
              this.currentCustomOptions = JSON.parse(customOptions)
            }
            if (customOptions) {
              this.currentCustomOptions = customOptions
            }
            if (typeof commonOptions === 'string') {
              this.currentCommonOptions = JSON.parse(commonOptions)
            }
            if (commonOptions) {
              this.currentCommonOptions = commonOptions
            }
          }
        }
      }
    })
  } catch (e) {
    console.error('error in get bundle: ', e)
  }
}

function refillBundleProducts (campaignIds = [], productIds = []) {
  if (this.getMoreBundleProduct()) {
    this.getBundleDiscount(campaignIds, productIds)
  }
}

function getMoreBundleProduct () {
  if (!this.bundleDiscount?.promotion?.number_cart_bundle_product_limit) { return true }
  const productsWithBundle = this.products.filter((product) => {
    if (product?.promotion && product?.promotion?.id === this.bundleDiscount?.promotion?.id) {
      return true
    }
    return false
  })
  if (parseInt(this.bundleDiscount?.promotion?.number_cart_bundle_product_limit) > productsWithBundle.length) {
    return true
  }
  return false
}

function handleRemoveItem (cartItem) {
  const campaignId = cartItem.campaign_id
  if (cartItem?.promotion) {
    return
  }

  const followProduct = this.products.find((item) => {
    if (item?.promotion && item?.campaignBundleId === campaignId) {
      return true
    }
  })

  const promotion = followProduct?.promotion

  let allowRemovePromotionInFollowProduct = false
  if (promotion?.is_same_campaign) {
    if (!cartItem?.campaignBundleId || !cartItem?.promotion) {
      allowRemovePromotionInFollowProduct = true
    }
  } else {
    allowRemovePromotionInFollowProduct = true
  }

  if (allowRemovePromotionInFollowProduct) {
    this.products.forEach((product, index) => {
      if (product.campaignBundleId && product.campaignBundleId === campaignId) {
        this.$store.commit('cart/UPDATE_CART_ITEM', { index, data: { promotion: null, campaignBundleId: null } })
      }
    })
  }
}

function getBundleIdsBeforeFetching () {
  const bundleIds = this.products.map((product) => {
    if (product?.promotion?.id) {
      return product.promotion.id
    }
  })

  return bundleIds.filter(id => id)
}
