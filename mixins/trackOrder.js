import lscache from 'lscache'

export default {
  name: 'TrackOrder',
  data () {
    return {
      form: {
        email: '',
        order_number: ''
      },
      error: false
    }
  },
  computed: {
    lastOrder () {
      if (!process.browser) {
        return null
      }

      return lscache.get('lastOrder')
    },
    trackLastOrderUrl () {
      if (this.lastOrder) {
        return `/order/status/${this.lastOrder.order_token}?utm_source=page&utm_medium=track-order`
      }
    }
  },
  mounted () {
    const userInfo = lscache.get('userInfo')

    if (userInfo) {
      this.form.email = userInfo.email

      // focus on next input
      this.$refs.orderNumberInput.focus()
    }
  },
  methods: {
    onSubmit () {
      // hide previous error message
      this.error = false

      if (!this.form.email || !this.form.order_number) {
        return
      }

      this.$axios.$post('/public/order/track', this.form).then((json) => {
        if (json.success) {
          this.$router.push(this.localePath(`/order/status/${json.data}`))
        } else {
          this.error = true
        }
      })
    },
    resendConfirmationEmail () {
      if (!this.form.email) {
        return
      }

      this.$axios.$post('/public/order/resend-confirm-email', {
        email: this.form.email
      }).finally(() => {
        this.$toast.success(this.$t('Please check your inbox'))
      })
    }
  }
}
