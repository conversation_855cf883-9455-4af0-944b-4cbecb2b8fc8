
import lscache from 'lscache'
import { v4 as uuidv4 } from 'uuid'

export default {
  asyncData,
  head,
  computed: {
    asyncComponent,
    title
  },
  watchQuery: ['page', 'category', 'sub_cat', 's', 'color', 'price', 'sort', 'collection', 'product'],
  mounted,
  methods: {
    buildKlaviyoData
  }
}

async function asyncData ({ params, query, store, error, route }) {
  const pageType = getPageType(route.name)
  if (!query.sort && process.browser) {
    const lastSortType = lscache.get('lastSortType')
    if (['popular', 'newest', 'highest_price', 'lowest_price'].includes(lastSortType)) {
      query.sort = lastSortType
      const param = new URLSearchParams()
      Object.keys(query).forEach((item) => {
        param.set(item, query[item])
      })
      return window.$nuxt.$router.push(`${route.path}?${param}`)
    }
  }

  const {
    apiQuery,
    apiQueryFilter,
    success,
    statusCode,
    message
  } = await store.dispatch('listing/getCampaignByUrl', {
    params,
    query,
    pageType
  }).catch((e) => {
    return error({
      statusCode: 404,
      error: e
    })
  })

  if (!success) {
    return error({
      statusCode,
      message
    })
  }

  return {
    apiQueryFilter,
    apiQuery,
    pageType,
    categorySlug: params.slug
  }
}

function head () {
  return {
    title: `${this.title} | ${this.$store.state.storeInfo.name || ''}`,
    meta: this.$createSEOMeta({ title: this.title, description: this.title })
  }
}

function asyncComponent () {
  try {
    require(`~/themes/${this.$store.state.storeInfo.theme}/pages/listing`)
    return () =>
      import(
        '~/themes/' + this.$store.state.storeInfo.theme + '/pages/listing'
      )
  } catch (e) {
    return () =>
      import(
        '~/themes/default/pages/listing'
      )
  }
}

function title () {
  if (this.pageType === 'category') {
    return getTitle(this.$store.state.generalInfo.categories, this.categorySlug)
  } else if (this.pageType === 'collection') {
    return this.$t('All products')
  } else if (this.pageType === 'collectionSlug') {
    const collectionName = this.$route.params.slug.replace(/-/g, ' ')
    return collectionName.charAt(0).toUpperCase() + collectionName.slice(1)
  } else if (this.pageType === 'search') {
    return `Search result for: ${this.$route.query.s}`
  } else if (this.pageType === 'tag') {
    return `Tag: ${this.$route.params.slug.replace(/[_-]/g, ' ')}`
  } else if (this.pageType === 'artist') {
    const artistName = this.$route.params.slug
    const parts = artistName.split('-')
    parts.pop()
    const capitalizedParts = parts.map(part =>
      part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
    )
    return this.$t('Designed by') + ' ' + capitalizedParts.join(' ')
  }
}

function getTitle (categories, categorySlug) {
  let categoryName = ''
  categories.some((category) => {
    if (category.slug === categorySlug) {
      categoryName = category.name
      return true
    } else if (category.child_menu && category.child_menu.length) {
      categoryName = getTitle(category.child_menu, categorySlug)
      if (categoryName) {
        return true
      }
    }
    return false
  })
  return categoryName
}

function getPageType (routeName = '') {
  if (routeName.startsWith('category-slug___')) {
    return 'category'
  } else if (routeName.startsWith('collection___')) {
    return 'collection'
  } else if (routeName.startsWith('collection-slug___')) {
    return 'collectionSlug'
  } else if (routeName.startsWith('search___')) {
    return 'search'
  } else if (routeName.startsWith('tag-slug___')) {
    return 'tag'
  } else if (routeName.startsWith('artist-slug___')) {
    return 'artist'
  }
}

function mounted () {
  if (this.pageType === 'collectionSlug' || this.pageType === 'collection' || this.pageType === 'category') {
    this.$tracking.trackEvent({
      event: 'view_item_list',
      options: {
        klaviyoData: this.buildKlaviyoData('view_item_list')
      }
    })
  }
  if (this.pageType === 'search') {
    this.$tracking.trackEvent({
      event: 'search',
      options: {
        klaviyoData: this.buildKlaviyoData('search')
      }
    })
  }
}

function buildKlaviyoData (event = 'view_item_list') {
  const url = window.location.href
  if (event === 'view_item_list') {
    const collectionName = this.title ?? ''
    const collectionID = this.$route.params.slug ?? ''
    return {
      URL: url,
      'Activity ID': uuidv4(),
      CollectionName: collectionName,
      CollectionID: collectionID
    }
  }
  if (event === 'search') {
    const keyword = this.$route.query.s ?? ''
    return {
      URL: url,
      'Activity ID': uuidv4(),
      SearchQuery: keyword,
      SearchQueryParts: keyword.split(' ')
    }
  }
}
