export default {
  data () {
    return {
      token: this.$route.params.slug,
      email: '',
      isLoadingSubmit: true,
      isSubmitSuccess: false
    }
  },
  methods: {
    handleSubmit
  },

  created
}

function created () {
  return this.$store.dispatch('unsubscribe/getEmailByToken', this.token).then((result) => {
    if (result && result.success) {
      this.email = result.data.email
      this.isLoadingSubmit = false
    } else {
      this.$nuxt.error({
        statusCode: 404,
        error: '404 Page Not Found'
      })
    }
  }).catch(() => {
    this.$nuxt.error({
      statusCode: 404,
      error: this.$t('404 Page Not Found')
    })
  })
}

function handleSubmit () {
  this.isLoadingSubmit = true
  this.$store.dispatch('unsubscribe/unsubcribeEmail', this.token).then((result) => {
    if (result && result.success) {
      this.isSubmitSuccess = true
    }
    this.isLoadingSubmit = false
  }).catch(() => {
    this.isLoadingSubmit = false
  })
}
