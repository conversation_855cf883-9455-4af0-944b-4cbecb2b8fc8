import lscache from 'lscache'

export default {
  data () {
    return {
      shippingCost: null,
      insuranceFee: null
    }
  },
  computed: {
    userCountry,
    products
  },
  mounted () {
    this.calculateShippingCost()
  },
  methods: {
    calculateShippingCost,
  },
  watch: {
    products: {
      handler () {
        this.calculateShippingCost()
      },
      deep: true
    }
  },
}
function products () {
  return this.$store.state.cart.products
}
function userCountry () {
  const { country } = lscache.get('visitInfo')
  return country
}
async function calculateShippingCost () {
  const productInfo = this.products.reduce((acc, item) => {
    const existingProduct = acc.find(product => product.productId === item.product_id)

    if (existingProduct) {
      existingProduct.quantity += item.quantity
    } else {
      acc.push({
        productId: item.product_id,
        quantity: item.quantity
      })
    }

    return acc
  }, [])
  const { data, success } = await this.$axios.$post('/public/order/pre-calculate-shipping', {
    productInfo,
    location: { country: this.userCountry, address: '' }
  })
  if (success) {
    this.shippingCost = data.shipping_amount
    this.insuranceFee = data.insurance_fee
  }
}
