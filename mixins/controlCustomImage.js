export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['customImage'],
  data () {
    return {
      isImageLowQuanlity: false,
      imageControlList: [{
        key: 'zoomOut',
        icon: 'icon-sen-magnify-minus-outline'
      }, {
        key: 'zoomIn',
        icon: 'icon-sen-magnify-plus-outline'
      }, {
        key: 'rollLeft',
        icon: 'icon-sen-refresh'
      }, {
        key: 'moveRight',
        icon: 'icon-sen-arrow-right-thin'
      }, {
        key: 'moveLeft',
        icon: 'icon-sen-arrow-left-thin'
      }, {
        key: 'moveDown',
        icon: 'icon-sen-arrow-down-thin'
      }, {
        key: 'moveUp',
        icon: 'icon-sen-arrow-up-thin'
      }],
      timeoutControl: null,
      intervalControll: null
    }
  },
  watch: {
    'customImage.scaleX': checkScale,
    'customImage.scaleY': checkScale
  },
  methods: {
    controlCustomImage,
    startIntevalControl,
    removeIntervalControl,
    checkScale,
    reRender
  }
}

function controlCustomImage (key) {
  switch (key) {
    case 'zoomOut':
      this.customImage.left = this.customImage.left + (this.customImage.width * this.customImage.scaleX * 0.02 * (Math.cos(this.customImage.angle * Math.PI / 180) || 1))
      this.customImage.top = this.customImage.top + (this.customImage.height * this.customImage.scaleY * 0.02 * (Math.cos(this.customImage.angle * Math.PI / 180) || 1))
      this.customImage.scale(this.customImage.scaleY / 1.04)
      break
    case 'zoomIn':
      this.customImage.scale(this.customImage.scaleY * 1.04)
      this.customImage.left = this.customImage.left - (this.customImage.width * this.customImage.scaleX * 0.02 * (Math.cos(this.customImage.angle * Math.PI / 180) || 1))
      this.customImage.top = this.customImage.top - (this.customImage.height * this.customImage.scaleY * 0.02 * (Math.cos(this.customImage.angle * Math.PI / 180) || 1))
      break
    case 'rollLeft':
      this.customImage.rotate(this.customImage.angle + 1)
      break
    case 'moveRight':
      this.customImage.left += 10
      break
    case 'moveLeft':
      this.customImage.left -= 10
      break
    case 'moveDown':
      this.customImage.top += 10
      break
    case 'moveUp':
      this.customImage.top -= 10
  }

  this.reRender()
}

function reRender () {

}

function startIntevalControl (key) {
  this.removeIntervalControl()
  this.timeoutControl = setTimeout(() => {
    this.intervalControll = setInterval(() => {
      this.controlCustomImage(key)
    }, 50)
  }, 500)
}

function removeIntervalControl () {
  if (this.timeoutControl) {
    clearTimeout(this.timeoutControl)
  }
  if (this.intervalControll) {
    clearInterval(this.intervalControll)
  }
}

function checkScale () {
  if (this.customImage && (this.customImage.scaleX > 2 || this.customImage.scaleY > 2)) {
    this.isImageLowQuanlity = true
  } else {
    this.isImageLowQuanlity = false
  }
}
