import { USD_CODE, EUR_CODE } from '~/helpers/variableConst'

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['tracking', 'order', 'timeframe', 'statement_descriptor'],
  data () {
    return {
      listKeys: Object.keys(this.tracking),
      filterProductIds: {},
      currentTime: new Date().toISOString(),
      USD_CODE,
      EUR_CODE
    }
  },
  computed: {
    storeDetail,
    relatedCart,
    getOrderStatus,
    currentCountry,
    getTimeTracking,
    canEditAddress () {
      const DAY = 86400000
      const order = this.order
      const numberDay = order.type === 'regular' && order.address_verified === 'invalid' ? 2 : 1
      return (new Date(order.paid_at).getTime() + numberDay * DAY) > (new Date().getTime())
    }
  },
  mounted
}

function mounted () {
  const intervalTracking = setInterval(() => {
    if (window.SP_VISIT_ACTIVITY >= 1) {
      this.filterProductIds.filter = []
      this.listKeys.map((key) => {
        const items = this.tracking[key]
        items.map((item) => {
          if (!item.product_id) {
            return
          }
          const itemProduct = {
            product_id: item.product_id,
            campaign_id: item.campaign_id,
            template_id: item.template_id
          }
          this.filterProductIds.filter.push(itemProduct)
        })
      })
      if (this.filterProductIds.filter.length > 0) {
        this.filterProductIds.type = 'post_sale'
        if (this.storeDetail.smart_remarketing && this.order.id) {
          this.filterProductIds.order_id = parseInt(this.order.id)
          this.filterProductIds.source = 'order_status'
        }
        this.$store.dispatch('cart/postRelatedCart', this.filterProductIds)
      }

      if (intervalTracking) {
        clearInterval(intervalTracking)
      }
    }
  }, 500)

  setTimeout(() => {
    if (intervalTracking) {
      clearInterval(intervalTracking)
    }
  }, 15000)

  const { open_edit: openEdit } = this.$route.query
  if (openEdit === 'true' && this.canEditAddress) {
    this.$refs.modalEditInfo.showModalEditAddress = true
  }
}

function relatedCart () {
  return this.$store.state.cart.relatedCart
}

function storeDetail () {
  return this.$store.state.storeInfo
}

function getOrderStatus () {
  return function (order) {
    switch (order) {
      case 'cancelled':
        return {
          className: 'danger',
          text: this.$i18n.t('Cancelled')
        }
      case 'processing':
        return {
          className: 'info',
          text: order.fulfill_status === 'unfulfilled' ? this.$i18n.t('Processing') : this.$i18n.t('In production')
        }
      case 'unfulfilled':
        return {
          className: 'secondary',
          text: this.$i18n.t('Unfulfilled')
        }
      case 'on_delivery':
        return {
          className: 'success',
          text: this.$i18n.t('On Delivery')
        }
      default: {
        if (order.includes('on_delivery')) {
          return {
            className: 'success',
            text: this.$i18n.t('On Delivery')
          }
        }

        if (order.includes('fulfilled')) {
          return {
            className: 'success',
            text: this.$i18n.t('Delivered')
          }
        }

        return {
          className: 'primary',
          text: this.$i18n.t('Processing')
        }
      }
    }
  }
}

function currentCountry () {
  if (!this.order.country) {
    return ''
  }
  return this.$store.state.generalInfo?.countries.find(country => country.code === this.order.country)
}

function getTimeTracking () {
  return (timeString) => {
    const time = new Date(timeString)
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec']
    const value = `${time.getDate()} ${this.$t(monthNames[time.getMonth()])}`
    return value
  }
}
