import cloneDeep from 'lodash/cloneDeep'
import { BUNDLE_DISCOUNT_VIEW_PLACE } from '~/helpers/variableConst'

export default {
  data () {
    return {
      bundleDiscount: {
        custom_personalized_options: [],
        products: [],
        variants: [],
        promotion: {}
      },
      currentCustomOptions: false,
      currentCommonOptions: false,
      bundleProductVariant: false,
      bundleForceUpdate: 1,
      currentBundleDiscount: false,
    }
  },
  computed: {
    totalBundleDiscount,
    saveBundleDiscount
  },
  methods: {
    getBundleDiscount,
    updateBundleProduct,
    openBundleProduct,
    checkProductUnfinished,
    markAsChecked,
  }
}

function totalBundleDiscount () {
  if (this.bundleForceUpdate) {
    return this.bundleDiscount.products.filter((item, index) => index < 3 && item.isSelected)
      .reduce((total, product) => {
        if ((product.personalized === 3 || product.full_printed === 5) && product.customFeePrice && product.customOptionGroupNumbers) {
          product.extraCustomFee = product.customFeePrice
          total += product.customFeePrice
        }
        return total + this.$convertPrice(((product.currentVariant && product.currentVariant.price) || product.price || 0), product.currency_code)
      }, 0)
  }
}

function saveBundleDiscount () {
  return this.totalBundleDiscount * this.bundleDiscount.promotion.discount_percentage / 100
}

// methods
async function getBundleDiscount (productIds = [], isRefetch = false) {
  this.bundleProductVariant = this.variantsList[0]
  const { success, data } = await this.$store.dispatch('campaign/getBundleDiscount', { campaignSlug: this.campaignSlug, campaignIds: [this.campaignData?.id], viewPlace: BUNDLE_DISCOUNT_VIEW_PLACE.CAMPAIGN_DETAIL, productIds, isRefetch })
  if (success) {
    const _data = cloneDeep(data)
    _data.products.forEach((product) => {
      let optionsListFull = product.options
      if (typeof optionsListFull === 'string') {
        optionsListFull = JSON.parse(optionsListFull)
      }
      const optionKeys = Object.keys(optionsListFull)
      const currentOptions = {}
      optionKeys.forEach((item, index) => {
        if (index === 0 && product?.default_option) {
          currentOptions[item] = product.default_option
        } else {
          currentOptions[item] = optionsListFull[item][0]
        }
      })
      product.isSelected = true
      product.variantsList = this.bundleDiscount.variants.filter(variant => variant.product_id === product.id)
      product.currentVariantKey = optionKeys.map(item => currentOptions[item]).join(' / ')
      product.existVariantList = this.$getExistVariantList(optionsListFull)
      product.currentVariant = product.variantsList.find(item => item.variant_key.replace(/_/g, ' ').replaceAll('-', ' / ') === product.currentVariantKey)
      product.currentOptions = currentOptions
      product.options = { ...optionsListFull }
    })
    Object.assign(this.bundleDiscount, _data)
  }
}

function updateBundleProduct (product, newData) {
  Object.assign(product, newData)
  this.$nextTick(() => {
    this.bundleForceUpdate++
  })
}

function openBundleProduct (product) {
  if (product) {
    this.currentBundleDiscount = product
    if (this.currentBundleDiscount.personalized === 3) {
      const findCustomOption = this.bundleDiscount.custom_personalized_options.find(item => item.id === this.currentBundleDiscount.campaign_id)
      if (findCustomOption && typeof findCustomOption.options === 'string') {
        this.currentCustomOptions = JSON.parse(findCustomOption.options)
      } else {
        this.currentCustomOptions = findCustomOption.options
      }
      if (findCustomOption && typeof findCustomOption.common_options === 'string') {
        this.currentCommonOptions = JSON.parse(findCustomOption.common_options)
      } else {
        this.currentCommonOptions = findCustomOption.common_options
      }
    }
    if (product.personalized === 0 && product.full_printed === 5) {
      const customOptions = product.template_custom_options
      const commonOptions = product.common_options ?? null
      if (typeof customOptions === 'string') {
        this.currentCustomOptions = JSON.parse(customOptions)
      }
      if (customOptions) {
        this.currentCustomOptions = customOptions
      }
      if (typeof commonOptions === 'string') {
        this.currentCommonOptions = JSON.parse(commonOptions)
      }
      if (commonOptions) {
        this.currentCommonOptions = commonOptions
      }
    }
    if (product.personalized || product.full_printed === 5) {
      this.$refs.modalBundleCustomText.isShowModalBundle = true
    } else {
      this.$refs.modalBundleProduct.isShowModalBundle = true
    }
  }
}

async function checkProductUnfinished (productList) {
  for (let i = 0; i < productList.length; i++) {
    const product = productList[i]
    if (product.personalized === 1) {
      if (!product.filterDesigns) {
        return product
      }
      const checkCustomText = product.customTextList && product.customTextList.length && product.customTextList.find(item => !item.data.text)
      if (checkCustomText) {
        return product
      } else if (product.customImageList && product.customImageList.length && !product.customImageList[0].design.currentFileUploadUrl) {
        return product
      } else {
        return false
      }
    } else if (product.personalized === 2) {
      if (!product.filterDesigns) {
        return product
      }
      const designData = product.filterDesigns && product.filterDesigns.length && product.filterDesigns[0].designData
      const validated = await window.pbsdk.validate(`design_${designData && designData.campaign_id}_${designData && designData.print_space}`)
      if (!validated) {
        return product
      }
    } else if (product.personalized === 3 || product.full_printed === 5) {
      const customOptions = product?.customerCustomOptions?.flat() ?? []
      const hasCustomerCustomOptions = customOptions && customOptions.length > 0 // true
      const requiredOptions = customOptions.filter(item => !item.unrequired)
      const allFilled = requiredOptions.every(item => item.value)
      const isChecked = product.is_checked
      const isCurrentProduct = this.currentBundleDiscount && this.currentBundleDiscount.campaign_id === product.campaign_id && this.currentBundleDiscount.name === product.name
      if ((!hasCustomerCustomOptions && !isChecked && !isCurrentProduct) || !allFilled) {
        return product
      }
    }
    this.markAsChecked(product)
  }
  return false
}

function markAsChecked (product) {
  try {
    const index = this.bundleDiscount.products.findIndex(item => item.campaign_id === product.campaign_id)
    this.bundleDiscount.products[index].is_checked = true
  } catch (e) {
    console.error('Error markAsChecked', e)
  }
}
