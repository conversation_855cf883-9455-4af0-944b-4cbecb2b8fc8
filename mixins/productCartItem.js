import lscache from 'lscache'
import { getLastUserBehavior, setUserBehavior } from '~/helpers/storage'

export default {
  // eslint-disable-next-line vue/require-prop-types
  props: ['index', 'cartItem'],
  data () {
    return {
      isEdit: false,
      isShowConfirmDeleteCartItem: false,
      images: [],
      campaign: {},
      product: {},
      optionsList: {},
      variantList: [],
      cheatVariable: 0,
      dynamicBaseCostIndex: 0,
      variantsProduct: []
    }
  },
  computed: {
    variant,
    isBundleProduct () {
      return !!this.$store.state.cart.products?.find(item => item.campaign_id === this.cartItem.campaignBundleId)
    },
    price () {
      return (this.cartItem.variantPrice || this.product?.price) + this.dynamicBaseCostIndex + this.extraCustomFee + this.cheatVariable - this.cheatVariable
    },
    totalPrice,
    totalPriceBundle,
    extraCustomFee,
    productSlug,
    priceBundle
  },
  watch: {
    cartItem: resetData,
    '$store.state.userCountry': {
      handler () {
        this.setDynamicBaseCost()
      },
      immediate: true,
    },
  },
  mounted () {
    this.resetData()
    this.setDynamicBaseCost()
  },
  methods: {
    selectProduct,
    updateOption,
    updateQuantity,
    duplicateCartItem,
    removeCartItem,
    correctVariantPrice (product) {
      const variantKey = Object.values(product.options).join('-').replaceAll('_', '').replaceAll(' ', '_')
      const productId = product.product_id ?? this.cartItem.product_id
      let variant = this.variantList.filter(variant => variant.variant_key === variantKey && variant.product_id === productId)
      const userCountry = this.$userCountryForPricing()
      variant = this.$getBaseCostOfTemplateDependOnUserCountry(userCountry, this.$store.state.generalInfo.countries, variant)
      this.cheatVariable++
      product.variantPrice = variant ? variant.price : 0
    },
    resetData,
    setDynamicBaseCost

  }
}

function productSlug () {
  return this.cartItem ? this.$toSlug(this.cartItem.product_name) : ''
}

function resetData () {
  const productId = this.cartItem.product_id
  const sellerId = this.cartItem.seller_id
  this.$store.dispatch('campaign/getCampaign', { campaignSlug: this.cartItem.campaign_slug, productSlug }).then(async ({ success, campaign, images, variants }) => {
    if (success) {
      campaign = this.$correctTestPriceCampaignAtCampaignDetail(JSON.parse(JSON.stringify(campaign)))
      // this.variantsProduct = this.$store.state.campaign.productVariants[productId]
      await this.$store.dispatch('campaign/getVariantsProduct', {
        productId,
        sellerId
      })
      this.variantsProduct = lscache.get('product-variants') ? lscache.get('product-variants')[productId] ?? [] : []

      if (!this.variantsProduct) {
        this.variantsProduct = await this.$store.dispatch('campaign/getVariantsProduct', {
          productId,
          sellerId
        })
      }
      this.images = images
      this.campaign = campaign
      this.variantList = productId !== undefined ? this.variantsProduct : []
      const varName = Object.keys(this.cartItem.options).map(option => this.cartItem.options[option] && this.cartItem.options[option].replaceAll(' ', '-')).toString().replaceAll('-', '_').replaceAll(',', '-')
      const variantsWithKey = this.variantsProduct.filter(item => item.variant_key === varName)
      const userCountry = this.$userCountryForPricing()
      const variant = this.$getBaseCostOfTemplateDependOnUserCountry(userCountry, this.$store.state.generalInfo.countries, variantsWithKey)
      this.product = campaign.products.find(item => item.id === this.cartItem.product_id)
      if (this.cartItem.full_printed === 5 || this.cartItem.personalized === 3) {
        campaign.products = campaign.products.filter(item => item.id === this.cartItem.product_id)
      } else {
        campaign.products = campaign.products.filter((item) => {
          return item.full_printed !== 5 || this.cartItem.personalized === 3
        })
      }
      this.campaign = campaign
      this.$store.commit('cart/UPDATE_CART_ITEM_BY_ID', {
        cartItemId: this.cartItem.cartItemId,
        data: {
          currency_code: this.product?.currency_code,
          price: this.product.price ?? this.price,
          variantPrice: variant?.price ?? this.cartItem.variantPrice,
        }
      })

      if (this.product && this.product.options) {
        this.optionsList = JSON.parse(this.product.options)
      }
    }
    this.setDynamicBaseCost()
  })
}

function variant () {
  if (this.variantList && this.optionsList) {
    const varName = Object.keys(this.optionsList).map(option => this.cartItem.options[option] && this.cartItem.options[option].replaceAll(' ', '-')).toString().replaceAll('-', '_').replaceAll(',', '-')
    const variantList = this.variantList.filter(variant => variant.variant_key === varName && variant.product_id === this.cartItem.product_id)
    const userCountry = this.$userCountryForPricing()
    return this.$getBaseCostOfTemplateDependOnUserCountry(userCountry, this.$store.state.generalInfo.countries, variantList)
  }
}

function totalPrice () {
  return this.price * this.cartItem.quantity
}

function totalPriceBundle () {
  return this.isBundleProduct ? this.price * this.cartItem.quantity * (100 - this.cartItem.promotion?.discount_percentage) / 100 : 0
}

function priceBundle () {
  return this.price * (100 - this.cartItem.promotion?.discount_percentage) / 100
}

function selectProduct (product) {
  const data = {}
  data.product_name = product.name
  data.product_id = product.id
  data.price = product.price + this.extraCustomFee
  data.thumb_url = product.thumb_url
  data.full_printed = product.full_printed
  data.total_price = this.cartItem.quantity * (product.price + this.extraCustomFee)
  const params = new URLSearchParams()
  params.set('product', this.$toSlug(product.name))

  const optionList = JSON.parse(product.options)
  this.optionsList = optionList
  data.options = {}

  if (optionList) {
    Object.keys(optionList).forEach((el, index) => {
      let lastOption = null
      if (el === 'size' || el === 'color') {
        lastOption = getLastUserBehavior(el, product.name)?.replace(/-/g, ' ')
        if (lastOption && !optionList[el].includes(lastOption)) {
          lastOption = null
        }
      }
      if (optionList[el].length === 1) {
        data.options[el] = `__${optionList[el][0]}`
      } else if (product.default_option && index === 0) {
        data.options[el] = lastOption || product.default_option
      } else {
        data.options[el] = lastOption || optionList[el][0]
      }
      params.set(el, data.options[el].replace(/ /g, '-'))
    })
    if (data.options.color && this.$colorVal(data.options.color.replace(/-/g, ' '))) {
      data.thumb_url = data.thumb_url.replace(/co_rgb:.{6}/, `co_rgb:${this.$colorVal(data.options.color.replace(/-/g, ' ')).replace('#', '')}`)
    }
  }

  data.product_url = `/${this.cartItem.campaign_slug}/?${params}`
  this.correctVariantPrice(data)
  this.$store.commit('cart/UPDATE_CART_ITEM', { index: this.index, data })
  this.resetData()
}

function updateOption (key, value) {
  const options = { ...this.cartItem.options }
  const data = {
    options
  }

  if (data.options) {
    data.options[key] = value
  }

  setUserBehavior(key, value.replace(/ /g, '-'), this.cartItem.product_name)

  if (key === 'color') {
    data.thumb_url = this.product.thumb_url.replace(/co_rgb:.{6}/, `co_rgb:${this.$colorVal(data.options.color.replace(/-/g, ' ')).replace('#', '')}`)
  }

  const params = new URLSearchParams()
  params.set('product', this.$toSlug(this.cartItem.product_name))
  Object.keys(data.options).forEach((el) => {
    params.set(el, data.options[el].replace(/ /g, '-'))
  })
  data.product_url = `/${this.cartItem.campaign_slug}/?${params}`
  this.correctVariantPrice(data)
  this.$store.commit('cart/UPDATE_CART_ITEM', { index: this.index, data })
  this.setDynamicBaseCost()
}

function updateQuantity (quantity) {
  quantity = Number(quantity)
  if (quantity === 0) {
    this.isShowConfirmDeleteCartItem = true
  }
  if (!quantity || quantity < 1) {
    quantity = 1
  }
  this.$store.commit('cart/UPDATE_CART_ITEM', { index: this.index, data: { quantity, isCheckQuantity: false } })
}

function duplicateCartItem () {
  this.$store.commit('cart/DUPLICATE_CART_ITEM', this.index)
}

function removeCartItem () {
  this.$emit('remove-item', this.cartItem)
  this.$store.commit('cart/REMOVE_CART_ITEM', this.index)
}

function extraCustomFee () {
  return this.cartItem && this.cartItem.extra_custom_fee ? this.cartItem.extra_custom_fee : 0
}

function setDynamicBaseCost () {
  try {
    const dynamicBaseCost = this.$getDynamicBaseCostIndex({
      currentCampaign: this.campaign,
      currentOption: this.cartItem.options,
      optionList: this.optionsList,
      variants: this.variantsProduct,
      variantsCurrency: this.product.currency_code
    })
    this.dynamicBaseCostIndex = dynamicBaseCost
    this.$store.commit('cart/UPDATE_CART_ITEM_BY_ID', {
      cartItemId: this.cartItem.cartItemId,
      data: {
        dynamic_base_index: dynamicBaseCost
      }
    })
  } catch (e) {
  }
}
